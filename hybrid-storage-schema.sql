-- Hybrid Storage Architecture Database Schema for Yendor Cats Gallery
-- Stores only metadata, with Backblaze B2 URLs for actual images

-- Core image metadata table
CREATE TABLE CatGalleryImages (
    Id BIGINT PRIMARY KEY IDENTITY(1,1),
    
    -- File identification
    B2Key NVARCHAR(500) NOT NULL UNIQUE, -- Backblaze B2 object key
    OriginalFileName NVARCHAR(255) NOT NULL,
    FileSize BIGINT NOT NULL,
    ContentType NVARCHAR(100) NOT NULL DEFAULT 'image/jpeg',
    
    -- Image properties
    Width INT,
    Height INT,
    AspectRatio AS (CAST(Width AS FLOAT) / NULLIF(Height, 0)) PERSISTED,
    
    -- Cat metadata
    CatName NVARCHAR(100),
    CatId NVARCHAR(50), -- Links to cat profiles
    Category NVARCHAR(50) NOT NULL, -- studs, queens, kittens, gallery
    
    -- Descriptive metadata
    Title NVARCHAR(200),
    Description NVARCHAR(1000),
    Tags NVARCHAR(500), -- Comma-separated for simple searching
    
    -- Cat-specific details
    AgeAtPhoto NVARCHAR(50), -- "6 months", "2 years", etc.
    Breed NVARCHAR(100) DEFAULT 'Maine Coon',
    Bloodline NVARCHAR(100),
    Gender CHAR(1) CHECK (Gender IN ('M', 'F')),
    
    -- Timestamps
    DateTaken DATETIME2,
    DateUploaded DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    DateModified DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    -- Status and visibility
    IsActive BIT NOT NULL DEFAULT 1,
    IsPublic BIT NOT NULL DEFAULT 1,
    SortOrder INT DEFAULT 0,
    
    -- Backblaze B2 specific
    B2FileId NVARCHAR(100), -- B2 file ID for direct operations
    B2BucketName NVARCHAR(100) NOT NULL,
    B2PublicUrl AS (
        'https://f002.backblazeb2.com/file/' + B2BucketName + '/' + B2Key
    ) PERSISTED,
    
    -- Caching and performance
    ThumbnailB2Key NVARCHAR(500), -- Pre-generated thumbnail
    LastAccessedAt DATETIME2,
    AccessCount INT DEFAULT 0,
    
    -- Audit fields
    CreatedBy NVARCHAR(100),
    ModifiedBy NVARCHAR(100)
);

-- Performance indexes
CREATE INDEX IX_CatGalleryImages_Category_DateTaken 
ON CatGalleryImages(Category, DateTaken DESC) 
INCLUDE (CatName, B2Key, B2PublicUrl);

CREATE INDEX IX_CatGalleryImages_CatId_Category 
ON CatGalleryImages(CatId, Category) 
INCLUDE (DateTaken, B2PublicUrl);

CREATE INDEX IX_CatGalleryImages_IsActive_IsPublic 
ON CatGalleryImages(IsActive, IsPublic) 
WHERE IsActive = 1 AND IsPublic = 1;

CREATE INDEX IX_CatGalleryImages_Tags 
ON CatGalleryImages(Tags) 
WHERE Tags IS NOT NULL;

-- Full-text search index for descriptions and tags
CREATE FULLTEXT CATALOG YendorCatsFullText;
CREATE FULLTEXT INDEX ON CatGalleryImages(Description, Tags) 
KEY INDEX PK__CatGalle__3214EC0743D61337;

-- Cat profiles table (links to images)
CREATE TABLE CatProfiles (
    Id INT PRIMARY KEY IDENTITY(1,1),
    CatId NVARCHAR(50) NOT NULL UNIQUE,
    CatName NVARCHAR(100) NOT NULL,
    Breed NVARCHAR(100) DEFAULT 'Maine Coon',
    Bloodline NVARCHAR(100),
    Gender CHAR(1) CHECK (Gender IN ('M', 'F')),
    BirthDate DATE,
    BreedingStatus NVARCHAR(50),
    
    -- Pedigree information
    FatherId NVARCHAR(50),
    MotherId NVARCHAR(50),
    
    -- Show information
    ChampionTitles NVARCHAR(500),
    RegistrationNumber NVARCHAR(100),
    RegisteredName NVARCHAR(200),
    
    -- Profile image
    ProfileImageB2Key NVARCHAR(500),
    
    -- Status
    IsActive BIT NOT NULL DEFAULT 1,
    
    -- Timestamps
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ModifiedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    -- Foreign key constraints
    FOREIGN KEY (FatherId) REFERENCES CatProfiles(CatId),
    FOREIGN KEY (MotherId) REFERENCES CatProfiles(CatId)
);

-- Link images to cat profiles
ALTER TABLE CatGalleryImages 
ADD CONSTRAINT FK_CatGalleryImages_CatProfiles 
FOREIGN KEY (CatId) REFERENCES CatProfiles(CatId);

-- Sync tracking table for B2 synchronization
CREATE TABLE B2SyncLog (
    Id BIGINT PRIMARY KEY IDENTITY(1,1),
    B2Key NVARCHAR(500) NOT NULL,
    Operation NVARCHAR(50) NOT NULL, -- INSERT, UPDATE, DELETE, VERIFY
    Status NVARCHAR(50) NOT NULL, -- SUCCESS, FAILED, PENDING
    ErrorMessage NVARCHAR(1000),
    SyncedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    INDEX IX_B2SyncLog_Status_SyncedAt (Status, SyncedAt)
);

-- View for public gallery (optimized for frontend)
CREATE VIEW PublicGalleryView AS
SELECT 
    Id,
    B2Key,
    B2PublicUrl,
    CatName,
    CatId,
    Category,
    Title,
    Description,
    AgeAtPhoto,
    Breed,
    Bloodline,
    Gender,
    DateTaken,
    DateUploaded,
    SortOrder,
    ThumbnailB2Key,
    Width,
    Height,
    AspectRatio
FROM CatGalleryImages 
WHERE IsActive = 1 AND IsPublic = 1;

-- Stored procedure for efficient category queries
CREATE PROCEDURE GetCategoryImages
    @Category NVARCHAR(50),
    @PageNumber INT = 1,
    @PageSize INT = 20,
    @SortBy NVARCHAR(50) = 'DateTaken',
    @SortDirection NVARCHAR(4) = 'DESC'
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    DECLARE @SQL NVARCHAR(MAX);
    
    SET @SQL = N'
    SELECT 
        Id, B2Key, B2PublicUrl, CatName, CatId, Category,
        Title, Description, AgeAtPhoto, Breed, Bloodline,
        Gender, DateTaken, DateUploaded, SortOrder,
        ThumbnailB2Key, Width, Height, AspectRatio,
        COUNT(*) OVER() as TotalCount
    FROM PublicGalleryView 
    WHERE Category = @Category
    ORDER BY ' + QUOTENAME(@SortBy) + ' ' + @SortDirection + '
    OFFSET @Offset ROWS 
    FETCH NEXT @PageSize ROWS ONLY';
    
    EXEC sp_executesql @SQL, 
        N'@Category NVARCHAR(50), @Offset INT, @PageSize INT',
        @Category, @Offset, @PageSize;
END;
