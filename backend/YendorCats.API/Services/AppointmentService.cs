using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Implementation of appointment management service
    /// </summary>
    public class AppointmentService : IAppointmentService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AppointmentService> _logger;
        // In the future, we can inject an email service for sending reminders
        // private readonly IEmailService _emailService;

        /// <summary>
        /// Constructor for appointment service
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="logger">Logger</param>
        public AppointmentService(AppDbContext context, ILogger<AppointmentService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<AppointmentResponse>> GetAllAppointmentsAsync(bool includeCompleted = true, bool includeCancelled = false)
        {
            _logger.LogInformation("Getting all appointments. Include completed: {IncludeCompleted}, Include cancelled: {IncludeCancelled}",
                includeCompleted, includeCancelled);

            var query = _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .AsQueryable();

            if (!includeCompleted)
            {
                query = query.Where(a => a.Status != AppointmentStatus.Completed);
            }

            if (!includeCancelled)
            {
                query = query.Where(a => a.Status != AppointmentStatus.Cancelled);
            }

            var appointments = await query
                .OrderBy(a => a.StartTime)
                .Select(a => MapToAppointmentResponse(a))
                .ToListAsync();

            return appointments;
        }

        /// <inheritdoc />
        public async Task<AppointmentResponse?> GetAppointmentByIdAsync(int id)
        {
            _logger.LogInformation("Getting appointment by ID: {Id}", id);

            var appointment = await _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null)
            {
                _logger.LogWarning("Appointment not found: {Id}", id);
                return null;
            }

            return MapToAppointmentResponse(appointment);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<AppointmentResponse>> GetAppointmentsByClientIdAsync(int clientId, bool includeCompleted = true, bool includeCancelled = false)
        {
            _logger.LogInformation("Getting appointments for client ID: {ClientId}. Include completed: {IncludeCompleted}, Include cancelled: {IncludeCancelled}",
                clientId, includeCompleted, includeCancelled);

            var query = _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .Where(a => a.ClientId == clientId)
                .AsQueryable();

            if (!includeCompleted)
            {
                query = query.Where(a => a.Status != AppointmentStatus.Completed);
            }

            if (!includeCancelled)
            {
                query = query.Where(a => a.Status != AppointmentStatus.Cancelled);
            }

            var appointments = await query
                .OrderBy(a => a.StartTime)
                .Select(a => MapToAppointmentResponse(a))
                .ToListAsync();

            return appointments;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<AppointmentResponse>> GetAppointmentsByDateRangeAsync(DateTime startDate, DateTime endDate, bool includeCompleted = true, bool includeCancelled = false)
        {
            _logger.LogInformation("Getting appointments between {StartDate} and {EndDate}. Include completed: {IncludeCompleted}, Include cancelled: {IncludeCancelled}",
                startDate, endDate, includeCompleted, includeCancelled);

            var query = _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .Where(a => a.StartTime >= startDate && a.EndTime <= endDate)
                .AsQueryable();

            if (!includeCompleted)
            {
                query = query.Where(a => a.Status != AppointmentStatus.Completed);
            }

            if (!includeCancelled)
            {
                query = query.Where(a => a.Status != AppointmentStatus.Cancelled);
            }

            var appointments = await query
                .OrderBy(a => a.StartTime)
                .Select(a => MapToAppointmentResponse(a))
                .ToListAsync();

            return appointments;
        }

        /// <inheritdoc />
        public async Task<AppointmentResponse> CreateAppointmentAsync(CreateAppointmentRequest request, int createdById)
        {
            _logger.LogInformation("Creating new appointment for client: {ClientId}", request.ClientId);

            // Validate client exists
            var client = await _context.Clients.FindAsync(request.ClientId);
            if (client == null)
            {
                _logger.LogWarning("Client not found: {ClientId}", request.ClientId);
                throw new KeyNotFoundException($"Client with ID {request.ClientId} not found");
            }

            // Validate cat exists if provided
            if (request.CatId.HasValue)
            {
                var cat = await _context.Cats.FindAsync(request.CatId.Value);
                if (cat == null)
                {
                    _logger.LogWarning("Cat not found: {CatId}", request.CatId.Value);
                    throw new KeyNotFoundException($"Cat with ID {request.CatId.Value} not found");
                }
            }

            // Validate appointment times
            if (request.EndTime <= request.StartTime)
            {
                _logger.LogWarning("Invalid appointment times: End time must be after start time");
                throw new ArgumentException("End time must be after start time");
            }

            // Check for conflicting appointments
            var conflictingAppointment = await _context.Appointments
                .Where(a => a.Status != AppointmentStatus.Cancelled &&
                           ((a.StartTime <= request.StartTime && a.EndTime > request.StartTime) ||
                            (a.StartTime < request.EndTime && a.EndTime >= request.EndTime) ||
                            (a.StartTime >= request.StartTime && a.EndTime <= request.EndTime)))
                .FirstOrDefaultAsync();

            if (conflictingAppointment != null)
            {
                _logger.LogWarning("Conflicting appointment found: {ConflictingId}", conflictingAppointment.Id);
                throw new InvalidOperationException($"Appointment conflicts with existing appointment at {conflictingAppointment.StartTime} - {conflictingAppointment.EndTime}");
            }

            var appointment = new Appointment
            {
                Title = request.Title,
                Description = request.Description,
                ClientId = request.ClientId,
                StartTime = request.StartTime,
                EndTime = request.EndTime,
                Location = request.Location,
                Status = AppointmentStatus.Scheduled,
                Notes = request.Notes,
                CreatedById = createdById,
                CreatedAt = DateTime.UtcNow,
                CatId = request.CatId
            };

            _context.Appointments.Add(appointment);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Appointment created successfully: {Id}", appointment.Id);

            // Load related entities for response
            await _context.Entry(appointment)
                .Reference(a => a.Client)
                .LoadAsync();

            if (appointment.CatId.HasValue)
            {
                await _context.Entry(appointment)
                    .Reference(a => a.Cat)
                    .LoadAsync();
            }

            return MapToAppointmentResponse(appointment);
        }

        /// <inheritdoc />
        public async Task<AppointmentResponse> UpdateAppointmentAsync(int id, UpdateAppointmentRequest request)
        {
            _logger.LogInformation("Updating appointment: {Id}", id);

            var appointment = await _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null)
            {
                _logger.LogWarning("Appointment not found: {Id}", id);
                throw new KeyNotFoundException($"Appointment with ID {id} not found");
            }

            // Validate client exists
            var client = await _context.Clients.FindAsync(request.ClientId);
            if (client == null)
            {
                _logger.LogWarning("Client not found: {ClientId}", request.ClientId);
                throw new KeyNotFoundException($"Client with ID {request.ClientId} not found");
            }

            // Validate cat exists if provided
            if (request.CatId.HasValue)
            {
                var cat = await _context.Cats.FindAsync(request.CatId.Value);
                if (cat == null)
                {
                    _logger.LogWarning("Cat not found: {CatId}", request.CatId.Value);
                    throw new KeyNotFoundException($"Cat with ID {request.CatId.Value} not found");
                }
            }

            // Validate appointment times
            if (request.EndTime <= request.StartTime)
            {
                _logger.LogWarning("Invalid appointment times: End time must be after start time");
                throw new ArgumentException("End time must be after start time");
            }

            // Check for conflicting appointments
            var conflictingAppointment = await _context.Appointments
                .Where(a => a.Id != id &&
                           a.Status != AppointmentStatus.Cancelled &&
                           ((a.StartTime <= request.StartTime && a.EndTime > request.StartTime) ||
                            (a.StartTime < request.EndTime && a.EndTime >= request.EndTime) ||
                            (a.StartTime >= request.StartTime && a.EndTime <= request.EndTime)))
                .FirstOrDefaultAsync();

            if (conflictingAppointment != null)
            {
                _logger.LogWarning("Conflicting appointment found: {ConflictingId}", conflictingAppointment.Id);
                throw new InvalidOperationException($"Appointment conflicts with existing appointment at {conflictingAppointment.StartTime} - {conflictingAppointment.EndTime}");
            }

            // Update appointment properties
            appointment.Title = request.Title;
            appointment.Description = request.Description;
            appointment.ClientId = request.ClientId;
            appointment.StartTime = request.StartTime;
            appointment.EndTime = request.EndTime;
            appointment.Location = request.Location;
            appointment.Status = request.Status;
            appointment.Notes = request.Notes;
            appointment.UpdatedAt = DateTime.UtcNow;
            appointment.CatId = request.CatId;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Appointment updated successfully: {Id}", appointment.Id);

            // Reload related entities for response
            await _context.Entry(appointment)
                .Reference(a => a.Client)
                .LoadAsync();

            if (appointment.CatId.HasValue)
            {
                await _context.Entry(appointment)
                    .Reference(a => a.Cat)
                    .LoadAsync();
            }

            return MapToAppointmentResponse(appointment);
        }

        /// <inheritdoc />
        public async Task<AppointmentResponse> CancelAppointmentAsync(int id, string? notes = null)
        {
            _logger.LogInformation("Cancelling appointment: {Id}", id);

            var appointment = await _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null)
            {
                _logger.LogWarning("Appointment not found: {Id}", id);
                throw new KeyNotFoundException($"Appointment with ID {id} not found");
            }

            if (appointment.Status == AppointmentStatus.Cancelled)
            {
                _logger.LogWarning("Appointment already cancelled: {Id}", id);
                throw new InvalidOperationException("Appointment is already cancelled");
            }

            // Update appointment properties
            appointment.Status = AppointmentStatus.Cancelled;
            appointment.UpdatedAt = DateTime.UtcNow;
            
            if (!string.IsNullOrWhiteSpace(notes))
            {
                appointment.Notes = string.IsNullOrEmpty(appointment.Notes)
                    ? $"Cancellation: {notes}"
                    : $"{appointment.Notes}\n\nCancellation: {notes}";
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Appointment cancelled successfully: {Id}", appointment.Id);

            return MapToAppointmentResponse(appointment);
        }

        /// <inheritdoc />
        public async Task<AppointmentResponse> CompleteAppointmentAsync(int id, string? notes = null)
        {
            _logger.LogInformation("Completing appointment: {Id}", id);

            var appointment = await _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null)
            {
                _logger.LogWarning("Appointment not found: {Id}", id);
                throw new KeyNotFoundException($"Appointment with ID {id} not found");
            }

            if (appointment.Status == AppointmentStatus.Completed)
            {
                _logger.LogWarning("Appointment already completed: {Id}", id);
                throw new InvalidOperationException("Appointment is already completed");
            }

            if (appointment.Status == AppointmentStatus.Cancelled)
            {
                _logger.LogWarning("Cannot complete cancelled appointment: {Id}", id);
                throw new InvalidOperationException("Cannot complete a cancelled appointment");
            }

            // Update appointment properties
            appointment.Status = AppointmentStatus.Completed;
            appointment.UpdatedAt = DateTime.UtcNow;
            
            if (!string.IsNullOrWhiteSpace(notes))
            {
                appointment.Notes = string.IsNullOrEmpty(appointment.Notes)
                    ? $"Completion: {notes}"
                    : $"{appointment.Notes}\n\nCompletion: {notes}";
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Appointment completed successfully: {Id}", appointment.Id);

            return MapToAppointmentResponse(appointment);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<AppointmentResponse>> GetUpcomingAppointmentsAsync(int days = 7)
        {
            _logger.LogInformation("Getting upcoming appointments for the next {Days} days", days);

            var startDate = DateTime.UtcNow;
            var endDate = startDate.AddDays(days);

            var appointments = await _context.Appointments
                .Include(a => a.Client)
                .Include(a => a.Cat)
                .Where(a => a.StartTime >= startDate && 
                           a.StartTime <= endDate && 
                           a.Status != AppointmentStatus.Cancelled &&
                           a.Status != AppointmentStatus.Completed)
                .OrderBy(a => a.StartTime)
                .ToListAsync();

            return appointments.Select(a => MapToAppointmentResponse(a));
        }

        /// <inheritdoc />
        public async Task<int> SendAppointmentRemindersAsync(int days = 1)
        {
            _logger.LogInformation("Sending reminders for appointments in the next {Days} days", days);

            var startDate = DateTime.UtcNow;
            var endDate = startDate.AddDays(days);

            var appointments = await _context.Appointments
                .Include(a => a.Client)
                .Where(a => a.StartTime >= startDate && 
                           a.StartTime <= endDate && 
                           a.Status != AppointmentStatus.Cancelled &&
                           a.Status != AppointmentStatus.Completed &&
                           !a.ReminderSent)
                .ToListAsync();

            if (appointments.Count == 0)
            {
                _logger.LogInformation("No upcoming appointments found requiring reminders");
                return 0;
            }

            int remindersSent = 0;

            foreach (var appointment in appointments)
            {
                try
                {
                    // In the future, we would send an actual email here
                    // For now, we'll just mark it as sent
                    _logger.LogInformation("Would send reminder for appointment {Id} to {ClientName} ({ClientEmail}) at {AppointmentTime}",
                        appointment.Id,
                        appointment.Client?.FirstName + " " + appointment.Client?.LastName,
                        appointment.Client?.Email,
                        appointment.StartTime);

                    // Update reminder status
                    appointment.ReminderSent = true;
                    appointment.ReminderSentAt = DateTime.UtcNow;
                    
                    remindersSent++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send reminder for appointment {Id}", appointment.Id);
                }
            }

            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Sent {Count} appointment reminders", remindersSent);
            
            return remindersSent;
        }

        /// <summary>
        /// Maps an Appointment entity to an AppointmentResponse model
        /// </summary>
        /// <param name="appointment">The appointment entity</param>
        /// <returns>Appointment response model</returns>
        private static AppointmentResponse MapToAppointmentResponse(Appointment appointment)
        {
            var response = new AppointmentResponse
            {
                Id = appointment.Id,
                Title = appointment.Title,
                Description = appointment.Description,
                ClientId = appointment.ClientId,
                StartTime = appointment.StartTime,
                EndTime = appointment.EndTime,
                Location = appointment.Location,
                Status = appointment.Status,
                Notes = appointment.Notes,
                ReminderSent = appointment.ReminderSent,
                ReminderSentAt = appointment.ReminderSentAt,
                CreatedAt = appointment.CreatedAt,
                UpdatedAt = appointment.UpdatedAt,
                CatId = appointment.CatId
            };

            if (appointment.Client != null)
            {
                response.Client = new ClientBasicInfo
                {
                    Id = appointment.Client.Id,
                    FullName = $"{appointment.Client.FirstName} {appointment.Client.LastName}",
                    Email = appointment.Client.Email,
                    Phone = appointment.Client.Phone
                };
            }

            if (appointment.Cat != null)
            {
                response.Cat = new CatBasicInfo
                {
                    Id = appointment.Cat.Id,
                    Name = appointment.Cat.Name,
                    Breed = appointment.Cat.Breed
                };
            }

            return response;
        }
    }
}
