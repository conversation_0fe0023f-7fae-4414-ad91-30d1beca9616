using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Comprehensive database seeding service for YendorCats application
    /// Handles admin user creation, sample data seeding, and database initialization
    /// </summary>
    public interface IDatabaseSeedingService
    {
        /// <summary>
        /// Initialize the database with essential data (admin user, etc.)
        /// </summary>
        Task<DatabaseSeedingResult> InitializeDatabaseAsync();
        
        /// <summary>
        /// Seed sample data for development/testing
        /// </summary>
        Task<DatabaseSeedingResult> SeedSampleDataAsync(SeedingOptions? options = null);
        
        /// <summary>
        /// Check if database needs seeding
        /// </summary>
        Task<bool> NeedsSeedingAsync();
        
        /// <summary>
        /// Get seeding status information
        /// </summary>
        Task<SeedingStatus> GetSeedingStatusAsync();
    }

    public class DatabaseSeedingService : IDatabaseSeedingService
    {
        private readonly AppDbContext _context;
        private readonly IAdminAuthService _adminAuthService;
        private readonly SampleDataGenerator _sampleDataGenerator;
        private readonly ILogger<DatabaseSeedingService> _logger;
        private readonly IConfiguration _configuration;

        public DatabaseSeedingService(
            AppDbContext context,
            IAdminAuthService adminAuthService,
            SampleDataGenerator sampleDataGenerator,
            ILogger<DatabaseSeedingService> logger,
            IConfiguration configuration)
        {
            _context = context;
            _adminAuthService = adminAuthService;
            _sampleDataGenerator = sampleDataGenerator;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<DatabaseSeedingResult> InitializeDatabaseAsync()
        {
            var result = new DatabaseSeedingResult
            {
                StartTime = DateTime.UtcNow,
                OperationType = "Database Initialization"
            };

            try
            {
                _logger.LogInformation("Starting database initialization...");

                // Ensure database exists and is up to date
                await EnsureDatabaseExistsAsync();

                // Initialize admin user
                var adminCreated = await InitializeAdminUserAsync();
                result.AdminUserCreated = adminCreated;

                // Initialize essential data (if any)
                await InitializeEssentialDataAsync();

                result.Success = true;
                result.Message = "Database initialization completed successfully";
                
                _logger.LogInformation("Database initialization completed successfully");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Database initialization failed: {ex.Message}";
                result.Error = ex;
                
                _logger.LogError(ex, "Database initialization failed");
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        public async Task<DatabaseSeedingResult> SeedSampleDataAsync(SeedingOptions? options = null)
        {
            options ??= new SeedingOptions();
            
            var result = new DatabaseSeedingResult
            {
                StartTime = DateTime.UtcNow,
                OperationType = "Sample Data Seeding"
            };

            try
            {
                _logger.LogInformation("Starting sample data seeding with options: {@Options}", options);

                // Check if sample data already exists
                if (!options.ForceReseed && await HasSampleDataAsync())
                {
                    result.Success = true;
                    result.Message = "Sample data already exists, skipping seeding";
                    result.SampleDataGenerated = false;
                    return result;
                }

                // Clear existing sample data if force reseeding
                if (options.ForceReseed)
                {
                    await ClearSampleDataAsync();
                }

                // Generate sample data using existing generator
                var sampleStats = await _sampleDataGenerator.GenerateCompleteDatasetAsync(
                    options.ProfileCount,
                    options.ImagesPerProfile,
                    options.EnableB2Sync);

                result.Success = sampleStats.Success;
                result.SampleDataGenerated = sampleStats.Success;
                result.ProfilesCreated = sampleStats.ProfilesGenerated;
                result.ImagesCreated = sampleStats.ImagesGenerated;
                result.SyncLogsCreated = sampleStats.SyncLogsGenerated;
                result.Message = sampleStats.Success 
                    ? "Sample data seeding completed successfully"
                    : "Sample data seeding failed";

                _logger.LogInformation("Sample data seeding completed: {Success}, Profiles: {Profiles}, Images: {Images}",
                    result.Success, result.ProfilesCreated, result.ImagesCreated);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Sample data seeding failed: {ex.Message}";
                result.Error = ex;
                
                _logger.LogError(ex, "Sample data seeding failed");
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        public async Task<bool> NeedsSeedingAsync()
        {
            try
            {
                // Check if admin users exist
                var adminCount = await _context.AdminUsers.CountAsync();
                if (adminCount == 0)
                {
                    return true;
                }

                // Check if essential data is missing (add more checks as needed)
                // For now, we only check admin users
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if database needs seeding");
                return true; // Assume seeding is needed if we can't check
            }
        }

        public async Task<SeedingStatus> GetSeedingStatusAsync()
        {
            var status = new SeedingStatus();

            try
            {
                status.DatabaseExists = await _context.Database.CanConnectAsync();
                status.AdminUsersCount = await _context.AdminUsers.CountAsync();
                status.CatProfilesCount = await _context.CatProfiles.CountAsync();
                status.GalleryImagesCount = await _context.CatGalleryImages.CountAsync();
                status.HasSampleData = status.CatProfilesCount > 0;
                status.LastChecked = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting seeding status");
                status.Error = ex.Message;
            }

            return status;
        }

        private async Task EnsureDatabaseExistsAsync()
        {
            _logger.LogInformation("Ensuring database exists and is up to date...");
            
            if (_context.Database.IsInMemory())
            {
                await _context.Database.EnsureCreatedAsync();
                _logger.LogInformation("In-memory database created");
            }
            else
            {
                await _context.Database.MigrateAsync();
                _logger.LogInformation("Database migrations applied");
            }
        }

        private async Task<bool> InitializeAdminUserAsync()
        {
            _logger.LogInformation("Initializing admin user...");
            
            try
            {
                var adminCreated = await _adminAuthService.InitializeDefaultAdminAsync();
                
                if (adminCreated)
                {
                    _logger.LogInformation("Default admin user created successfully");
                }
                else
                {
                    _logger.LogInformation("Admin user already exists, skipping creation");
                }
                
                return adminCreated;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize admin user");
                throw;
            }
        }

        private async Task InitializeEssentialDataAsync()
        {
            _logger.LogInformation("Initializing essential data...");
            
            // Add any essential data initialization here
            // For example: default categories, system settings, etc.
            
            await Task.CompletedTask; // Placeholder for now
        }

        private async Task<bool> HasSampleDataAsync()
        {
            var profileCount = await _context.CatProfiles.CountAsync();
            return profileCount > 0;
        }

        private async Task ClearSampleDataAsync()
        {
            _logger.LogInformation("Clearing existing sample data...");
            
            // Use the same clearing logic as SampleDataGenerator
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM B2SyncLogs");
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM CatGalleryImages");
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM CatProfiles");
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM sqlite_sequence WHERE name IN ('CatProfiles', 'CatGalleryImages', 'B2SyncLogs')");
            
            _logger.LogInformation("Sample data cleared successfully");
        }
    }

    /// <summary>
    /// Options for database seeding operations
    /// </summary>
    public class SeedingOptions
    {
        public int ProfileCount { get; set; } = 25;
        public int ImagesPerProfile { get; set; } = 3;
        public bool EnableB2Sync { get; set; } = true;
        public bool ForceReseed { get; set; } = false;
    }

    /// <summary>
    /// Result of database seeding operations
    /// </summary>
    public class DatabaseSeedingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string OperationType { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public Exception? Error { get; set; }
        
        // Admin user creation
        public bool AdminUserCreated { get; set; }
        
        // Sample data creation
        public bool SampleDataGenerated { get; set; }
        public int ProfilesCreated { get; set; }
        public int ImagesCreated { get; set; }
        public int SyncLogsCreated { get; set; }
    }

    /// <summary>
    /// Current status of database seeding
    /// </summary>
    public class SeedingStatus
    {
        public bool DatabaseExists { get; set; }
        public int AdminUsersCount { get; set; }
        public int CatProfilesCount { get; set; }
        public int GalleryImagesCount { get; set; }
        public bool HasSampleData { get; set; }
        public DateTime LastChecked { get; set; }
        public string? Error { get; set; }
    }
}
