using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Services.Migration
{
    /// <summary>
    /// Service interface for migrating S3 metadata to database with dual storage support
    /// Provides batch processing and comprehensive progress tracking
    /// </summary>
    public interface IS3ToDbMigrationService
    {
        /// <summary>
        /// Start a new migration process from S3 to database
        /// </summary>
        /// <param name="batchSize">Number of items to process per batch</param>
        /// <param name="enableB2Sync">Whether to also sync to B2 during migration</param>
        /// <param name="dryRun">Whether to run in dry-run mode without making changes</param>
        /// <returns>Migration process ID for tracking</returns>
        Task<string> StartMigrationAsync(int batchSize = 100, bool enableB2Sync = true, bool dryRun = false);
        
        /// <summary>
        /// Resume a paused or failed migration process
        /// </summary>
        /// <param name="migrationId">The migration process ID to resume</param>
        /// <returns>True if resumed successfully, false if not found or cannot resume</returns>
        Task<bool> ResumeMigrationAsync(string migrationId);
        
        /// <summary>
        /// Pause a running migration process
        /// </summary>
        /// <param name="migrationId">The migration process ID to pause</param>
        /// <returns>True if paused successfully, false if not found or cannot pause</returns>
        Task<bool> PauseMigrationAsync(string migrationId);
        
        /// <summary>
        /// Cancel a running migration process
        /// </summary>
        /// <param name="migrationId">The migration process ID to cancel</param>
        /// <returns>True if cancelled successfully, false if not found</returns>
        Task<bool> CancelMigrationAsync(string migrationId);
        
        /// <summary>
        /// Get the current status of a migration process
        /// </summary>
        /// <param name="migrationId">The migration process ID</param>
        /// <returns>Migration status information</returns>
        Task<MigrationStatus?> GetMigrationStatusAsync(string migrationId);
        
        /// <summary>
        /// Get all migration processes with their current status
        /// </summary>
        /// <returns>List of all migration processes</returns>
        Task<List<MigrationStatus>> GetAllMigrationStatusesAsync();
        
        /// <summary>
        /// Get detailed migration results for a completed migration
        /// </summary>
        /// <param name="migrationId">The migration process ID</param>
        /// <returns>Detailed migration results</returns>
        Task<MigrationResult?> GetMigrationResultAsync(string migrationId);
        
        /// <summary>
        /// Migrate a single S3 object to database
        /// </summary>
        /// <param name="s3Key">The S3 key to migrate</param>
        /// <param name="enableB2Sync">Whether to also sync to B2</param>
        /// <returns>Migration result for the single item</returns>
        Task<SingleItemMigrationResult> MigrateSingleItemAsync(string s3Key, bool enableB2Sync = true);
        
        /// <summary>
        /// Migrate a batch of S3 objects to database
        /// </summary>
        /// <param name="s3Keys">The S3 keys to migrate</param>
        /// <param name="enableB2Sync">Whether to also sync to B2</param>
        /// <returns>Migration results for the batch</returns>
        Task<BatchMigrationResult> MigrateBatchAsync(IEnumerable<string> s3Keys, bool enableB2Sync = true);
        
        /// <summary>
        /// Validate the integrity of migrated data
        /// </summary>
        /// <param name="migrationId">The migration process ID to validate</param>
        /// <returns>Validation results</returns>
        Task<ValidationResult> ValidateMigrationAsync(string migrationId);
        
        /// <summary>
        /// Get migration statistics for monitoring
        /// </summary>
        /// <returns>Migration statistics</returns>
        Task<MigrationStatistics> GetMigrationStatisticsAsync();
        
        /// <summary>
        /// Clean up completed migration processes older than specified days
        /// </summary>
        /// <param name="daysOld">Number of days old to consider for cleanup</param>
        /// <returns>Number of processes cleaned up</returns>
        Task<int> CleanupOldMigrationsAsync(int daysOld = 30);
        
        /// <summary>
        /// Estimate migration time and resource requirements
        /// </summary>
        /// <param name="batchSize">Batch size for estimation</param>
        /// <param name="enableB2Sync">Whether B2 sync is enabled</param>
        /// <returns>Migration estimation</returns>
        Task<MigrationEstimation> EstimateMigrationAsync(int batchSize = 100, bool enableB2Sync = true);
        
        /// <summary>
        /// Get migration progress for a specific migration
        /// </summary>
        /// <param name="migrationId">The migration process ID</param>
        /// <returns>Real-time progress information</returns>
        Task<MigrationProgress?> GetMigrationProgressAsync(string migrationId);
        
        /// <summary>
        /// Rollback a completed migration to S3-only state
        /// </summary>
        /// <param name="migrationId">The migration process ID to rollback</param>
        /// <returns>Rollback result</returns>
        Task<RollbackResult> RollbackMigrationAsync(string migrationId);
        
        /// <summary>
        /// Sync database records to B2 storage
        /// </summary>
        /// <param name="batchSize">Number of items to sync per batch</param>
        /// <returns>B2 sync results</returns>
        Task<B2SyncResult> SyncToB2Async(int batchSize = 100);
        
        /// <summary>
        /// Verify B2 sync integrity for all records
        /// </summary>
        /// <returns>B2 sync verification results</returns>
        Task<B2SyncVerification> VerifyB2SyncAsync();
        
        /// <summary>
        /// Get migration logs for troubleshooting
        /// </summary>
        /// <param name="migrationId">The migration process ID</param>
        /// <param name="logLevel">Minimum log level to retrieve</param>
        /// <param name="pageSize">Number of log entries per page</param>
        /// <param name="page">Page number</param>
        /// <returns>Migration logs</returns>
        Task<MigrationLogs> GetMigrationLogsAsync(string migrationId, string logLevel = "Info", int pageSize = 100, int page = 1);
    }
    
    /// <summary>
    /// Migration status information
    /// </summary>
    public class MigrationStatus
    {
        public string MigrationId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Pending, Running, Paused, Completed, Failed, Cancelled
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public DateTime? LastUpdateTime { get; set; }
        public int TotalItems { get; set; }
        public int ProcessedItems { get; set; }
        public int SuccessfulItems { get; set; }
        public int FailedItems { get; set; }
        public int SkippedItems { get; set; }
        public double ProgressPercentage { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
        public string? CurrentBatch { get; set; }
        public string? ErrorMessage { get; set; }
        public bool EnableB2Sync { get; set; }
        public bool IsDryRun { get; set; }
        public int BatchSize { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
    
    /// <summary>
    /// Single item migration result
    /// </summary>
    public class SingleItemMigrationResult
    {
        public string S3Key { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime ProcessedAt { get; set; }
        public long ProcessingTimeMs { get; set; }
        public bool B2Synced { get; set; }
        public string? B2Key { get; set; }
        public string? B2FileId { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
    
    /// <summary>
    /// Batch migration result
    /// </summary>
    public class BatchMigrationResult
    {
        public string BatchId { get; set; } = string.Empty;
        public int TotalItems { get; set; }
        public int SuccessfulItems { get; set; }
        public int FailedItems { get; set; }
        public int SkippedItems { get; set; }
        public List<SingleItemMigrationResult> ItemResults { get; set; } = new();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public long TotalProcessingTimeMs { get; set; }
        public double SuccessRate { get; set; }
        public List<string> Errors { get; set; } = new();
    }
    
    /// <summary>
    /// Validation result for migration integrity
    /// </summary>
    public class ValidationResult
    {
        public string MigrationId { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public int TotalChecked { get; set; }
        public int ValidItems { get; set; }
        public int InvalidItems { get; set; }
        public List<ValidationError> Errors { get; set; } = new();
        public DateTime ValidationTime { get; set; }
        public long ValidationTimeMs { get; set; }
        public Dictionary<string, int> ValidationStats { get; set; } = new();
    }
    
    
    /// <summary>
    /// Migration statistics for monitoring
    /// </summary>
    public class MigrationStatistics
    {
        public int TotalMigrations { get; set; }
        public int CompletedMigrations { get; set; }
        public int FailedMigrations { get; set; }
        public int RunningMigrations { get; set; }
        public int PausedMigrations { get; set; }
        public long TotalItemsMigrated { get; set; }
        public long TotalItemsFailed { get; set; }
        public double AverageSuccessRate { get; set; }
        public TimeSpan AverageProcessingTime { get; set; }
        public DateTime LastMigrationTime { get; set; }
        public Dictionary<string, int> MigrationsByStatus { get; set; } = new();
        public Dictionary<string, int> ErrorsByType { get; set; } = new();
    }
    
    /// <summary>
    /// Migration estimation results
    /// </summary>
    public class MigrationEstimation
    {
        public int TotalItems { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public long EstimatedDataSizeBytes { get; set; }
        public int EstimatedBatches { get; set; }
        public double EstimatedB2CostUsd { get; set; }
        public double EstimatedBandwidthGb { get; set; }
        public Dictionary<string, object> Recommendations { get; set; } = new();
    }
    
    /// <summary>
    /// Real-time migration progress
    /// </summary>
    public class MigrationProgress
    {
        public string MigrationId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public double ProgressPercentage { get; set; }
        public int CurrentBatch { get; set; }
        public int TotalBatches { get; set; }
        public TimeSpan Elapsed { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
        public double ItemsPerSecond { get; set; }
        public long BytesPerSecond { get; set; }
        public string? CurrentActivity { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
    }
    
    /// <summary>
    /// Migration rollback result
    /// </summary>
    public class RollbackResult
    {
        public string MigrationId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int ItemsRolledBack { get; set; }
        public int ItemsSkipped { get; set; }
        public int ItemsFailed { get; set; }
        public DateTime RollbackTime { get; set; }
        public long RollbackTimeMs { get; set; }
        public List<string> Errors { get; set; } = new();
    }
    
    /// <summary>
    /// B2 sync result
    /// </summary>
    public class B2SyncResult
    {
        public string SyncId { get; set; } = string.Empty;
        public int TotalItems { get; set; }
        public int SyncedItems { get; set; }
        public int FailedItems { get; set; }
        public int SkippedItems { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public long TotalSizeBytes { get; set; }
        public double CostUsd { get; set; }
        public List<string> Errors { get; set; } = new();
    }
    
    /// <summary>
    /// B2 sync verification result
    /// </summary>
    public class B2SyncVerification
    {
        public int TotalItems { get; set; }
        public int VerifiedItems { get; set; }
        public int MissingItems { get; set; }
        public int CorruptedItems { get; set; }
        public bool IsValid { get; set; }
        public DateTime VerificationTime { get; set; }
        public List<string> MissingFiles { get; set; } = new();
        public List<string> CorruptedFiles { get; set; } = new();
    }
    
    /// <summary>
    /// Migration logs for troubleshooting
    /// </summary>
    public class MigrationLogs
    {
        public string MigrationId { get; set; } = string.Empty;
        public int TotalLogs { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public List<MigrationLogEntry> Entries { get; set; } = new();
    }
    
    /// <summary>
    /// Individual migration log entry
    /// </summary>
    public class MigrationLogEntry
    {
        public DateTime Timestamp { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? Category { get; set; }
        public string? ItemId { get; set; }
        public string? Exception { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }
}