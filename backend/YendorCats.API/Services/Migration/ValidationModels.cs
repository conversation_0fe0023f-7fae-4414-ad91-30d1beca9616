using System;
using System.Collections.Generic;

namespace YendorCats.API.Services.Migration
{
    /// <summary>
    /// Validation options for comprehensive migration validation
    /// </summary>
    public class ValidationOptions
    {
        /// <summary>
        /// Validate database consistency and integrity
        /// </summary>
        public bool ValidateDatabase { get; set; } = true;

        /// <summary>
        /// Validate S3 storage consistency
        /// </summary>
        public bool ValidateS3Storage { get; set; } = true;

        /// <summary>
        /// Validate B2 storage consistency
        /// </summary>
        public bool ValidateB2Storage { get; set; } = true;

        /// <summary>
        /// Validate cross-storage consistency
        /// </summary>
        public bool ValidateCrossStorage { get; set; } = true;

        /// <summary>
        /// Validate data integrity using checksums and metadata
        /// </summary>
        public bool ValidateDataIntegrity { get; set; } = true;

        /// <summary>
        /// Validate performance metrics and response times
        /// </summary>
        public bool ValidatePerformance { get; set; } = true;

        /// <summary>
        /// Validate audit trail completeness
        /// </summary>
        public bool ValidateAuditTrail { get; set; } = true;

        /// <summary>
        /// Skip expensive validation operations
        /// </summary>
        public bool SkipExpensiveValidation { get; set; } = false;

        /// <summary>
        /// Maximum number of records to validate (0 = all)
        /// </summary>
        public int MaxRecordsToValidate { get; set; } = 0;

        /// <summary>
        /// Validation timeout in minutes
        /// </summary>
        public int ValidationTimeoutMinutes { get; set; } = 30;
    }

    /// <summary>
    /// Comprehensive validation result containing all validation categories
    /// </summary>
    public class ComprehensiveValidationResult
    {
        /// <summary>
        /// Migration ID being validated
        /// </summary>
        public string MigrationId { get; set; } = string.Empty;

        /// <summary>
        /// Overall validation result
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total validation duration in milliseconds
        /// </summary>
        public long ValidationDurationMs { get; set; }

        /// <summary>
        /// Validation options used
        /// </summary>
        public ValidationOptions ValidationOptions { get; set; } = new();

        /// <summary>
        /// Database validation results
        /// </summary>
        public DatabaseValidationResult? DatabaseValidation { get; set; }

        /// <summary>
        /// S3 storage validation results
        /// </summary>
        public S3ValidationResult? S3Validation { get; set; }

        /// <summary>
        /// B2 storage validation results
        /// </summary>
        public B2ValidationResult? B2Validation { get; set; }

        /// <summary>
        /// Cross-storage validation results
        /// </summary>
        public CrossStorageValidationResult? CrossStorageValidation { get; set; }

        /// <summary>
        /// Data integrity validation results
        /// </summary>
        public DataIntegrityValidationResult? DataIntegrityValidation { get; set; }

        /// <summary>
        /// Performance validation results
        /// </summary>
        public PerformanceValidationResult? PerformanceValidation { get; set; }

        /// <summary>
        /// Audit trail validation results
        /// </summary>
        public AuditTrailValidationResult? AuditTrailValidation { get; set; }

        /// <summary>
        /// Global validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;

        /// <summary>
        /// Validation recommendations
        /// </summary>
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// Database validation result
    /// </summary>
    public class DatabaseValidationResult
    {
        /// <summary>
        /// Whether database validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total records validated
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Valid records count
        /// </summary>
        public int ValidRecords { get; set; }

        /// <summary>
        /// Invalid records count
        /// </summary>
        public int InvalidRecords { get; set; }

        /// <summary>
        /// Database validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Index validation results
        /// </summary>
        public string IndexValidation { get; set; } = string.Empty;

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// S3 storage validation result
    /// </summary>
    public class S3ValidationResult
    {
        /// <summary>
        /// Whether S3 validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total S3 records validated
        /// </summary>
        public int TotalS3Records { get; set; }

        /// <summary>
        /// Valid S3 records count
        /// </summary>
        public int ValidS3Records { get; set; }

        /// <summary>
        /// Invalid S3 records count
        /// </summary>
        public int InvalidS3Records { get; set; }

        /// <summary>
        /// Number of orphaned S3 objects
        /// </summary>
        public int OrphanedS3Objects { get; set; }

        /// <summary>
        /// S3 validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// B2 storage validation result
    /// </summary>
    public class B2ValidationResult
    {
        /// <summary>
        /// Whether B2 validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total B2 records validated
        /// </summary>
        public int TotalB2Records { get; set; }

        /// <summary>
        /// Valid B2 records count
        /// </summary>
        public int ValidB2Records { get; set; }

        /// <summary>
        /// Invalid B2 records count
        /// </summary>
        public int InvalidB2Records { get; set; }

        /// <summary>
        /// Number of orphaned B2 objects
        /// </summary>
        public int OrphanedB2Objects { get; set; }

        /// <summary>
        /// B2 validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Cross-storage validation result
    /// </summary>
    public class CrossStorageValidationResult
    {
        /// <summary>
        /// Whether cross-storage validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total records validated
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Valid records count
        /// </summary>
        public int ValidRecords { get; set; }

        /// <summary>
        /// Invalid records count
        /// </summary>
        public int InvalidRecords { get; set; }

        /// <summary>
        /// Cross-storage validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Data integrity validation result
    /// </summary>
    public class DataIntegrityValidationResult
    {
        /// <summary>
        /// Whether data integrity validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total records validated
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Valid records count
        /// </summary>
        public int ValidRecords { get; set; }

        /// <summary>
        /// Invalid records count
        /// </summary>
        public int InvalidRecords { get; set; }

        /// <summary>
        /// Data integrity validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performance validation result
    /// </summary>
    public class PerformanceValidationResult
    {
        /// <summary>
        /// Whether performance validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// List of performance tests executed
        /// </summary>
        public List<PerformanceTest> PerformanceTests { get; set; } = new();

        /// <summary>
        /// Average response time across all tests
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// Maximum response time recorded
        /// </summary>
        public long MaxResponseTime { get; set; }

        /// <summary>
        /// Minimum response time recorded
        /// </summary>
        public long MinResponseTime { get; set; }

        /// <summary>
        /// Performance validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Audit trail validation result
    /// </summary>
    public class AuditTrailValidationResult
    {
        /// <summary>
        /// Migration ID being validated
        /// </summary>
        public string MigrationId { get; set; } = string.Empty;

        /// <summary>
        /// Whether audit trail validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Total audit records validated
        /// </summary>
        public int TotalAuditRecords { get; set; }

        /// <summary>
        /// Valid audit records count
        /// </summary>
        public int ValidAuditRecords { get; set; }

        /// <summary>
        /// Invalid audit records count
        /// </summary>
        public int InvalidAuditRecords { get; set; }

        /// <summary>
        /// Audit trail validation errors
        /// </summary>
        public List<ValidationError> ValidationErrors { get; set; } = new();

        /// <summary>
        /// Validation summary
        /// </summary>
        public string ValidationSummary { get; set; } = string.Empty;
    }

    /// <summary>
    /// Performance test result
    /// </summary>
    public class PerformanceTest
    {
        /// <summary>
        /// Test name
        /// </summary>
        public string TestName { get; set; } = string.Empty;

        /// <summary>
        /// Test description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Response time in milliseconds
        /// </summary>
        public long ResponseTimeMs { get; set; }

        /// <summary>
        /// Whether the test passed performance criteria
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Performance threshold used
        /// </summary>
        public long ThresholdMs { get; set; }

        /// <summary>
        /// Test execution timestamp
        /// </summary>
        public DateTime ExecutionTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Additional test metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Validation error details
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// Item identifier related to the error
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Error type category
        /// </summary>
        public string ErrorType { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Detailed error information
        /// </summary>
        public string ErrorDetails { get; set; } = string.Empty;

        /// <summary>
        /// Error severity level
        /// </summary>
        public ValidationErrorSeverity Severity { get; set; } = ValidationErrorSeverity.Error;

        /// <summary>
        /// Error timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Additional error context
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        // Legacy/Compatibility Properties (for backward compatibility with existing code)
        /// <summary>
        /// S3 key - alias for ItemId when dealing with S3 objects
        /// </summary>
        public string? S3Key
        {
            get => Context.ContainsKey("S3Key") ? Context["S3Key"]?.ToString() : ItemId;
            set
            {
                Context["S3Key"] = value ?? string.Empty;
                if (string.IsNullOrEmpty(ItemId))
                    ItemId = value ?? string.Empty;
            }
        }

        /// <summary>
        /// B2 key - alias for ItemId when dealing with B2 objects
        /// </summary>
        public string? B2Key
        {
            get => Context.ContainsKey("B2Key") ? Context["B2Key"]?.ToString() : ItemId;
            set
            {
                Context["B2Key"] = value ?? string.Empty;
                if (string.IsNullOrEmpty(ItemId))
                    ItemId = value ?? string.Empty;
            }
        }

        /// <summary>
        /// Details - alias for ErrorDetails
        /// </summary>
        public string Details
        {
            get => ErrorDetails;
            set => ErrorDetails = value;
        }
    }

    /// <summary>
    /// Validation error severity levels
    /// </summary>
    public enum ValidationErrorSeverity
    {
        /// <summary>
        /// Informational message
        /// </summary>
        Info,

        /// <summary>
        /// Warning that should be addressed
        /// </summary>
        Warning,

        /// <summary>
        /// Error that affects functionality
        /// </summary>
        Error,

        /// <summary>
        /// Critical error that prevents operation
        /// </summary>
        Critical
    }
}