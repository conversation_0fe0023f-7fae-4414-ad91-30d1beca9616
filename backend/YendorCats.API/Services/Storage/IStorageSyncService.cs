using YendorCats.API.Models;
using YendorCats.API.Services.B2Sync;

namespace YendorCats.API.Services.Storage
{
    /// <summary>
    /// Generic storage synchronization service for both S3 and B2
    /// Maintains compatibility with existing S3 workflows while adding B2 support
    /// </summary>
    public interface IStorageSyncService
    {
        // Generic sync operations (works with both S3 and B2)
        /// <summary>
        /// Sync image upload to database (auto-detects storage provider)
        /// </summary>
        Task<SyncResult> SyncImageUploadAsync(string storageKey, string bucketName, 
            StorageProvider provider, string? fileId = null, Dictionary<string, string>? metadata = null);
        
        /// <summary>
        /// Sync image deletion from database
        /// </summary>
        Task<SyncResult> SyncImageDeletionAsync(string storageKey, StorageProvider provider);
        
        /// <summary>
        /// Sync metadata update for existing image
        /// </summary>
        Task<SyncResult> SyncMetadataUpdateAsync(string storageKey, StorageProvider provider, 
            Dictionary<string, string> metadata);
        
        // S3-specific operations (for backward compatibility)
        /// <summary>
        /// Sync S3 image upload (maintains existing S3 workflow)
        /// </summary>
        Task<SyncResult> SyncS3ImageUploadAsync(string s3Key, string s3BucketName, 
            Dictionary<string, string>? metadata = null);
        
        /// <summary>
        /// Sync S3 image deletion
        /// </summary>
        Task<SyncResult> SyncS3ImageDeletionAsync(string s3Key);
        
        // B2-specific operations
        /// <summary>
        /// Sync B2 image upload
        /// </summary>
        Task<SyncResult> SyncB2ImageUploadAsync(string b2Key, string b2BucketName, 
            string? b2FileId = null, Dictionary<string, string>? metadata = null);
        
        /// <summary>
        /// Sync B2 image deletion
        /// </summary>
        Task<SyncResult> SyncB2ImageDeletionAsync(string b2Key);
        
        // Migration operations
        /// <summary>
        /// Migrate image from S3 to B2
        /// </summary>
        Task<MigrationResult> MigrateImageS3ToB2Async(long imageId, string newB2Key, 
            string newB2BucketName, string? b2FileId = null);
        
        /// <summary>
        /// Migrate image from B2 to S3
        /// </summary>
        Task<MigrationResult> MigrateImageB2ToS3Async(long imageId, string newS3Key, 
            string newS3BucketName);
        
        /// <summary>
        /// Batch migrate images between storage providers
        /// </summary>
        Task<BatchMigrationResult> BatchMigrateAsync(IEnumerable<MigrationRequest> requests);
        
        // Verification operations
        /// <summary>
        /// Verify consistency across both storage providers
        /// </summary>
        Task<VerificationResult> VerifyConsistencyAsync(StorageProvider? provider = null, 
            string? category = null, int batchSize = 100);
        
        // Health and monitoring
        /// <summary>
        /// Get sync status for all storage providers
        /// </summary>
        Task<StorageSyncStatus> GetSyncStatusAsync();
        
        /// <summary>
        /// Get health check for storage sync service
        /// </summary>
        Task<StorageSyncHealthCheck> GetHealthCheckAsync();
    }
    
    /// <summary>
    /// Storage provider enumeration
    /// </summary>
    public enum StorageProvider
    {
        S3,
        B2
    }
    
    /// <summary>
    /// Migration request for batch operations
    /// </summary>
    public class MigrationRequest
    {
        public long ImageId { get; set; }
        public StorageProvider FromProvider { get; set; }
        public StorageProvider ToProvider { get; set; }
        public string NewStorageKey { get; set; } = string.Empty;
        public string NewBucketName { get; set; } = string.Empty;
        public string? NewFileId { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
    }
    
    /// <summary>
    /// Migration result
    /// </summary>
    public class MigrationResult
    {
        public bool Success { get; set; }
        public long ImageId { get; set; }
        public StorageProvider FromProvider { get; set; }
        public StorageProvider ToProvider { get; set; }
        public string? OldStorageKey { get; set; }
        public string? NewStorageKey { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool RollbackAvailable { get; set; }
    }
    
    /// <summary>
    /// Batch migration result
    /// </summary>
    public class BatchMigrationResult
    {
        public int TotalRequests { get; set; }
        public int SuccessfulMigrations { get; set; }
        public int FailedMigrations { get; set; }
        public List<MigrationResult> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan TotalDuration { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulMigrations / TotalRequests : 0;
    }
    
    /// <summary>
    /// Storage sync status for all providers
    /// </summary>
    public class StorageSyncStatus
    {
        public bool IsHealthy { get; set; }
        public Dictionary<StorageProvider, ProviderSyncStatus> ProviderStatus { get; set; } = new();
        public int TotalPendingOperations { get; set; }
        public int TotalFailedOperations { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public DateTime? LastFailedSync { get; set; }
        public string? LastError { get; set; }
        public double OverallSuccessRate { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Sync status for individual storage provider
    /// </summary>
    public class ProviderSyncStatus
    {
        public StorageProvider Provider { get; set; }
        public bool IsHealthy { get; set; }
        public int PendingOperations { get; set; }
        public int FailedOperations { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public DateTime? LastFailedSync { get; set; }
        public string? LastError { get; set; }
        public double SuccessRate { get; set; }
        public Dictionary<string, int> OperationCounts { get; set; } = new();
        public Dictionary<string, TimeSpan> AverageOperationTimes { get; set; } = new();
    }
    
    /// <summary>
    /// Health check for storage sync service
    /// </summary>
    public class StorageSyncHealthCheck
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public Dictionary<StorageProvider, bool> ProviderConnectivity { get; set; } = new();
        public Dictionary<StorageProvider, TimeSpan> ProviderResponseTimes { get; set; } = new();
        public bool DatabaseConnected { get; set; }
        public TimeSpan DatabaseResponseTime { get; set; }
        public int TotalPendingOperations { get; set; }
        public int TotalFailedOperations { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.UtcNow;
    }
}
