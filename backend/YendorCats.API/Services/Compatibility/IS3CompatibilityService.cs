using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace YendorCats.API.Services.Compatibility
{
    /// <summary>
    /// Interface for backward compatibility service for existing S3 operations
    /// Provides seamless transition from S3-only to hybrid storage architecture
    /// Maintains existing API contracts while supporting new dual storage features
    /// </summary>
    public interface IS3CompatibilityService
    {
        /// <summary>
        /// Get all images with backward compatibility for S3-only operations
        /// Transparently uses database if available, falls back to S3 scanning
        /// </summary>
        /// <returns>List of gallery images</returns>
        Task<List<LegacyGalleryImage>> GetAllImagesAsync();

        /// <summary>
        /// Get images by category with backward compatibility
        /// </summary>
        /// <param name="category">Image category</param>
        /// <returns>List of gallery images in category</returns>
        Task<List<LegacyGalleryImage>> GetImagesByCategoryAsync(string category);

        /// <summary>
        /// Get image by filename with backward compatibility
        /// </summary>
        /// <param name="filename">Image filename</param>
        /// <returns>Gallery image or null if not found</returns>
        Task<LegacyGalleryImage?> GetImageByFilenameAsync(string filename);

        /// <summary>
        /// Get image metadata with backward compatibility
        /// </summary>
        /// <param name="filename">Image filename</param>
        /// <returns>Image metadata or null if not found</returns>
        Task<LegacyImageMetadata?> GetImageMetadataAsync(string filename);

        /// <summary>
        /// Check if migration is needed for the gallery
        /// </summary>
        /// <returns>True if migration is needed, false otherwise</returns>
        Task<bool> IsMigrationNeededAsync();

        /// <summary>
        /// Start automatic migration if needed
        /// </summary>
        /// <returns>Migration ID if started, null if not needed</returns>
        Task<string?> StartMigrationIfNeededAsync();

        /// <summary>
        /// Get migration progress for backward compatibility monitoring
        /// </summary>
        /// <returns>Migration progress summary</returns>
        Task<LegacyMigrationProgress?> GetMigrationProgressAsync();

        /// <summary>
        /// Get system status for backward compatibility
        /// </summary>
        /// <returns>System status information</returns>
        Task<LegacySystemStatus> GetSystemStatusAsync();
    }
}