using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Services.Compatibility
{
    /// <summary>
    /// Legacy gallery image model for backward compatibility
    /// Maintains the original S3-only structure while supporting new hybrid features
    /// </summary>
    public class LegacyGalleryImage
    {
        /// <summary>
        /// Image ID (0 for S3-only images)
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Original filename
        /// </summary>
        [Required]
        public string Filename { get; set; } = string.Empty;

        /// <summary>
        /// Full URL to the image
        /// </summary>
        [Required]
        public string ImageUrl { get; set; } = string.Empty;

        /// <summary>
        /// Cat name extracted from filename or database
        /// </summary>
        public string CatName { get; set; } = string.Empty;

        /// <summary>
        /// Image category or cat identifier
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Image description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Image width in pixels
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// Image height in pixels
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Comma-separated tags
        /// </summary>
        public string Tags { get; set; } = string.Empty;

        /// <summary>
        /// Whether the image is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether the image is featured
        /// </summary>
        public bool IsFeatured { get; set; }

        /// <summary>
        /// Number of times viewed
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// Number of likes
        /// </summary>
        public int LikeCount { get; set; }
    }

    /// <summary>
    /// Legacy image metadata model for backward compatibility
    /// Maintains the original S3 metadata structure
    /// </summary>
    public class LegacyImageMetadata
    {
        /// <summary>
        /// Original filename
        /// </summary>
        [Required]
        public string Filename { get; set; } = string.Empty;

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// Image width in pixels
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// Image height in pixels
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// Image format (JPEG, PNG, etc.)
        /// </summary>
        public string Format { get; set; } = string.Empty;

        /// <summary>
        /// MIME type
        /// </summary>
        public string MimeType { get; set; } = string.Empty;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last updated timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// S3 key for the image
        /// </summary>
        public string S3Key { get; set; } = string.Empty;

        /// <summary>
        /// Full S3 URL
        /// </summary>
        public string S3Url { get; set; } = string.Empty;

        /// <summary>
        /// EXIF data if available
        /// </summary>
        public string? ExifData { get; set; }
    }

    /// <summary>
    /// Legacy migration progress model for backward compatibility
    /// Provides simple progress tracking for existing monitoring systems
    /// </summary>
    public class LegacyMigrationProgress
    {
        /// <summary>
        /// Whether migration is currently running
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// Current migration status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Progress percentage (0-100)
        /// </summary>
        public double ProgressPercentage { get; set; }

        /// <summary>
        /// Number of items processed
        /// </summary>
        public int ProcessedItems { get; set; }

        /// <summary>
        /// Total number of items to process
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// Number of successful items
        /// </summary>
        public int SuccessfulItems { get; set; }

        /// <summary>
        /// Number of failed items
        /// </summary>
        public int FailedItems { get; set; }

        /// <summary>
        /// Migration start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Migration end time (null if running)
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Estimated time remaining
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }
    }

    /// <summary>
    /// Legacy system status model for backward compatibility
    /// Provides simple system health information
    /// </summary>
    public class LegacySystemStatus
    {
        /// <summary>
        /// Whether database is connected
        /// </summary>
        public bool DatabaseConnected { get; set; }

        /// <summary>
        /// Whether S3 is connected
        /// </summary>
        public bool S3Connected { get; set; }

        /// <summary>
        /// Whether migration is currently active
        /// </summary>
        public bool MigrationActive { get; set; }

        /// <summary>
        /// Number of images in database
        /// </summary>
        public int DatabaseImageCount { get; set; }

        /// <summary>
        /// Number of images in S3
        /// </summary>
        public int S3ImageCount { get; set; }

        /// <summary>
        /// Synchronization status
        /// </summary>
        public string SyncStatus { get; set; } = string.Empty;

        /// <summary>
        /// Last status check time
        /// </summary>
        public DateTime LastChecked { get; set; }

        /// <summary>
        /// Error message if any
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// S3 object information for compatibility
    /// </summary>
    public class S3ObjectInfo
    {
        /// <summary>
        /// S3 object key
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// Object size in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Last modified timestamp
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ETag for the object
        /// </summary>
        public string ETag { get; set; } = string.Empty;
    }

    /// <summary>
    /// S3 object metadata for compatibility
    /// </summary>
    public class S3ObjectMetadata
    {
        /// <summary>
        /// Content type
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Content length
        /// </summary>
        public long ContentLength { get; set; }

        /// <summary>
        /// Last modified timestamp
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ETag for the object
        /// </summary>
        public string ETag { get; set; } = string.Empty;

        /// <summary>
        /// Custom metadata
        /// </summary>
        public Dictionary<string, string> UserMetadata { get; set; } = new();
    }
}