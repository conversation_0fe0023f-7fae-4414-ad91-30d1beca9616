using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for client management service
    /// </summary>
    public interface IClientService
    {
        /// <summary>
        /// Get all clients
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive clients</param>
        /// <returns>List of clients</returns>
        Task<IEnumerable<ClientResponse>> GetAllClientsAsync(bool includeInactive = false);

        /// <summary>
        /// Get client by ID
        /// </summary>
        /// <param name="id">Client ID</param>
        /// <returns>Client details</returns>
        Task<ClientResponse?> GetClientByIdAsync(int id);

        /// <summary>
        /// Create a new client
        /// </summary>
        /// <param name="request">Client creation request</param>
        /// <returns>Created client</returns>
        Task<ClientResponse> CreateClientAsync(CreateClientRequest request);

        /// <summary>
        /// Update an existing client
        /// </summary>
        /// <param name="id">Client ID</param>
        /// <param name="request">Client update request</param>
        /// <returns>Updated client</returns>
        Task<ClientResponse> UpdateClientAsync(int id, UpdateClientRequest request);

        /// <summary>
        /// Delete a client (mark as inactive)
        /// </summary>
        /// <param name="id">Client ID</param>
        /// <returns>Success flag</returns>
        Task<bool> DeleteClientAsync(int id);

        /// <summary>
        /// Search clients by name or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="includeInactive">Whether to include inactive clients</param>
        /// <returns>List of matching clients</returns>
        Task<IEnumerable<ClientResponse>> SearchClientsAsync(string searchTerm, bool includeInactive = false);
    }
}
