using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Smart image loading service that implements priority chain:
    /// B2 Direct → Local Resources → Database Cache
    /// </summary>
    public interface ISmartImageService
    {
        /// <summary>
        /// Get images for a category using smart fallback loading
        /// </summary>
        /// <param name="category">Category name (studs, queens, kittens, gallery)</param>
        /// <param name="sortBy">Sort order (date, name, random)</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of images with smart URLs</returns>
        Task<SmartImageResponse> GetCategoryImagesAsync(string category, string sortBy = "date", bool includeMetadata = false);

        /// <summary>
        /// Get images for a specific cat using smart fallback loading
        /// </summary>
        /// <param name="catName">Name of the cat</param>
        /// <param name="sortBy">Sort order (date, name, random)</param>
        /// <param name="includeMetadata">Include detailed metadata</param>
        /// <returns>List of images for the cat</returns>
        Task<SmartImageResponse> GetCatImagesAsync(string catName, string sortBy = "date", bool includeMetadata = false);

        /// <summary>
        /// Check if an image exists at the B2 URL
        /// </summary>
        /// <param name="b2Url">B2 URL to check</param>
        /// <returns>True if image exists, false otherwise</returns>
        Task<bool> CheckImageAvailabilityAsync(string b2Url);

        /// <summary>
        /// Get a single image with fallback chain
        /// </summary>
        /// <param name="category">Category name</param>
        /// <param name="catName">Cat name</param>
        /// <param name="filename">Image filename</param>
        /// <returns>Smart image response</returns>
        Task<SmartImageInfo> GetImageWithFallbackAsync(string category, string catName, string filename);

        /// <summary>
        /// Generate B2 direct URL for an image
        /// </summary>
        /// <param name="category">Category name</param>
        /// <param name="catName">Cat name</param>
        /// <param name="filename">Image filename</param>
        /// <returns>B2 direct URL</returns>
        string GenerateB2DirectUrl(string category, string catName, string filename);

        /// <summary>
        /// Generate local resource URL for an image
        /// </summary>
        /// <param name="category">Category name</param>
        /// <param name="filename">Image filename</param>
        /// <returns>Local resource URL</returns>
        string GenerateLocalResourceUrl(string category, string filename);
    }

    /// <summary>
    /// Response object for smart image loading
    /// </summary>
    public class SmartImageResponse
    {
        public string Category { get; set; } = string.Empty;
        public string? CatName { get; set; }
        public int Count { get; set; }
        public string SortBy { get; set; } = "date";
        public List<SmartImageInfo> Images { get; set; } = new();
        public string Source { get; set; } = "smart"; // smart, database, index
        public DateTime RetrievedAt { get; set; } = DateTime.UtcNow;
        public SmartLoadingStats LoadingStats { get; set; } = new();
    }

    /// <summary>
    /// Smart image information with fallback URLs
    /// </summary>
    public class SmartImageInfo
    {
        public long Id { get; set; }
        public string CatName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime DateTaken { get; set; }
        public int OrderNumber { get; set; }
        public float Age { get; set; }
        
        // Smart URL properties
        public string PrimaryUrl { get; set; } = string.Empty; // B2 direct URL
        public string FallbackUrl { get; set; } = string.Empty; // Local resource URL
        public string DatabaseUrl { get; set; } = string.Empty; // Current database URL
        public string ActiveUrl { get; set; } = string.Empty; // Currently recommended URL
        
        // Availability flags
        public bool IsAvailableOnB2 { get; set; } = true; // Assume true until proven false
        public bool IsAvailableLocally { get; set; } = false;
        public bool IsAvailableInDatabase { get; set; } = false;
        
        // Metadata (when requested)
        public string? Description { get; set; }
        public string? Color { get; set; }
        public string? Gender { get; set; }
        public string? Traits { get; set; }
        public string? Breed { get; set; }
        public string? Personality { get; set; }
        public string? FileFormat { get; set; }
        public long? FileSize { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
    }

    /// <summary>
    /// Statistics about smart loading performance
    /// </summary>
    public class SmartLoadingStats
    {
        public int TotalImages { get; set; }
        public int B2Available { get; set; }
        public int LocalAvailable { get; set; }
        public int DatabaseOnly { get; set; }
        public TimeSpan LoadingTime { get; set; }
        public string PreferredSource { get; set; } = "B2";
    }
}
