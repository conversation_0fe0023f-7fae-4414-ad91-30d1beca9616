using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;
using System.Diagnostics;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Services.Gallery
{
    /// <summary>
    /// High-performance gallery service implementation
    /// Multi-level caching: Memory (5min) → Distributed (30min) → Database
    /// Target: 85-90% performance improvement, sub-500ms response times
    /// </summary>
    public class GalleryService : IGalleryService
    {
        private readonly IGalleryRepository _repository;
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<GalleryService> _logger;
        
        // Cache configuration
        private readonly TimeSpan _memoryCacheExpiry = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _distributedCacheExpiry = TimeSpan.FromMinutes(30);
        private readonly TimeSpan _statsCacheExpiry = TimeSpan.FromMinutes(15);
        private const string CACHE_PREFIX = "gallery_";
        private const string STATS_CACHE_KEY = "gallery_stats";
        private const string HEALTH_CACHE_KEY = "gallery_health";
        
        // Performance tracking
        private static readonly Dictionary<string, List<TimeSpan>> _responseTimesByCategory = new();
        private static readonly Dictionary<string, int> _requestCountsByCategory = new();
        private static int _totalRequests = 0;
        private static int _cacheHits = 0;
        private static int _cacheMisses = 0;
        
        public GalleryService(
            IGalleryRepository repository,
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            ILogger<GalleryService> logger)
        {
            _repository = repository;
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
        }

        /// <summary>
        /// Get category images with multi-level caching for maximum performance
        /// </summary>
        public async Task<PagedResult<GalleryImageDto>> GetCategoryImagesAsync(
            string category, 
            int page = 1, 
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool useCache = true)
        {
            var stopwatch = Stopwatch.StartNew();
            var cacheKey = $"{CACHE_PREFIX}category_{category}_{page}_{pageSize}_{sortBy}_{descending}";
            
            try
            {
                Interlocked.Increment(ref _totalRequests);
                TrackCategoryRequest(category);
                
                if (useCache)
                {
                    // 1. Check memory cache first (sub-5ms response)
                    if (_memoryCache.TryGetValue(cacheKey, out PagedResult<GalleryImageDto>? cachedResult) && cachedResult != null)
                    {
                        Interlocked.Increment(ref _cacheHits);
                        cachedResult.CacheSource = "memory";
                        cachedResult.QueryTime = stopwatch.Elapsed;
                        
                        _logger.LogDebug("Memory cache hit for category {Category}, page {Page}", category, page);
                        TrackResponseTime(category, stopwatch.Elapsed);
                        return cachedResult;
                    }
                    
                    // 2. Check distributed cache (sub-50ms response)
                    var distributedCached = await _distributedCache.GetStringAsync(cacheKey);
                    if (!string.IsNullOrEmpty(distributedCached))
                    {
                        try
                        {
                            var result = JsonSerializer.Deserialize<PagedResult<GalleryImageDto>>(distributedCached);
                            if (result != null)
                            {
                                Interlocked.Increment(ref _cacheHits);
                                result.CacheSource = "distributed";
                                result.QueryTime = stopwatch.Elapsed;
                                
                                // Store in memory cache for next request
                                _memoryCache.Set(cacheKey, result, _memoryCacheExpiry);
                                
                                _logger.LogDebug("Distributed cache hit for category {Category}, page {Page}", category, page);
                                TrackResponseTime(category, stopwatch.Elapsed);
                                return result;
                            }
                        }
                        catch (JsonException ex)
                        {
                            _logger.LogWarning(ex, "Failed to deserialize cached result for {CacheKey}", cacheKey);
                        }
                    }
                }
                
                // 3. Query database (target: sub-200ms)
                Interlocked.Increment(ref _cacheMisses);
                var dbStartTime = DateTime.UtcNow;
                var dbResult = await _repository.GetCategoryImagesAsync(category, page, pageSize, sortBy, descending);
                var dbQueryTime = DateTime.UtcNow - dbStartTime;
                
                // Convert to DTOs
                var dtoResult = ConvertToPagedDto(dbResult);
                dtoResult.CacheSource = "database";
                dtoResult.QueryTime = stopwatch.Elapsed;
                dtoResult.DatabaseQueryTime = dbQueryTime;
                
                if (useCache)
                {
                    // 4. Cache at both levels for future requests
                    try
                    {
                        var serialized = JsonSerializer.Serialize(dtoResult);
                        await _distributedCache.SetStringAsync(cacheKey, serialized, new DistributedCacheEntryOptions
                        {
                            AbsoluteExpirationRelativeToNow = _distributedCacheExpiry
                        });
                        _memoryCache.Set(cacheKey, dtoResult, _memoryCacheExpiry);
                        
                        _logger.LogDebug("Cached result for category {Category}, page {Page}", category, page);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to cache result for {CacheKey}", cacheKey);
                    }
                }
                
                TrackResponseTime(category, stopwatch.Elapsed);
                
                _logger.LogInformation(
                    "Gallery query completed: Category={Category}, Page={Page}, Items={Count}, " +
                    "CacheSource={CacheSource}, Time={Time}ms, DBTime={DBTime}ms",
                    category, page, dtoResult.ItemCount, dtoResult.CacheSource, 
                    stopwatch.Elapsed.TotalMilliseconds, dbQueryTime.TotalMilliseconds);
                
                return dtoResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting category images for {Category}, page {Page}", category, page);
                throw;
            }
        }

        /// <summary>
        /// Get lightweight category images for mobile/list views
        /// </summary>
        public async Task<PagedResult<GalleryImageDto>> GetCategoryImagesLightweightAsync(
            string category, 
            int page = 1, 
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true)
        {
            var stopwatch = Stopwatch.StartNew();
            var cacheKey = $"{CACHE_PREFIX}lightweight_{category}_{page}_{pageSize}_{sortBy}_{descending}";
            
            try
            {
                // Check memory cache first
                if (_memoryCache.TryGetValue(cacheKey, out PagedResult<GalleryImageDto>? cachedResult) && cachedResult != null)
                {
                    cachedResult.CacheSource = "memory";
                    cachedResult.QueryTime = stopwatch.Elapsed;
                    return cachedResult;
                }
                
                // Query database with lightweight projection
                var dbResult = await _repository.GetCategoryImagesLightweightAsync(category, page, pageSize);
                var dtoResult = ConvertToPagedDtoLightweight(dbResult);
                dtoResult.CacheSource = "database";
                dtoResult.QueryTime = stopwatch.Elapsed;
                
                // Cache for shorter time (lightweight data changes more frequently)
                _memoryCache.Set(cacheKey, dtoResult, TimeSpan.FromMinutes(2));
                
                return dtoResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lightweight category images for {Category}", category);
                throw;
            }
        }

        public async Task<GalleryImageDto?> GetImageByIdAsync(long id, bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}image_{id}";
            
            if (useCache && _memoryCache.TryGetValue(cacheKey, out GalleryImageDto? cachedImage) && cachedImage != null)
            {
                return cachedImage;
            }
            
            var image = await _repository.GetByIdAsync(id);
            if (image == null) return null;
            
            var dto = GalleryImageDto.FromEntity(image);
            
            if (useCache)
            {
                _memoryCache.Set(cacheKey, dto, _memoryCacheExpiry);
            }
            
            return dto;
        }

        public async Task<GalleryImageDto?> GetImageByStorageKeyAsync(string storageKey, bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}storage_{storageKey}";
            
            if (useCache && _memoryCache.TryGetValue(cacheKey, out GalleryImageDto? cachedImage) && cachedImage != null)
            {
                return cachedImage;
            }
            
            var image = await _repository.GetByStorageKeyAsync(storageKey);
            if (image == null) return null;
            
            var dto = GalleryImageDto.FromEntity(image);
            
            if (useCache)
            {
                _memoryCache.Set(cacheKey, dto, _memoryCacheExpiry);
            }
            
            return dto;
        }

        public async Task<List<GalleryImageDto>> GetImagesByIdsAsync(IEnumerable<long> ids, bool useCache = true)
        {
            var idList = ids.ToList();
            var result = new List<GalleryImageDto>();
            var uncachedIds = new List<long>();
            
            if (useCache)
            {
                // Check cache for each ID
                foreach (var id in idList)
                {
                    var cacheKey = $"{CACHE_PREFIX}image_{id}";
                    if (_memoryCache.TryGetValue(cacheKey, out GalleryImageDto? cachedImage) && cachedImage != null)
                    {
                        result.Add(cachedImage);
                    }
                    else
                    {
                        uncachedIds.Add(id);
                    }
                }
            }
            else
            {
                uncachedIds = idList;
            }
            
            // Query database for uncached images
            if (uncachedIds.Any())
            {
                var images = await _repository.GetByIdsAsync(uncachedIds);
                foreach (var image in images)
                {
                    var dto = GalleryImageDto.FromEntity(image);
                    result.Add(dto);
                    
                    if (useCache)
                    {
                        var cacheKey = $"{CACHE_PREFIX}image_{image.Id}";
                        _memoryCache.Set(cacheKey, dto, _memoryCacheExpiry);
                    }
                }
            }
            
            return result.OrderBy(dto => idList.IndexOf(dto.Id)).ToList();
        }

        /// <summary>
        /// Convert paged repository result to DTO result
        /// </summary>
        private PagedResult<GalleryImageDto> ConvertToPagedDto(PagedResult<CatGalleryImage> source)
        {
            var dtos = source.Items.Select(GalleryImageDto.FromEntity).ToList();
            
            return PagedResult<GalleryImageDto>.CreateWithTotals(
                dtos, source.TotalCount, source.Page, source.PageSize, source.SortBy, source.SortDirection)
                .WithPerformanceTracking(source.QueryTime, source.CacheSource, source.DatabaseQueryTime);
        }

        /// <summary>
        /// Convert paged repository result to lightweight DTO result
        /// </summary>
        private PagedResult<GalleryImageDto> ConvertToPagedDtoLightweight(PagedResult<CatGalleryImage> source)
        {
            var dtos = source.Items.Select(GalleryImageDto.CreateLightweight).ToList();
            
            return PagedResult<GalleryImageDto>.CreateWithTotals(
                dtos, source.TotalCount, source.Page, source.PageSize, source.SortBy, source.SortDirection)
                .WithPerformanceTracking(source.QueryTime, source.CacheSource, source.DatabaseQueryTime);
        }

        /// <summary>
        /// Track response time by category for performance monitoring
        /// </summary>
        private void TrackResponseTime(string category, TimeSpan responseTime)
        {
            lock (_responseTimesByCategory)
            {
                if (!_responseTimesByCategory.ContainsKey(category))
                {
                    _responseTimesByCategory[category] = new List<TimeSpan>();
                }
                
                _responseTimesByCategory[category].Add(responseTime);
                
                // Keep only last 100 measurements per category
                if (_responseTimesByCategory[category].Count > 100)
                {
                    _responseTimesByCategory[category].RemoveAt(0);
                }
            }
        }

        /// <summary>
        /// Track request count by category
        /// </summary>
        private void TrackCategoryRequest(string category)
        {
            lock (_requestCountsByCategory)
            {
                if (!_requestCountsByCategory.ContainsKey(category))
                {
                    _requestCountsByCategory[category] = 0;
                }
                _requestCountsByCategory[category]++;
            }
        }

        // Search operations
        public async Task<PagedResult<GalleryImageDto>> SearchImagesAsync(
            string query,
            string? category = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}search_{query}_{category}_{page}_{pageSize}_{sortBy}_{descending}";

            if (useCache && _memoryCache.TryGetValue(cacheKey, out PagedResult<GalleryImageDto>? cachedResult) && cachedResult != null)
            {
                return cachedResult;
            }

            var dbResult = await _repository.SearchAsync(query, category, page, pageSize, sortBy, descending);
            var dtoResult = ConvertToPagedDto(dbResult);

            if (useCache)
            {
                // Cache search results for shorter time
                _memoryCache.Set(cacheKey, dtoResult, TimeSpan.FromMinutes(2));
            }

            return dtoResult;
        }

        public async Task<PagedResult<GalleryImageDto>> AdvancedSearchAsync(
            string? catName = null,
            string? breed = null,
            string? gender = null,
            string? category = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? tags = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}advsearch_{catName}_{breed}_{gender}_{category}_{dateFrom}_{dateTo}_{tags}_{page}_{pageSize}_{sortBy}_{descending}";

            if (useCache && _memoryCache.TryGetValue(cacheKey, out PagedResult<GalleryImageDto>? cachedResult) && cachedResult != null)
            {
                return cachedResult;
            }

            var searchCriteria = new {
                catName, breed, gender, category, dateFrom, dateTo, tags, page, pageSize, sortBy, descending
            };
            var dbResult = await _repository.AdvancedSearchAsync(searchCriteria);
            var dtoResult = ConvertToPagedDto(dbResult);

            if (useCache)
            {
                _memoryCache.Set(cacheKey, dtoResult, TimeSpan.FromMinutes(2));
            }

            return dtoResult;
        }

        // Analytics and statistics
        public async Task<Dictionary<string, int>> GetCategoryStatsAsync(bool useCache = true)
        {
            if (useCache && _memoryCache.TryGetValue(STATS_CACHE_KEY, out Dictionary<string, int>? cachedStats) && cachedStats != null)
            {
                return cachedStats;
            }

            var stats = await _repository.GetCategoryStatsAsync();

            if (useCache)
            {
                _memoryCache.Set(STATS_CACHE_KEY, stats, _statsCacheExpiry);
            }

            return stats;
        }

        public async Task<List<GalleryImageDto>> GetPopularImagesAsync(int count = 10, string? category = null, bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}popular_{count}_{category}";

            if (useCache && _memoryCache.TryGetValue(cacheKey, out List<GalleryImageDto>? cachedImages) && cachedImages != null)
            {
                return cachedImages;
            }

            var images = await _repository.GetPopularImagesAsync(count, category);
            var dtos = images.Select(GalleryImageDto.FromEntity).ToList();

            if (useCache)
            {
                _memoryCache.Set(cacheKey, dtos, TimeSpan.FromMinutes(10));
            }

            return dtos;
        }

        public async Task<List<GalleryImageDto>> GetRecentImagesAsync(int count = 10, string? category = null, bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}recent_{count}_{category}";

            if (useCache && _memoryCache.TryGetValue(cacheKey, out List<GalleryImageDto>? cachedImages) && cachedImages != null)
            {
                return cachedImages;
            }

            var images = await _repository.GetRecentlyUploadedAsync(count, category);
            var dtos = images.Select(GalleryImageDto.FromEntity).ToList();

            if (useCache)
            {
                _memoryCache.Set(cacheKey, dtos, TimeSpan.FromMinutes(5));
            }

            return dtos;
        }

        public async Task<List<GalleryImageDto>> GetFeaturedImagesAsync(int count = 5, string? category = null)
        {
            // Featured images are random, so don't cache
            var images = await _repository.GetRandomImagesAsync(count, category);
            return images.Select(GalleryImageDto.FromEntity).ToList();
        }

        public async Task<List<GalleryImageDto>> GetRelatedImagesAsync(string catId, int count = 10, bool useCache = true)
        {
            var cacheKey = $"{CACHE_PREFIX}related_{catId}_{count}";

            if (useCache && _memoryCache.TryGetValue(cacheKey, out List<GalleryImageDto>? cachedImages) && cachedImages != null)
            {
                return cachedImages;
            }

            var images = await _repository.GetImagesByCatIdAsync(catId, false);
            var dtos = images.Take(count).Select(GalleryImageDto.FromEntity).ToList();

            if (useCache)
            {
                _memoryCache.Set(cacheKey, dtos, TimeSpan.FromMinutes(15));
            }

            return dtos;
        }

        // Cache management
        public async Task InvalidateCacheAsync(string? category = null, long? imageId = null, string? storageKey = null)
        {
            try
            {
                if (imageId.HasValue)
                {
                    // Remove specific image from cache
                    _memoryCache.Remove($"{CACHE_PREFIX}image_{imageId.Value}");
                }

                if (!string.IsNullOrEmpty(storageKey))
                {
                    _memoryCache.Remove($"{CACHE_PREFIX}storage_{storageKey}");
                }

                if (!string.IsNullOrEmpty(category))
                {
                    // Remove category-specific cache entries
                    // Note: This is a simplified approach. In production, consider using cache tags
                    var keysToRemove = new List<string>();

                    // This would need a more sophisticated cache key tracking mechanism
                    // For now, just remove common patterns
                    for (int page = 1; page <= 10; page++)
                    {
                        keysToRemove.Add($"{CACHE_PREFIX}category_{category}_{page}_20_DateTaken_True");
                        keysToRemove.Add($"{CACHE_PREFIX}category_{category}_{page}_20_DateTaken_False");
                        keysToRemove.Add($"{CACHE_PREFIX}lightweight_{category}_{page}_20_DateTaken_True");
                        keysToRemove.Add($"{CACHE_PREFIX}lightweight_{category}_{page}_20_DateTaken_False");
                    }

                    foreach (var key in keysToRemove)
                    {
                        _memoryCache.Remove(key);
                    }
                }

                // Always invalidate stats cache when any data changes
                _memoryCache.Remove(STATS_CACHE_KEY);

                _logger.LogInformation("Cache invalidated for category: {Category}, imageId: {ImageId}, storageKey: {StorageKey}",
                    category, imageId, storageKey);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache");
            }
        }

        public async Task WarmCacheAsync(string? category = null, int imageCount = 50)
        {
            try
            {
                _logger.LogInformation("Starting cache warmup for category: {Category}", category ?? "all");

                var images = await _repository.GetImagesForCacheWarmupAsync(category, imageCount);

                foreach (var image in images)
                {
                    var dto = GalleryImageDto.FromEntity(image);
                    var cacheKey = $"{CACHE_PREFIX}image_{image.Id}";
                    _memoryCache.Set(cacheKey, dto, _memoryCacheExpiry);
                }

                // Warm up first few pages of category data
                if (!string.IsNullOrEmpty(category))
                {
                    for (int page = 1; page <= 3; page++)
                    {
                        await GetCategoryImagesAsync(category, page, 20, "DateTaken", true, true);
                    }
                }

                _logger.LogInformation("Cache warmup completed for {Count} images", images.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache warmup");
            }
        }

        public async Task<CacheStats> GetCacheStatsAsync()
        {
            var stats = new CacheStats();

            // Calculate hit rates
            var totalRequests = _cacheHits + _cacheMisses;
            if (totalRequests > 0)
            {
                stats.OverallCacheHitRate = (double)_cacheHits / totalRequests;
            }

            // Get response time averages by category
            lock (_responseTimesByCategory)
            {
                foreach (var kvp in _responseTimesByCategory)
                {
                    if (kvp.Value.Any())
                    {
                        stats.CacheHitsByCategory[kvp.Key] = kvp.Value.Count;
                    }
                }
            }

            return stats;
        }

        public async Task ClearAllCacheAsync()
        {
            try
            {
                // Clear memory cache (this is a simplified approach)
                if (_memoryCache is MemoryCache mc)
                {
                    mc.Clear();
                }

                _logger.LogWarning("All cache cleared - this will impact performance temporarily");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache");
            }
        }

        // Performance tracking
        public async Task IncrementAccessCountAsync(long id)
        {
            // Fire-and-forget operation
            _ = Task.Run(async () =>
            {
                try
                {
                    await _repository.IncrementAccessCountAsync(id);

                    // Invalidate cached image
                    _memoryCache.Remove($"{CACHE_PREFIX}image_{id}");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to increment access count for image {ImageId}", id);
                }
            });
        }

        public async Task<GalleryPerformanceMetrics> GetPerformanceMetricsAsync()
        {
            var metrics = new GalleryPerformanceMetrics
            {
                TotalRequests = _totalRequests
            };

            lock (_responseTimesByCategory)
            {
                foreach (var kvp in _responseTimesByCategory)
                {
                    if (kvp.Value.Any())
                    {
                        metrics.ResponseTimesByCategory[kvp.Key] = TimeSpan.FromMilliseconds(
                            kvp.Value.Average(t => t.TotalMilliseconds));
                    }
                }

                if (_responseTimesByCategory.Values.Any(list => list.Any()))
                {
                    var allTimes = _responseTimesByCategory.Values.SelectMany(list => list).ToList();
                    if (allTimes.Any())
                    {
                        metrics.AverageResponseTime = TimeSpan.FromMilliseconds(
                            allTimes.Average(t => t.TotalMilliseconds));

                        var sortedTimes = allTimes.OrderBy(t => t.TotalMilliseconds).ToList();
                        var p95Index = (int)(sortedTimes.Count * 0.95);
                        var p99Index = (int)(sortedTimes.Count * 0.99);

                        if (p95Index < sortedTimes.Count)
                            metrics.P95ResponseTime = sortedTimes[p95Index];
                        if (p99Index < sortedTimes.Count)
                            metrics.P99ResponseTime = sortedTimes[p99Index];
                    }
                }
            }

            lock (_requestCountsByCategory)
            {
                metrics.RequestsByCategory = new Dictionary<string, int>(_requestCountsByCategory);
            }

            return metrics;
        }

        public async Task<GalleryHealthCheck> GetHealthCheckAsync()
        {
            var healthCheck = new GalleryHealthCheck();
            var issues = new List<string>();

            try
            {
                // Test database connectivity
                var dbStartTime = DateTime.UtcNow;
                var stats = await _repository.GetHealthStatsAsync();
                healthCheck.DatabaseResponseTime = DateTime.UtcNow - dbStartTime;
                healthCheck.DatabaseConnected = true;
                healthCheck.TotalImages = stats.TryGetValue("TotalImages", out var totalImages) ? Convert.ToInt32(totalImages) : 0;
                healthCheck.ActiveImages = stats.TryGetValue("ActiveImages", out var activeImages) ? Convert.ToInt32(activeImages) : 0;

                if (healthCheck.DatabaseResponseTime > TimeSpan.FromSeconds(1))
                {
                    issues.Add("Database response time is slow");
                }

                // Test cache connectivity
                var cacheStartTime = DateTime.UtcNow;
                _memoryCache.Set("health_check", "test", TimeSpan.FromSeconds(1));
                var cacheTest = _memoryCache.Get("health_check");
                healthCheck.CacheResponseTime = DateTime.UtcNow - cacheStartTime;
                healthCheck.CacheConnected = cacheTest != null;

                if (!healthCheck.CacheConnected)
                {
                    issues.Add("Memory cache is not responding");
                }

                // Check for performance issues
                var cacheHitRate = _cacheHits + _cacheMisses > 0 ? (double)_cacheHits / (_cacheHits + _cacheMisses) : 0;
                if (cacheHitRate < 0.5)
                {
                    issues.Add($"Cache hit rate is low: {cacheHitRate:P}");
                }

                healthCheck.IsHealthy = !issues.Any();
                healthCheck.Status = healthCheck.IsHealthy ? "Healthy" : "Degraded";
                healthCheck.Issues = issues;

                healthCheck.Metrics = new Dictionary<string, object>
                {
                    ["CacheHitRate"] = cacheHitRate,
                    ["TotalRequests"] = _totalRequests,
                    ["CacheHits"] = _cacheHits,
                    ["CacheMisses"] = _cacheMisses
                };
            }
            catch (Exception ex)
            {
                healthCheck.IsHealthy = false;
                healthCheck.Status = "Unhealthy";
                healthCheck.Issues.Add($"Health check failed: {ex.Message}");
                _logger.LogError(ex, "Health check failed");
            }

            return healthCheck;
        }

        // Image management operations
        public async Task<GalleryImageDto> AddImageAsync(CatGalleryImage image)
        {
            var addedImage = await _repository.AddAsync(image);
            var dto = GalleryImageDto.FromEntity(addedImage);

            // Invalidate relevant caches
            await InvalidateCacheAsync(image.Category);

            return dto;
        }

        public async Task<GalleryImageDto> UpdateImageAsync(CatGalleryImage image)
        {
            await _repository.UpdateAsync(image);
            var dto = GalleryImageDto.FromEntity(image);

            // Invalidate relevant caches
            await InvalidateCacheAsync(image.Category, image.Id, image.StorageKey);

            return dto;
        }

        public async Task DeleteImageAsync(long id)
        {
            var image = await _repository.GetByIdAsync(id);
            if (image != null)
            {
                await _repository.SoftDeleteAsync(id);
                await InvalidateCacheAsync(image.Category, id, image.StorageKey);
            }
        }

        public async Task<List<GalleryImageDto>> BulkUpdateImagesAsync(IEnumerable<CatGalleryImage> images)
        {
            var imageList = images.ToList();
            var updatedImages = await _repository.BulkUpdateAsync(imageList);
            var dtos = updatedImages.Select(GalleryImageDto.FromEntity).ToList();

            // Invalidate caches for all affected categories
            var categories = imageList.Select(img => img.Category).Distinct();
            foreach (var category in categories)
            {
                await InvalidateCacheAsync(category);
            }

            return dtos;
        }
    }
}
