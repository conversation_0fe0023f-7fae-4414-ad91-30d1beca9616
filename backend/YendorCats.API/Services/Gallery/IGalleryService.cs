using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Services.Gallery
{
    /// <summary>
    /// High-performance gallery service interface
    /// Designed to achieve 85-90% performance improvement over S3 scanning
    /// Target: sub-500ms response times with multi-level caching
    /// </summary>
    public interface IGalleryService
    {
        // Primary gallery operations - CRITICAL for performance improvement
        /// <summary>
        /// Get paginated images for a category with multi-level caching
        /// Performance target: sub-500ms response time
        /// Cache hierarchy: Memory (5min) → Distributed (30min) → Database
        /// </summary>
        Task<PagedResult<GalleryImageDto>> GetCategoryImagesAsync(
            string category, 
            int page = 1, 
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool useCache = true);
        
        /// <summary>
        /// Get lightweight category images for list views (minimal data transfer)
        /// Optimized for mobile and slow connections
        /// </summary>
        Task<PagedResult<GalleryImageDto>> GetCategoryImagesLightweightAsync(
            string category, 
            int page = 1, 
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        /// <summary>
        /// Get single image by ID with caching
        /// </summary>
        Task<GalleryImageDto?> GetImageByIdAsync(long id, bool useCache = true);
        
        /// <summary>
        /// Get single image by storage key with caching
        /// </summary>
        Task<GalleryImageDto?> GetImageByStorageKeyAsync(string storageKey, bool useCache = true);
        
        /// <summary>
        /// Get multiple images by IDs in a single optimized call
        /// </summary>
        Task<List<GalleryImageDto>> GetImagesByIdsAsync(IEnumerable<long> ids, bool useCache = true);
        
        // Search and filtering operations
        /// <summary>
        /// Search images with full-text search and caching
        /// </summary>
        Task<PagedResult<GalleryImageDto>> SearchImagesAsync(
            string query, 
            string? category = null, 
            int page = 1, 
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool useCache = true);
        
        /// <summary>
        /// Advanced search with multiple criteria
        /// </summary>
        Task<PagedResult<GalleryImageDto>> AdvancedSearchAsync(
            string? catName = null,
            string? breed = null,
            string? gender = null,
            string? category = null,
            DateTime? dateFrom = null,
            DateTime? dateTo = null,
            string? tags = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool useCache = true);
        
        // Analytics and statistics
        /// <summary>
        /// Get category statistics with caching
        /// </summary>
        Task<Dictionary<string, int>> GetCategoryStatsAsync(bool useCache = true);
        
        /// <summary>
        /// Get popular images across all categories or specific category
        /// </summary>
        Task<List<GalleryImageDto>> GetPopularImagesAsync(int count = 10, string? category = null, bool useCache = true);
        
        /// <summary>
        /// Get recently uploaded images
        /// </summary>
        Task<List<GalleryImageDto>> GetRecentImagesAsync(int count = 10, string? category = null, bool useCache = true);
        
        /// <summary>
        /// Get random featured images for homepage
        /// </summary>
        Task<List<GalleryImageDto>> GetFeaturedImagesAsync(int count = 5, string? category = null);
        
        /// <summary>
        /// Get related images for a specific cat
        /// </summary>
        Task<List<GalleryImageDto>> GetRelatedImagesAsync(string catId, int count = 10, bool useCache = true);
        
        // Cache management operations
        /// <summary>
        /// Invalidate cache for specific category or image
        /// </summary>
        Task InvalidateCacheAsync(string? category = null, long? imageId = null, string? storageKey = null);
        
        /// <summary>
        /// Warm cache for popular categories and images
        /// </summary>
        Task WarmCacheAsync(string? category = null, int imageCount = 50);
        
        /// <summary>
        /// Get cache statistics and performance metrics
        /// </summary>
        Task<CacheStats> GetCacheStatsAsync();
        
        /// <summary>
        /// Clear all cache entries (use with caution)
        /// </summary>
        Task ClearAllCacheAsync();
        
        // Performance tracking and monitoring
        /// <summary>
        /// Increment access count for an image (fire-and-forget)
        /// </summary>
        Task IncrementAccessCountAsync(long id);
        
        /// <summary>
        /// Get performance metrics for monitoring
        /// </summary>
        Task<GalleryPerformanceMetrics> GetPerformanceMetricsAsync();
        
        /// <summary>
        /// Get health check information
        /// </summary>
        Task<GalleryHealthCheck> GetHealthCheckAsync();
        
        // Image management operations (for admin)
        /// <summary>
        /// Add new image to gallery with cache invalidation
        /// </summary>
        Task<GalleryImageDto> AddImageAsync(CatGalleryImage image);
        
        /// <summary>
        /// Update existing image with cache invalidation
        /// </summary>
        Task<GalleryImageDto> UpdateImageAsync(CatGalleryImage image);
        
        /// <summary>
        /// Soft delete image with cache invalidation
        /// </summary>
        Task DeleteImageAsync(long id);
        
        /// <summary>
        /// Bulk operations for admin efficiency
        /// </summary>
        Task<List<GalleryImageDto>> BulkUpdateImagesAsync(IEnumerable<CatGalleryImage> images);
    }
    
    /// <summary>
    /// Cache statistics for monitoring
    /// </summary>
    public class CacheStats
    {
        public int MemoryCacheEntries { get; set; }
        public long MemoryCacheSize { get; set; }
        public int DistributedCacheEntries { get; set; }
        public double MemoryCacheHitRate { get; set; }
        public double DistributedCacheHitRate { get; set; }
        public double OverallCacheHitRate { get; set; }
        public TimeSpan AverageMemoryCacheResponseTime { get; set; }
        public TimeSpan AverageDistributedCacheResponseTime { get; set; }
        public TimeSpan AverageDatabaseResponseTime { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, int> CacheHitsByCategory { get; set; } = new();
        public Dictionary<string, int> CacheMissesByCategory { get; set; } = new();
    }
    
    /// <summary>
    /// Performance metrics for monitoring
    /// </summary>
    public class GalleryPerformanceMetrics
    {
        public int TotalRequests { get; set; }
        public int RequestsLast24Hours { get; set; }
        public int RequestsLastHour { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public TimeSpan MedianResponseTime { get; set; }
        public TimeSpan P95ResponseTime { get; set; }
        public TimeSpan P99ResponseTime { get; set; }
        public double ErrorRate { get; set; }
        public Dictionary<string, int> RequestsByCategory { get; set; } = new();
        public Dictionary<string, TimeSpan> ResponseTimesByCategory { get; set; } = new();
        public List<string> SlowestQueries { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Health check information
    /// </summary>
    public class GalleryHealthCheck
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public TimeSpan DatabaseResponseTime { get; set; }
        public TimeSpan CacheResponseTime { get; set; }
        public bool DatabaseConnected { get; set; }
        public bool CacheConnected { get; set; }
        public int TotalImages { get; set; }
        public int ActiveImages { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.UtcNow;
    }
}
