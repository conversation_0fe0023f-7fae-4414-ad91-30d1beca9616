using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;
using System.Collections.Concurrent;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Services.Gallery
{
    /// <summary>
    /// Advanced caching service for gallery operations
    /// Provides sophisticated cache management with tagging, invalidation, and performance tracking
    /// </summary>
    public interface IGalleryCacheService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, params string[] tags) where T : class;
        Task RemoveAsync(string key);
        Task RemoveByTagAsync(string tag);
        Task RemoveByPatternAsync(string pattern);
        Task<CacheStatistics> GetStatisticsAsync();
        Task WarmupAsync(string category, int count = 50);
        Task ClearAllAsync();
    }

    public class GalleryCacheService : IGalleryCacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<GalleryCacheService> _logger;
        
        // Cache configuration
        private readonly TimeSpan _defaultMemoryExpiry = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _defaultDistributedExpiry = TimeSpan.FromMinutes(30);
        
        // Cache tracking
        private readonly ConcurrentDictionary<string, HashSet<string>> _tagToKeys = new();
        private readonly ConcurrentDictionary<string, HashSet<string>> _keyToTags = new();
        private readonly ConcurrentDictionary<string, DateTime> _keyAccessTimes = new();
        private readonly ConcurrentDictionary<string, int> _keyAccessCounts = new();
        
        // Performance tracking
        private long _memoryHits = 0;
        private long _distributedHits = 0;
        private long _misses = 0;
        private readonly ConcurrentQueue<TimeSpan> _recentResponseTimes = new();

        public GalleryCacheService(
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            ILogger<GalleryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
        }

        /// <summary>
        /// Get cached value with multi-level cache hierarchy
        /// </summary>
        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                // Track access
                _keyAccessTimes[key] = DateTime.UtcNow;
                _keyAccessCounts.AddOrUpdate(key, 1, (k, v) => v + 1);
                
                // 1. Check memory cache first (fastest)
                if (_memoryCache.TryGetValue(key, out T? memoryValue) && memoryValue != null)
                {
                    Interlocked.Increment(ref _memoryHits);
                    TrackResponseTime(DateTime.UtcNow - startTime);
                    _logger.LogDebug("Memory cache hit for key: {Key}", key);
                    return memoryValue;
                }
                
                // 2. Check distributed cache
                var distributedValue = await _distributedCache.GetStringAsync(key);
                if (!string.IsNullOrEmpty(distributedValue))
                {
                    try
                    {
                        var deserializedValue = JsonSerializer.Deserialize<T>(distributedValue);
                        if (deserializedValue != null)
                        {
                            Interlocked.Increment(ref _distributedHits);
                            
                            // Store in memory cache for next time
                            _memoryCache.Set(key, deserializedValue, _defaultMemoryExpiry);
                            
                            TrackResponseTime(DateTime.UtcNow - startTime);
                            _logger.LogDebug("Distributed cache hit for key: {Key}", key);
                            return deserializedValue;
                        }
                    }
                    catch (JsonException ex)
                    {
                        _logger.LogWarning(ex, "Failed to deserialize cached value for key: {Key}", key);
                        // Remove corrupted cache entry
                        await _distributedCache.RemoveAsync(key);
                    }
                }
                
                // Cache miss
                Interlocked.Increment(ref _misses);
                TrackResponseTime(DateTime.UtcNow - startTime);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return null;
            }
        }

        /// <summary>
        /// Set value in cache with tagging support
        /// </summary>
        public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null, params string[] tags) where T : class
        {
            try
            {
                var memoryExpiry = expiry ?? _defaultMemoryExpiry;
                var distributedExpiry = expiry ?? _defaultDistributedExpiry;
                
                // Store in memory cache
                _memoryCache.Set(key, value, memoryExpiry);
                
                // Store in distributed cache
                var serializedValue = JsonSerializer.Serialize(value);
                await _distributedCache.SetStringAsync(key, serializedValue, new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = distributedExpiry
                });
                
                // Track tags
                if (tags.Any())
                {
                    _keyToTags[key] = new HashSet<string>(tags);
                    
                    foreach (var tag in tags)
                    {
                        _tagToKeys.AddOrUpdate(tag, 
                            new HashSet<string> { key }, 
                            (t, existing) => 
                            {
                                existing.Add(key);
                                return existing;
                            });
                    }
                }
                
                _logger.LogDebug("Cached value for key: {Key} with tags: {Tags}", key, string.Join(", ", tags));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
            }
        }

        /// <summary>
        /// Remove specific cache entry
        /// </summary>
        public async Task RemoveAsync(string key)
        {
            try
            {
                // Remove from both caches
                _memoryCache.Remove(key);
                await _distributedCache.RemoveAsync(key);
                
                // Clean up tag tracking
                if (_keyToTags.TryRemove(key, out var tags))
                {
                    foreach (var tag in tags)
                    {
                        if (_tagToKeys.TryGetValue(tag, out var keys))
                        {
                            keys.Remove(key);
                            if (!keys.Any())
                            {
                                _tagToKeys.TryRemove(tag, out _);
                            }
                        }
                    }
                }
                
                // Clean up access tracking
                _keyAccessTimes.TryRemove(key, out _);
                _keyAccessCounts.TryRemove(key, out _);
                
                _logger.LogDebug("Removed cache entry for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
            }
        }

        /// <summary>
        /// Remove all cache entries with a specific tag
        /// </summary>
        public async Task RemoveByTagAsync(string tag)
        {
            try
            {
                if (_tagToKeys.TryGetValue(tag, out var keys))
                {
                    var keyList = keys.ToList(); // Create a copy to avoid modification during iteration
                    
                    foreach (var key in keyList)
                    {
                        await RemoveAsync(key);
                    }
                    
                    _logger.LogInformation("Removed {Count} cache entries with tag: {Tag}", keyList.Count, tag);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by tag: {Tag}", tag);
            }
        }

        /// <summary>
        /// Remove cache entries matching a pattern
        /// </summary>
        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                var keysToRemove = new List<string>();
                
                // Find keys matching pattern in tag tracking
                foreach (var key in _keyToTags.Keys)
                {
                    if (key.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                    {
                        keysToRemove.Add(key);
                    }
                }
                
                foreach (var key in keysToRemove)
                {
                    await RemoveAsync(key);
                }
                
                _logger.LogInformation("Removed {Count} cache entries matching pattern: {Pattern}", keysToRemove.Count, pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
            }
        }

        /// <summary>
        /// Get comprehensive cache statistics
        /// </summary>
        public async Task<CacheStatistics> GetStatisticsAsync()
        {
            var stats = new CacheStatistics();
            
            try
            {
                var totalRequests = _memoryHits + _distributedHits + _misses;
                
                stats.TotalRequests = totalRequests;
                stats.MemoryHits = _memoryHits;
                stats.DistributedHits = _distributedHits;
                stats.Misses = _misses;
                
                if (totalRequests > 0)
                {
                    stats.MemoryHitRate = (double)_memoryHits / totalRequests;
                    stats.DistributedHitRate = (double)_distributedHits / totalRequests;
                    stats.OverallHitRate = (double)(_memoryHits + _distributedHits) / totalRequests;
                    stats.MissRate = (double)_misses / totalRequests;
                }
                
                stats.TotalKeys = _keyToTags.Count;
                stats.TotalTags = _tagToKeys.Count;
                
                // Calculate average response time
                var responseTimes = _recentResponseTimes.ToArray();
                if (responseTimes.Any())
                {
                    stats.AverageResponseTime = TimeSpan.FromMilliseconds(
                        responseTimes.Average(t => t.TotalMilliseconds));
                }
                
                // Most accessed keys
                stats.MostAccessedKeys = _keyAccessCounts
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                
                // Recently accessed keys
                stats.RecentlyAccessedKeys = _keyAccessTimes
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10)
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                
                stats.GeneratedAt = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating cache statistics");
            }
            
            return stats;
        }

        /// <summary>
        /// Warm up cache with popular content
        /// </summary>
        public async Task WarmupAsync(string category, int count = 50)
        {
            try
            {
                _logger.LogInformation("Starting cache warmup for category: {Category}", category);
                
                // This would typically involve pre-loading popular content
                // For now, we'll just log the operation
                
                _logger.LogInformation("Cache warmup completed for category: {Category}", category);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache warmup for category: {Category}", category);
            }
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        public async Task ClearAllAsync()
        {
            try
            {
                // Clear memory cache
                if (_memoryCache is MemoryCache mc)
                {
                    mc.Clear();
                }
                
                // Clear tracking dictionaries
                _tagToKeys.Clear();
                _keyToTags.Clear();
                _keyAccessTimes.Clear();
                _keyAccessCounts.Clear();
                
                // Reset counters
                Interlocked.Exchange(ref _memoryHits, 0);
                Interlocked.Exchange(ref _distributedHits, 0);
                Interlocked.Exchange(ref _misses, 0);
                
                // Clear response times
                while (_recentResponseTimes.TryDequeue(out _)) { }
                
                _logger.LogWarning("All cache cleared - performance will be impacted temporarily");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all cache");
            }
        }

        /// <summary>
        /// Track response time for performance monitoring
        /// </summary>
        private void TrackResponseTime(TimeSpan responseTime)
        {
            _recentResponseTimes.Enqueue(responseTime);
            
            // Keep only recent measurements (last 1000)
            while (_recentResponseTimes.Count > 1000)
            {
                _recentResponseTimes.TryDequeue(out _);
            }
        }
    }

    /// <summary>
    /// Cache statistics for monitoring and optimization
    /// </summary>
    public class CacheStatistics
    {
        public long TotalRequests { get; set; }
        public long MemoryHits { get; set; }
        public long DistributedHits { get; set; }
        public long Misses { get; set; }
        public double MemoryHitRate { get; set; }
        public double DistributedHitRate { get; set; }
        public double OverallHitRate { get; set; }
        public double MissRate { get; set; }
        public int TotalKeys { get; set; }
        public int TotalTags { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public Dictionary<string, int> MostAccessedKeys { get; set; } = new();
        public Dictionary<string, DateTime> RecentlyAccessedKeys { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
}
