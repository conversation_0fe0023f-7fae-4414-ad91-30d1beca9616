using Microsoft.EntityFrameworkCore;
using System.Diagnostics;
using YendorCats.API.Data;
using YendorCats.API.Data.Repositories;
using YendorCats.API.Models;
using YendorCats.API.Services.Gallery;

namespace YendorCats.API.Services.B2Sync
{
    /// <summary>
    /// Implementation of B2 synchronization service
    /// Maintains real-time consistency between database and Backblaze B2 storage
    /// </summary>
    public class B2SyncService : IB2SyncService
    {
        private readonly AppDbContext _context;
        private readonly IGalleryRepository _galleryRepository;
        private readonly IGalleryCacheService _cacheService;
        private readonly ILogger<B2SyncService> _logger;
        
        public B2SyncService(
            AppDbContext context,
            IGalleryRepository galleryRepository,
            IGalleryCacheService cacheService,
            ILogger<B2SyncService> logger)
        {
            _context = context;
            _galleryRepository = galleryRepository;
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// Sync a single image upload to database
        /// </summary>
        public async Task<SyncResult> SyncImageUploadAsync(string b2Key, string bucketName, string? b2FileId = null, 
            Dictionary<string, string>? metadata = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new SyncResult
            {
                B2Key = b2Key,
                Operation = SyncOperation.Insert
            };
            
            try
            {
                _logger.LogInformation("Starting sync for B2 upload: {B2Key}", b2Key);
                
                // Check if image already exists in database
                var existingImage = await _galleryRepository.GetByStorageKeyAsync(b2Key);
                if (existingImage != null)
                {
                    _logger.LogWarning("Image already exists in database: {B2Key}, updating instead", b2Key);
                    return await SyncMetadataUpdateAsync(b2Key, metadata ?? new Dictionary<string, string>());
                }
                
                // Create new gallery image from B2 metadata
                var galleryImage = CreateGalleryImageFromB2(b2Key, bucketName, b2FileId, metadata);
                
                // Add to database
                var addedImage = await _galleryRepository.AddAsync(galleryImage);
                
                // Log sync operation
                await LogSyncOperationAsync(b2Key, SyncOperation.Insert, "SUCCESS", null);
                
                // Invalidate relevant caches
                await _cacheService.RemoveByTagAsync($"category_{galleryImage.Category}");
                
                result.Success = true;
                result.DatabaseId = addedImage.Id;
                result.Duration = stopwatch.Elapsed;
                
                _logger.LogInformation("Successfully synced B2 upload: {B2Key} -> Database ID: {DatabaseId}", 
                    b2Key, addedImage.Id);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                
                await LogSyncOperationAsync(b2Key, SyncOperation.Insert, "FAILED", ex.Message);
                
                _logger.LogError(ex, "Failed to sync B2 upload: {B2Key}", b2Key);
            }
            
            return result;
        }

        /// <summary>
        /// Sync image deletion from database
        /// </summary>
        public async Task<SyncResult> SyncImageDeletionAsync(string b2Key)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new SyncResult
            {
                B2Key = b2Key,
                Operation = SyncOperation.Delete
            };
            
            try
            {
                _logger.LogInformation("Starting sync for B2 deletion: {B2Key}", b2Key);
                
                var existingImage = await _galleryRepository.GetByStorageKeyAsync(b2Key);
                if (existingImage == null)
                {
                    _logger.LogWarning("Image not found in database for deletion: {B2Key}", b2Key);
                    result.Success = true; // Already deleted or never existed
                    result.Duration = stopwatch.Elapsed;
                    return result;
                }
                
                // Soft delete the image
                await _galleryRepository.SoftDeleteAsync(existingImage.Id);
                
                // Log sync operation
                await LogSyncOperationAsync(b2Key, SyncOperation.Delete, "SUCCESS", null);
                
                // Invalidate relevant caches
                await _cacheService.RemoveByTagAsync($"category_{existingImage.Category}");
                await _cacheService.RemoveAsync($"image_{existingImage.Id}");
                
                result.Success = true;
                result.DatabaseId = existingImage.Id;
                result.Duration = stopwatch.Elapsed;
                
                _logger.LogInformation("Successfully synced B2 deletion: {B2Key}", b2Key);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                
                await LogSyncOperationAsync(b2Key, SyncOperation.Delete, "FAILED", ex.Message);
                
                _logger.LogError(ex, "Failed to sync B2 deletion: {B2Key}", b2Key);
            }
            
            return result;
        }

        /// <summary>
        /// Sync metadata update for existing image
        /// </summary>
        public async Task<SyncResult> SyncMetadataUpdateAsync(string b2Key, Dictionary<string, string> metadata)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new SyncResult
            {
                B2Key = b2Key,
                Operation = SyncOperation.Update
            };
            
            try
            {
                _logger.LogInformation("Starting metadata sync for: {B2Key}", b2Key);
                
                var existingImage = await _galleryRepository.GetByStorageKeyAsync(b2Key);
                if (existingImage == null)
                {
                    _logger.LogWarning("Image not found in database for metadata update: {B2Key}", b2Key);
                    // Try to create new entry instead
                    return await SyncImageUploadAsync(b2Key, existingImage?.StorageBucketName ?? "yendor", null, metadata);
                }
                
                // Update metadata from B2
                UpdateImageMetadataFromB2(existingImage, metadata);
                existingImage.DateModified = DateTime.UtcNow;
                
                // Update in database
                await _galleryRepository.UpdateAsync(existingImage);
                
                // Log sync operation
                await LogSyncOperationAsync(b2Key, SyncOperation.Update, "SUCCESS", null);
                
                // Invalidate relevant caches
                await _cacheService.RemoveByTagAsync($"category_{existingImage.Category}");
                await _cacheService.RemoveAsync($"image_{existingImage.Id}");
                
                result.Success = true;
                result.DatabaseId = existingImage.Id;
                result.Duration = stopwatch.Elapsed;
                
                _logger.LogInformation("Successfully synced metadata update: {B2Key}", b2Key);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                
                await LogSyncOperationAsync(b2Key, SyncOperation.Update, "FAILED", ex.Message);
                
                _logger.LogError(ex, "Failed to sync metadata update: {B2Key}", b2Key);
            }
            
            return result;
        }

        /// <summary>
        /// Sync multiple images in a batch operation
        /// </summary>
        public async Task<BatchSyncResult> SyncBatchAsync(IEnumerable<B2SyncItem> items)
        {
            var itemList = items.ToList();
            var result = new BatchSyncResult
            {
                TotalItems = itemList.Count,
                StartTime = DateTime.UtcNow
            };
            
            _logger.LogInformation("Starting batch sync for {Count} items", itemList.Count);
            
            foreach (var item in itemList)
            {
                try
                {
                    SyncResult syncResult = item.Operation switch
                    {
                        SyncOperation.Insert => await SyncImageUploadAsync(item.B2Key, item.BucketName, item.B2FileId, item.Metadata),
                        SyncOperation.Update => await SyncMetadataUpdateAsync(item.B2Key, item.Metadata ?? new Dictionary<string, string>()),
                        SyncOperation.Delete => await SyncImageDeletionAsync(item.B2Key),
                        _ => new SyncResult { Success = false, ErrorMessage = "Unknown operation type" }
                    };
                    
                    result.Results.Add(syncResult);
                    
                    if (syncResult.Success)
                    {
                        result.SuccessfulItems++;
                    }
                    else
                    {
                        result.FailedItems++;
                        if (!string.IsNullOrEmpty(syncResult.ErrorMessage))
                        {
                            result.Errors.Add($"{item.B2Key}: {syncResult.ErrorMessage}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.FailedItems++;
                    result.Errors.Add($"{item.B2Key}: {ex.Message}");
                    _logger.LogError(ex, "Error processing batch item: {B2Key}", item.B2Key);
                }
            }
            
            result.EndTime = DateTime.UtcNow;
            result.TotalDuration = result.EndTime.Value - result.StartTime;
            
            _logger.LogInformation("Batch sync completed: {Successful}/{Total} successful, Duration: {Duration}ms",
                result.SuccessfulItems, result.TotalItems, result.TotalDuration.TotalMilliseconds);
            
            return result;
        }

        /// <summary>
        /// Create gallery image from B2 metadata
        /// </summary>
        private CatGalleryImage CreateGalleryImageFromB2(string b2Key, string bucketName, string? b2FileId, 
            Dictionary<string, string>? metadata)
        {
            var image = new CatGalleryImage
            {
                StorageKey = b2Key,
                StorageProvider = "B2",
                StorageBucketName = bucketName,
                B2Key = b2Key,
                B2BucketName = bucketName,
                B2FileId = b2FileId,
                OriginalFileName = Path.GetFileName(b2Key),
                DateUploaded = DateTime.UtcNow,
                DateModified = DateTime.UtcNow,
                CreatedBy = "B2_SYNC"
            };
            
            // Extract category from B2 key path
            var pathParts = b2Key.Split('/');
            if (pathParts.Length > 1)
            {
                image.Category = pathParts[0].ToLower();
            }
            else
            {
                image.Category = "gallery"; // Default category
            }
            
            // Update from metadata if provided
            if (metadata != null)
            {
                UpdateImageMetadataFromB2(image, metadata);
            }
            
            return image;
        }

        /// <summary>
        /// Update image metadata from B2 metadata dictionary
        /// </summary>
        private void UpdateImageMetadataFromB2(CatGalleryImage image, Dictionary<string, string> metadata)
        {
            foreach (var kvp in metadata)
            {
                switch (kvp.Key.ToLower())
                {
                    case "catname":
                    case "cat-name":
                        image.CatName = kvp.Value;
                        break;
                    case "catid":
                    case "cat-id":
                        image.CatId = kvp.Value;
                        break;
                    case "title":
                        image.Title = kvp.Value;
                        break;
                    case "description":
                        image.Description = kvp.Value;
                        break;
                    case "breed":
                        image.Breed = kvp.Value;
                        break;
                    case "gender":
                        image.Gender = kvp.Value;
                        break;
                    case "bloodline":
                        image.Bloodline = kvp.Value;
                        break;
                    case "tags":
                        image.Tags = kvp.Value;
                        break;
                    case "age":
                    case "ageatphoto":
                        image.AgeAtPhoto = kvp.Value;
                        break;
                    case "datetaken":
                        if (DateTime.TryParse(kvp.Value, out var dateTaken))
                        {
                            image.DateTaken = dateTaken;
                        }
                        break;
                    case "width":
                        if (int.TryParse(kvp.Value, out var width))
                        {
                            image.Width = width;
                        }
                        break;
                    case "height":
                        if (int.TryParse(kvp.Value, out var height))
                        {
                            image.Height = height;
                        }
                        break;
                    case "filesize":
                        if (long.TryParse(kvp.Value, out var fileSize))
                        {
                            image.FileSize = fileSize;
                        }
                        break;
                    case "contenttype":
                        image.ContentType = kvp.Value;
                        break;
                }
            }
        }

        /// <summary>
        /// Log sync operation to database
        /// </summary>
        private async Task LogSyncOperationAsync(string b2Key, SyncOperation operation, string status, string? errorMessage)
        {
            try
            {
                var syncLog = new B2SyncLog
                {
                    B2Key = b2Key,
                    Operation = operation.ToString(),
                    Status = status,
                    ErrorMessage = errorMessage,
                    SyncedAt = DateTime.UtcNow
                };

                _context.B2SyncLogs.Add(syncLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log sync operation for {B2Key}", b2Key);
                // Don't throw - logging failure shouldn't break sync
            }
        }

        // Additional methods for full sync, verification, and monitoring
        public async Task<FullSyncResult> PerformFullSyncAsync(string? category = null, bool dryRun = false)
        {
            var result = new FullSyncResult
            {
                StartTime = DateTime.UtcNow,
                WasDryRun = dryRun,
                Category = category
            };

            _logger.LogInformation("Starting full sync - Category: {Category}, DryRun: {DryRun}", category, dryRun);

            try
            {
                // This would require integration with B2 API to list files
                // For now, return a placeholder result
                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = result.EndTime.Value - result.StartTime;

                _logger.LogInformation("Full sync completed - processed {DatabaseEntries} database entries, {B2Files} B2 files",
                    result.DatabaseEntriesProcessed, result.B2FilesProcessed);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Full sync failed");
                result.Errors.Add(ex.Message);
            }

            return result;
        }

        public async Task<VerificationResult> VerifyConsistencyAsync(string? category = null, int batchSize = 100)
        {
            var result = new VerificationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Starting consistency verification for category: {Category}", category);

                // Get database entries to verify
                var query = _context.CatGalleryImages.AsQueryable();
                if (!string.IsNullOrEmpty(category))
                {
                    query = query.Where(img => img.Category.ToLower() == category.ToLower());
                }

                var images = await query.Where(img => img.IsActive).ToListAsync();
                result.TotalChecked = images.Count;

                // For each image, verify it exists in B2 (would need B2 API integration)
                foreach (var image in images)
                {
                    // Placeholder verification logic
                    if (string.IsNullOrEmpty(image.StorageKey))
                    {
                        result.InconsistentEntries++;
                        result.Details.Add(new InconsistencyDetail
                        {
                            DatabaseId = image.Id,
                            B2Key = image.StorageKey,
                            IssueType = "MissingStorageKey",
                            Description = "Database entry has no storage key"
                        });
                    }
                    else
                    {
                        result.ConsistentEntries++;
                    }
                }

                result.Duration = stopwatch.Elapsed;

                _logger.LogInformation("Consistency verification completed: {Consistent}/{Total} consistent",
                    result.ConsistentEntries, result.TotalChecked);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Consistency verification failed");
                result.Issues.Add($"Verification failed: {ex.Message}");
            }

            return result;
        }

        public async Task<List<CatGalleryImage>> FindOrphanedDatabaseEntriesAsync()
        {
            try
            {
                // Find database entries that might be orphaned
                return await _context.CatGalleryImages
                    .Where(img => img.IsActive &&
                                  (string.IsNullOrEmpty(img.StorageKey) ||
                                   string.IsNullOrEmpty(img.StorageBucketName)))
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to find orphaned database entries");
                return new List<CatGalleryImage>();
            }
        }

        public async Task<List<string>> FindOrphanedB2FilesAsync(string? category = null)
        {
            // This would require B2 API integration to list files and compare with database
            // For now, return empty list
            return new List<string>();
        }

        public async Task<SyncStatus> GetSyncStatusAsync()
        {
            var status = new SyncStatus();

            try
            {
                var recentLogs = await _context.B2SyncLogs
                    .Where(log => log.SyncedAt >= DateTime.UtcNow.AddHours(-24))
                    .ToListAsync();

                status.PendingOperations = 0; // Would need a queue system for this
                status.FailedOperations = recentLogs.Count(log => log.Status == "FAILED");

                var successfulOps = recentLogs.Where(log => log.Status == "SUCCESS").ToList();
                var failedOps = recentLogs.Where(log => log.Status == "FAILED").ToList();

                if (successfulOps.Any())
                {
                    status.LastSuccessfulSync = successfulOps.Max(log => log.SyncedAt);
                }

                if (failedOps.Any())
                {
                    status.LastFailedSync = failedOps.Max(log => log.SyncedAt);
                    status.LastError = failedOps.OrderByDescending(log => log.SyncedAt).First().ErrorMessage;
                }

                var totalOps = recentLogs.Count;
                status.SuccessRate = totalOps > 0 ? (double)successfulOps.Count / totalOps : 1.0;

                status.OperationCounts = recentLogs
                    .GroupBy(log => log.Operation)
                    .ToDictionary(g => g.Key, g => g.Count());

                status.IsHealthy = status.SuccessRate > 0.95 && status.FailedOperations < 10;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get sync status");
                status.IsHealthy = false;
                status.LastError = ex.Message;
            }

            return status;
        }

        public async Task<List<B2SyncLog>> GetRecentSyncLogsAsync(int count = 50)
        {
            try
            {
                return await _context.B2SyncLogs
                    .OrderByDescending(log => log.SyncedAt)
                    .Take(count)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get recent sync logs");
                return new List<B2SyncLog>();
            }
        }

        public async Task<List<B2SyncLog>> GetFailedSyncOperationsAsync()
        {
            try
            {
                return await _context.B2SyncLogs
                    .Where(log => log.Status == "FAILED")
                    .OrderByDescending(log => log.SyncedAt)
                    .Take(100)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get failed sync operations");
                return new List<B2SyncLog>();
            }
        }

        public async Task<BatchSyncResult> RetryFailedOperationsAsync()
        {
            var failedLogs = await GetFailedSyncOperationsAsync();
            var retryItems = failedLogs.Select(log => new B2SyncItem
            {
                B2Key = log.B2Key,
                Operation = Enum.Parse<SyncOperation>(log.Operation),
                BucketName = "yendor" // Default bucket
            }).ToList();

            return await SyncBatchAsync(retryItems);
        }

        public async Task CleanupOldSyncLogsAsync(TimeSpan olderThan)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow - olderThan;
                var oldLogs = await _context.B2SyncLogs
                    .Where(log => log.SyncedAt < cutoffDate)
                    .ToListAsync();

                if (oldLogs.Any())
                {
                    _context.B2SyncLogs.RemoveRange(oldLogs);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Cleaned up {Count} old sync logs older than {CutoffDate}",
                        oldLogs.Count, cutoffDate);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup old sync logs");
            }
        }

        public async Task<SyncHealthCheck> GetHealthCheckAsync()
        {
            var healthCheck = new SyncHealthCheck();

            try
            {
                // Test database connectivity
                var dbStartTime = DateTime.UtcNow;
                var recentLogCount = await _context.B2SyncLogs
                    .Where(log => log.SyncedAt >= DateTime.UtcNow.AddMinutes(-5))
                    .CountAsync();
                healthCheck.DatabaseResponseTime = DateTime.UtcNow - dbStartTime;
                healthCheck.DatabaseConnected = true;

                // Get sync status
                var status = await GetSyncStatusAsync();
                healthCheck.PendingOperations = status.PendingOperations;
                healthCheck.FailedOperations = status.FailedOperations;

                // Determine health
                healthCheck.IsHealthy = healthCheck.DatabaseConnected &&
                                       status.IsHealthy &&
                                       healthCheck.DatabaseResponseTime < TimeSpan.FromSeconds(2);

                healthCheck.Status = healthCheck.IsHealthy ? "Healthy" : "Degraded";

                if (!healthCheck.IsHealthy)
                {
                    if (!healthCheck.DatabaseConnected)
                        healthCheck.Issues.Add("Database connection failed");
                    if (healthCheck.FailedOperations > 10)
                        healthCheck.Issues.Add($"High number of failed operations: {healthCheck.FailedOperations}");
                    if (healthCheck.DatabaseResponseTime > TimeSpan.FromSeconds(2))
                        healthCheck.Issues.Add("Slow database response time");
                }

                healthCheck.Metrics = new Dictionary<string, object>
                {
                    ["RecentSyncOperations"] = recentLogCount,
                    ["SuccessRate"] = status.SuccessRate,
                    ["DatabaseResponseTimeMs"] = healthCheck.DatabaseResponseTime.TotalMilliseconds
                };
            }
            catch (Exception ex)
            {
                healthCheck.IsHealthy = false;
                healthCheck.Status = "Unhealthy";
                healthCheck.Issues.Add($"Health check failed: {ex.Message}");
                _logger.LogError(ex, "Sync service health check failed");
            }

            return healthCheck;
        }
    }
}
