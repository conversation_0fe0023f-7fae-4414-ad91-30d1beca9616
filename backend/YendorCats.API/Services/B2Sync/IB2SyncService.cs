using YendorCats.API.Models;

namespace YendorCats.API.Services.B2Sync
{
    /// <summary>
    /// Interface for Backblaze B2 synchronization service
    /// Maintains consistency between database metadata and B2 storage
    /// </summary>
    public interface IB2SyncService
    {
        // Real-time sync operations
        /// <summary>
        /// Sync a single image upload to database
        /// Called immediately after B2 upload
        /// </summary>
        Task<SyncResult> SyncImageUploadAsync(string b2Key, string bucketName, string? b2FileId = null, 
            Dictionary<string, string>? metadata = null);
        
        /// <summary>
        /// Sync image deletion from database
        /// Called when image is deleted from B2
        /// </summary>
        Task<SyncResult> SyncImageDeletionAsync(string b2Key);
        
        /// <summary>
        /// Sync metadata update for existing image
        /// Called when B2 metadata is updated
        /// </summary>
        Task<SyncResult> SyncMetadataUpdateAsync(string b2Key, Dictionary<string, string> metadata);
        
        // Batch sync operations
        /// <summary>
        /// Sync multiple images in a batch operation
        /// Optimized for bulk uploads/migrations
        /// </summary>
        Task<BatchSyncResult> SyncBatchAsync(IEnumerable<B2SyncItem> items);
        
        /// <summary>
        /// Full synchronization between B2 and database
        /// Use with caution - can be resource intensive
        /// </summary>
        Task<FullSyncResult> PerformFullSyncAsync(string? category = null, bool dryRun = false);
        
        // Verification operations
        /// <summary>
        /// Verify that database entries match B2 storage
        /// </summary>
        Task<VerificationResult> VerifyConsistencyAsync(string? category = null, int batchSize = 100);
        
        /// <summary>
        /// Find orphaned database entries (no corresponding B2 file)
        /// </summary>
        Task<List<CatGalleryImage>> FindOrphanedDatabaseEntriesAsync();
        
        /// <summary>
        /// Find orphaned B2 files (no corresponding database entry)
        /// </summary>
        Task<List<string>> FindOrphanedB2FilesAsync(string? category = null);
        
        // Monitoring and health
        /// <summary>
        /// Get sync status and statistics
        /// </summary>
        Task<SyncStatus> GetSyncStatusAsync();
        
        /// <summary>
        /// Get recent sync operations
        /// </summary>
        Task<List<B2SyncLog>> GetRecentSyncLogsAsync(int count = 50);
        
        /// <summary>
        /// Get failed sync operations that need retry
        /// </summary>
        Task<List<B2SyncLog>> GetFailedSyncOperationsAsync();
        
        /// <summary>
        /// Retry failed sync operations
        /// </summary>
        Task<BatchSyncResult> RetryFailedOperationsAsync();
        
        // Configuration and maintenance
        /// <summary>
        /// Clean up old sync logs
        /// </summary>
        Task CleanupOldSyncLogsAsync(TimeSpan olderThan);
        
        /// <summary>
        /// Get sync service health check
        /// </summary>
        Task<SyncHealthCheck> GetHealthCheckAsync();
    }
    
    /// <summary>
    /// Item for batch sync operations
    /// </summary>
    public class B2SyncItem
    {
        public string B2Key { get; set; } = string.Empty;
        public string BucketName { get; set; } = string.Empty;
        public string? B2FileId { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
        public SyncOperation Operation { get; set; } = SyncOperation.Insert;
        public long? DatabaseId { get; set; }
    }
    
    /// <summary>
    /// Sync operation types
    /// </summary>
    public enum SyncOperation
    {
        Insert,
        Update,
        Delete,
        Verify
    }
    
    /// <summary>
    /// Result of a single sync operation
    /// </summary>
    public class SyncResult
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public long? DatabaseId { get; set; }
        public string? B2Key { get; set; }
        public SyncOperation Operation { get; set; }
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object>? AdditionalData { get; set; }
    }
    
    /// <summary>
    /// Result of batch sync operations
    /// </summary>
    public class BatchSyncResult
    {
        public int TotalItems { get; set; }
        public int SuccessfulItems { get; set; }
        public int FailedItems { get; set; }
        public List<SyncResult> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan TotalDuration { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsComplete => EndTime.HasValue;
        public double SuccessRate => TotalItems > 0 ? (double)SuccessfulItems / TotalItems : 0;
    }
    
    /// <summary>
    /// Result of full synchronization
    /// </summary>
    public class FullSyncResult : BatchSyncResult
    {
        public int DatabaseEntriesProcessed { get; set; }
        public int B2FilesProcessed { get; set; }
        public int OrphanedDatabaseEntries { get; set; }
        public int OrphanedB2Files { get; set; }
        public int MetadataMismatches { get; set; }
        public bool WasDryRun { get; set; }
        public string? Category { get; set; }
    }
    
    /// <summary>
    /// Result of consistency verification
    /// </summary>
    public class VerificationResult
    {
        public int TotalChecked { get; set; }
        public int ConsistentEntries { get; set; }
        public int InconsistentEntries { get; set; }
        public int MissingInB2 { get; set; }
        public int MissingInDatabase { get; set; }
        public int MetadataMismatches { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<InconsistencyDetail> Details { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool IsHealthy => InconsistentEntries == 0;
    }
    
    /// <summary>
    /// Details of a specific inconsistency
    /// </summary>
    public class InconsistencyDetail
    {
        public string B2Key { get; set; } = string.Empty;
        public long? DatabaseId { get; set; }
        public string IssueType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object>? AdditionalInfo { get; set; }
    }
    
    /// <summary>
    /// Current sync status
    /// </summary>
    public class SyncStatus
    {
        public bool IsHealthy { get; set; }
        public int PendingOperations { get; set; }
        public int FailedOperations { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public DateTime? LastFailedSync { get; set; }
        public string? LastError { get; set; }
        public Dictionary<string, int> OperationCounts { get; set; } = new();
        public Dictionary<string, TimeSpan> AverageOperationTimes { get; set; } = new();
        public double SuccessRate { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Health check for sync service
    /// </summary>
    public class SyncHealthCheck
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public bool DatabaseConnected { get; set; }
        public bool B2Connected { get; set; }
        public TimeSpan DatabaseResponseTime { get; set; }
        public TimeSpan B2ResponseTime { get; set; }
        public int PendingOperations { get; set; }
        public int FailedOperations { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.UtcNow;
    }
}
