using YendorCats.API.Models;

namespace YendorCats.API.Services.Performance
{
    /// <summary>
    /// Interface for thumbnail generation and management service
    /// Optimizes image loading performance through pre-generated thumbnails
    /// </summary>
    public interface IThumbnailService
    {
        // Thumbnail generation
        /// <summary>
        /// Generate thumbnail for a single image
        /// </summary>
        Task<ThumbnailResult> GenerateThumbnailAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium);
        
        /// <summary>
        /// Generate thumbnail from image stream
        /// </summary>
        Task<ThumbnailResult> GenerateThumbnailFromStreamAsync(Stream imageStream, string originalFileName, 
            ThumbnailSize size = ThumbnailSize.Medium);
        
        /// <summary>
        /// Generate multiple thumbnail sizes for an image
        /// </summary>
        Task<List<ThumbnailResult>> GenerateMultipleThumbnailsAsync(long imageId, 
            params ThumbnailSize[] sizes);
        
        // Batch operations
        /// <summary>
        /// Generate thumbnails for multiple images
        /// </summary>
        Task<BatchThumbnailResult> GenerateBatchThumbnailsAsync(IEnumerable<long> imageIds, 
            ThumbnailSize size = ThumbnailSize.Medium);
        
        /// <summary>
        /// Generate missing thumbnails for a category
        /// </summary>
        Task<BatchThumbnailResult> GenerateMissingThumbnailsAsync(string? category = null, 
            int batchSize = 50);
        
        // Thumbnail management
        /// <summary>
        /// Get thumbnail URL for an image
        /// </summary>
        Task<string?> GetThumbnailUrlAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium);
        
        /// <summary>
        /// Check if thumbnail exists for an image
        /// </summary>
        Task<bool> ThumbnailExistsAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium);
        
        /// <summary>
        /// Delete thumbnail for an image
        /// </summary>
        Task<bool> DeleteThumbnailAsync(long imageId, ThumbnailSize size = ThumbnailSize.Medium);
        
        /// <summary>
        /// Delete all thumbnails for an image
        /// </summary>
        Task<bool> DeleteAllThumbnailsAsync(long imageId);
        
        // Maintenance operations
        /// <summary>
        /// Clean up orphaned thumbnails (no corresponding image)
        /// </summary>
        Task<CleanupResult> CleanupOrphanedThumbnailsAsync();
        
        /// <summary>
        /// Regenerate corrupted or outdated thumbnails
        /// </summary>
        Task<BatchThumbnailResult> RegenerateThumbnailsAsync(string? category = null, 
            bool forceRegenerate = false);
        
        // Statistics and monitoring
        /// <summary>
        /// Get thumbnail generation statistics
        /// </summary>
        Task<ThumbnailStatistics> GetStatisticsAsync();
        
        /// <summary>
        /// Get health check for thumbnail service
        /// </summary>
        Task<ThumbnailHealthCheck> GetHealthCheckAsync();
    }
    
    /// <summary>
    /// Thumbnail size options
    /// </summary>
    public enum ThumbnailSize
    {
        Small = 150,    // 150px max dimension
        Medium = 300,   // 300px max dimension
        Large = 600     // 600px max dimension
    }
    
    /// <summary>
    /// Result of thumbnail generation
    /// </summary>
    public class ThumbnailResult
    {
        public bool Success { get; set; }
        public string? ThumbnailUrl { get; set; }
        public string? StorageKey { get; set; }
        public ThumbnailSize Size { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public long FileSize { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan GenerationTime { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Result of batch thumbnail operations
    /// </summary>
    public class BatchThumbnailResult
    {
        public int TotalImages { get; set; }
        public int SuccessfulThumbnails { get; set; }
        public int FailedThumbnails { get; set; }
        public int SkippedThumbnails { get; set; }
        public List<ThumbnailResult> Results { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan TotalDuration { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public double SuccessRate => TotalImages > 0 ? (double)SuccessfulThumbnails / TotalImages : 0;
        public bool IsComplete => EndTime.HasValue;
    }
    
    /// <summary>
    /// Result of cleanup operations
    /// </summary>
    public class CleanupResult
    {
        public int OrphanedThumbnailsFound { get; set; }
        public int OrphanedThumbnailsDeleted { get; set; }
        public long SpaceFreed { get; set; }
        public List<string> DeletedFiles { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Thumbnail service statistics
    /// </summary>
    public class ThumbnailStatistics
    {
        public int TotalImages { get; set; }
        public int ImagesWithThumbnails { get; set; }
        public int ImagesWithoutThumbnails { get; set; }
        public Dictionary<ThumbnailSize, int> ThumbnailsBySize { get; set; } = new();
        public Dictionary<string, int> ThumbnailsByCategory { get; set; } = new();
        public long TotalThumbnailStorage { get; set; }
        public TimeSpan AverageGenerationTime { get; set; }
        public int ThumbnailsGeneratedToday { get; set; }
        public int ThumbnailsGeneratedThisWeek { get; set; }
        public double ThumbnailCoverage => TotalImages > 0 ? (double)ImagesWithThumbnails / TotalImages : 0;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Health check for thumbnail service
    /// </summary>
    public class ThumbnailHealthCheck
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public bool StorageAccessible { get; set; }
        public bool ImageProcessingWorking { get; set; }
        public TimeSpan StorageResponseTime { get; set; }
        public TimeSpan ProcessingResponseTime { get; set; }
        public int PendingGenerations { get; set; }
        public int FailedGenerations { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.UtcNow;
    }
}
