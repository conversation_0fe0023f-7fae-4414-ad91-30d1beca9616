using System.Collections.Concurrent;
using System.Text.Json;
using Microsoft.Extensions.Caching.Memory;

namespace YendorCats.API.Services.Performance
{
    /// <summary>
    /// High-performance metrics collection and monitoring service
    /// Tracks system performance to ensure 85-90% improvement targets are met
    /// </summary>
    public class PerformanceMetricsService : IPerformanceMetricsService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<PerformanceMetricsService> _logger;
        
        // In-memory storage for performance data (in production, use persistent storage)
        private static readonly ConcurrentQueue<ApiRequestMetric> _apiMetrics = new();
        private static readonly ConcurrentQueue<DatabaseQueryMetric> _dbMetrics = new();
        private static readonly ConcurrentQueue<CacheOperationMetric> _cacheMetrics = new();
        private static readonly ConcurrentQueue<ImageProcessingMetric> _imageMetrics = new();
        
        // Performance baselines (pre-optimization values)
        private static readonly Dictionary<string, TimeSpan> _baselineResponseTimes = new()
        {
            { "/api/v2/gallery/studs", TimeSpan.FromSeconds(3.5) },
            { "/api/v2/gallery/queens", TimeSpan.FromSeconds(3.2) },
            { "/api/v2/gallery/kittens", TimeSpan.FromSeconds(4.1) },
            { "/api/v2/gallery/gallery", TimeSpan.FromSeconds(2.8) }
        };
        
        // SLA targets
        private static readonly Dictionary<string, TimeSpan> _slaTargets = new()
        {
            { "api_response_time", TimeSpan.FromMilliseconds(500) },
            { "database_query_time", TimeSpan.FromMilliseconds(200) },
            { "cache_response_time", TimeSpan.FromMilliseconds(50) }
        };
        
        // Configuration
        private const int MAX_METRICS_IN_MEMORY = 10000;
        private const int CLEANUP_BATCH_SIZE = 1000;
        private readonly TimeSpan _defaultTimeWindow = TimeSpan.FromHours(1);

        public PerformanceMetricsService(IMemoryCache cache, ILogger<PerformanceMetricsService> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public async Task RecordApiRequestAsync(string endpoint, string method, TimeSpan duration, 
            bool success, string? cacheSource = null)
        {
            var metric = new ApiRequestMetric
            {
                Endpoint = endpoint,
                Method = method,
                Duration = duration,
                Success = success,
                CacheSource = cacheSource,
                Timestamp = DateTime.UtcNow
            };
            
            _apiMetrics.Enqueue(metric);
            await CleanupOldMetricsIfNeeded();
            
            // Log slow requests
            if (duration > TimeSpan.FromSeconds(1))
            {
                _logger.LogWarning("Slow API request: {Method} {Endpoint} took {Duration}ms", 
                    method, endpoint, duration.TotalMilliseconds);
            }
        }

        public async Task RecordDatabaseQueryAsync(string queryType, TimeSpan duration, int resultCount, 
            bool success, string? additionalInfo = null)
        {
            var metric = new DatabaseQueryMetric
            {
                QueryType = queryType,
                Duration = duration,
                ResultCount = resultCount,
                Success = success,
                AdditionalInfo = additionalInfo,
                Timestamp = DateTime.UtcNow
            };
            
            _dbMetrics.Enqueue(metric);
            await CleanupOldMetricsIfNeeded();
            
            // Log slow queries
            if (duration > TimeSpan.FromMilliseconds(500))
            {
                _logger.LogWarning("Slow database query: {QueryType} took {Duration}ms, returned {ResultCount} results", 
                    queryType, duration.TotalMilliseconds, resultCount);
            }
        }

        public async Task RecordCacheOperationAsync(string operation, string cacheType, TimeSpan duration, 
            bool hit, string? key = null)
        {
            var metric = new CacheOperationMetric
            {
                Operation = operation,
                CacheType = cacheType,
                Duration = duration,
                Hit = hit,
                Key = key,
                Timestamp = DateTime.UtcNow
            };
            
            _cacheMetrics.Enqueue(metric);
            await CleanupOldMetricsIfNeeded();
        }

        public async Task RecordImageProcessingAsync(string operation, TimeSpan duration, bool success, 
            long? fileSize = null, string? format = null)
        {
            var metric = new ImageProcessingMetric
            {
                Operation = operation,
                Duration = duration,
                Success = success,
                FileSize = fileSize,
                Format = format,
                Timestamp = DateTime.UtcNow
            };
            
            _imageMetrics.Enqueue(metric);
            await CleanupOldMetricsIfNeeded();
        }

        public async Task<SystemPerformanceMetrics> GetSystemMetricsAsync(TimeSpan? timeWindow = null)
        {
            var window = timeWindow ?? _defaultTimeWindow;
            var cutoff = DateTime.UtcNow - window;
            
            var recentApiMetrics = _apiMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            
            if (!recentApiMetrics.Any())
            {
                return new SystemPerformanceMetrics { TimeWindow = window };
            }
            
            var responseTimes = recentApiMetrics.Select(m => m.Duration).OrderBy(d => d).ToList();
            var successfulRequests = recentApiMetrics.Count(m => m.Success);
            var totalRequests = recentApiMetrics.Count;
            
            // Calculate cache hit rate
            var recentCacheMetrics = _cacheMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            var cacheHitRate = recentCacheMetrics.Any() 
                ? (double)recentCacheMetrics.Count(m => m.Hit) / recentCacheMetrics.Count 
                : 0;
            
            return new SystemPerformanceMetrics
            {
                AverageResponseTime = TimeSpan.FromMilliseconds(responseTimes.Average(t => t.TotalMilliseconds)),
                MedianResponseTime = responseTimes[responseTimes.Count / 2],
                P95ResponseTime = responseTimes[(int)(responseTimes.Count * 0.95)],
                P99ResponseTime = responseTimes[(int)(responseTimes.Count * 0.99)],
                ErrorRate = totalRequests > 0 ? (double)(totalRequests - successfulRequests) / totalRequests : 0,
                ThroughputPerSecond = totalRequests / window.TotalSeconds,
                CacheHitRate = cacheHitRate,
                TotalRequests = totalRequests,
                SuccessfulRequests = successfulRequests,
                FailedRequests = totalRequests - successfulRequests,
                TimeWindow = window
            };
        }

        public async Task<List<EndpointPerformanceMetrics>> GetEndpointMetricsAsync(TimeSpan? timeWindow = null)
        {
            var window = timeWindow ?? _defaultTimeWindow;
            var cutoff = DateTime.UtcNow - window;
            
            var recentMetrics = _apiMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            
            return recentMetrics
                .GroupBy(m => new { m.Endpoint, m.Method })
                .Select(g => new EndpointPerformanceMetrics
                {
                    Endpoint = g.Key.Endpoint,
                    Method = g.Key.Method,
                    RequestCount = g.Count(),
                    AverageResponseTime = TimeSpan.FromMilliseconds(g.Average(m => m.Duration.TotalMilliseconds)),
                    MinResponseTime = g.Min(m => m.Duration),
                    MaxResponseTime = g.Max(m => m.Duration),
                    ErrorRate = (double)g.Count(m => !m.Success) / g.Count(),
                    CacheHitRate = (double)g.Count(m => !string.IsNullOrEmpty(m.CacheSource)) / g.Count(),
                    RecentResponseTimes = g.OrderByDescending(m => m.Timestamp).Take(10).Select(m => m.Duration).ToList()
                })
                .OrderByDescending(e => e.RequestCount)
                .ToList();
        }

        public async Task<CachePerformanceMetrics> GetCacheMetricsAsync(TimeSpan? timeWindow = null)
        {
            var window = timeWindow ?? _defaultTimeWindow;
            var cutoff = DateTime.UtcNow - window;
            
            var recentMetrics = _cacheMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            
            if (!recentMetrics.Any())
            {
                return new CachePerformanceMetrics();
            }
            
            var memoryMetrics = recentMetrics.Where(m => m.CacheType == "memory").ToList();
            var distributedMetrics = recentMetrics.Where(m => m.CacheType == "distributed").ToList();
            
            return new CachePerformanceMetrics
            {
                OverallHitRate = (double)recentMetrics.Count(m => m.Hit) / recentMetrics.Count,
                MemoryCacheHitRate = memoryMetrics.Any() ? (double)memoryMetrics.Count(m => m.Hit) / memoryMetrics.Count : 0,
                DistributedCacheHitRate = distributedMetrics.Any() ? (double)distributedMetrics.Count(m => m.Hit) / distributedMetrics.Count : 0,
                AverageMemoryResponseTime = memoryMetrics.Any() ? TimeSpan.FromMilliseconds(memoryMetrics.Average(m => m.Duration.TotalMilliseconds)) : TimeSpan.Zero,
                AverageDistributedResponseTime = distributedMetrics.Any() ? TimeSpan.FromMilliseconds(distributedMetrics.Average(m => m.Duration.TotalMilliseconds)) : TimeSpan.Zero,
                TotalCacheOperations = recentMetrics.Count,
                CacheHits = recentMetrics.Count(m => m.Hit),
                CacheMisses = recentMetrics.Count(m => !m.Hit)
            };
        }

        public async Task<DatabasePerformanceMetrics> GetDatabaseMetricsAsync(TimeSpan? timeWindow = null)
        {
            var window = timeWindow ?? _defaultTimeWindow;
            var cutoff = DateTime.UtcNow - window;
            
            var recentMetrics = _dbMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            
            if (!recentMetrics.Any())
            {
                return new DatabasePerformanceMetrics();
            }
            
            return new DatabasePerformanceMetrics
            {
                AverageQueryTime = TimeSpan.FromMilliseconds(recentMetrics.Average(m => m.Duration.TotalMilliseconds)),
                SlowestQueryTime = recentMetrics.Max(m => m.Duration),
                TotalQueries = recentMetrics.Count,
                SuccessfulQueries = recentMetrics.Count(m => m.Success),
                FailedQueries = recentMetrics.Count(m => !m.Success),
                ErrorRate = (double)recentMetrics.Count(m => !m.Success) / recentMetrics.Count,
                QueryTimesByType = recentMetrics
                    .GroupBy(m => m.QueryType)
                    .ToDictionary(g => g.Key, g => TimeSpan.FromMilliseconds(g.Average(m => m.Duration.TotalMilliseconds))),
                QueryCountsByType = recentMetrics
                    .GroupBy(m => m.QueryType)
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        public async Task<PerformanceComparisonResult> CompareWithBaselineAsync()
        {
            var currentMetrics = await GetSystemMetricsAsync(TimeSpan.FromHours(1));
            var endpointMetrics = await GetEndpointMetricsAsync(TimeSpan.FromHours(1));

            var result = new PerformanceComparisonResult();

            // Calculate overall improvement
            var baselineAverage = _baselineResponseTimes.Values.Average(t => t.TotalMilliseconds);
            var currentAverage = currentMetrics.AverageResponseTime.TotalMilliseconds;

            result.BaselineResponseTime = TimeSpan.FromMilliseconds(baselineAverage);
            result.CurrentResponseTime = currentMetrics.AverageResponseTime;
            result.ImprovementPercentage = ((baselineAverage - currentAverage) / baselineAverage) * 100;
            result.MeetsTarget = result.ImprovementPercentage >= 85; // 85-90% improvement target

            // Calculate improvement by endpoint
            foreach (var endpoint in endpointMetrics)
            {
                var key = endpoint.Endpoint;
                if (_baselineResponseTimes.ContainsKey(key))
                {
                    var baselineTime = _baselineResponseTimes[key].TotalMilliseconds;
                    var currentTime = endpoint.AverageResponseTime.TotalMilliseconds;
                    var improvement = ((baselineTime - currentTime) / baselineTime) * 100;

                    result.ImprovementByCategory[key] = improvement;

                    if (improvement >= 85)
                        result.ImprovementAreas.Add($"{key}: {improvement:F1}% improvement");
                    else if (improvement < 0)
                        result.RegressionAreas.Add($"{key}: {Math.Abs(improvement):F1}% regression");
                }
            }

            return result;
        }

        public async Task<PerformanceTrendAnalysis> GetPerformanceTrendsAsync(TimeSpan timeWindow)
        {
            var cutoff = DateTime.UtcNow - timeWindow;
            var recentMetrics = _apiMetrics.Where(m => m.Timestamp >= cutoff).OrderBy(m => m.Timestamp).ToList();

            var analysis = new PerformanceTrendAnalysis();

            if (recentMetrics.Count < 10) // Need minimum data points for trend analysis
            {
                analysis.OverallTrend = TrendDirection.Unknown;
                return analysis;
            }

            // Group metrics into time buckets for trend analysis
            var bucketSize = timeWindow.TotalMinutes / 20; // 20 data points
            var buckets = recentMetrics
                .GroupBy(m => (int)((m.Timestamp - cutoff).TotalMinutes / bucketSize))
                .Select(g => new PerformanceDataPoint
                {
                    Timestamp = cutoff.AddMinutes(g.Key * bucketSize),
                    ResponseTime = TimeSpan.FromMilliseconds(g.Average(m => m.Duration.TotalMilliseconds)),
                    ThroughputPerSecond = g.Count() / (bucketSize * 60),
                    ErrorRate = (double)g.Count(m => !m.Success) / g.Count()
                })
                .OrderBy(dp => dp.Timestamp)
                .ToList();

            analysis.DataPoints = buckets;

            // Calculate trend slope for response time
            if (buckets.Count >= 2)
            {
                var firstHalf = buckets.Take(buckets.Count / 2).Average(dp => dp.ResponseTime.TotalMilliseconds);
                var secondHalf = buckets.Skip(buckets.Count / 2).Average(dp => dp.ResponseTime.TotalMilliseconds);

                analysis.TrendSlope = (secondHalf - firstHalf) / firstHalf;

                if (analysis.TrendSlope < -0.1)
                    analysis.OverallTrend = TrendDirection.Improving;
                else if (analysis.TrendSlope > 0.1)
                    analysis.OverallTrend = TrendDirection.Degrading;
                else
                    analysis.OverallTrend = TrendDirection.Stable;
            }

            return analysis;
        }

        public async Task<SlaComplianceResult> CheckSlaComplianceAsync()
        {
            var systemMetrics = await GetSystemMetricsAsync(TimeSpan.FromHours(1));
            var result = new SlaComplianceResult { TimeWindow = TimeSpan.FromHours(1) };

            // Check API response time SLA
            var apiTarget = _slaTargets["api_response_time"];
            var apiCompliant = systemMetrics.P95ResponseTime <= apiTarget;
            result.ComplianceByMetric["api_response_time"] = apiCompliant;

            if (!apiCompliant)
            {
                result.Violations.Add(new SlaViolation
                {
                    Metric = "API Response Time (P95)",
                    Threshold = apiTarget.TotalMilliseconds + "ms",
                    ActualValue = systemMetrics.P95ResponseTime.TotalMilliseconds + "ms",
                    ViolationTime = DateTime.UtcNow,
                    Severity = "High"
                });
            }

            // Check error rate SLA (should be < 1%)
            var errorRateCompliant = systemMetrics.ErrorRate < 0.01;
            result.ComplianceByMetric["error_rate"] = errorRateCompliant;

            if (!errorRateCompliant)
            {
                result.Violations.Add(new SlaViolation
                {
                    Metric = "Error Rate",
                    Threshold = "1%",
                    ActualValue = (systemMetrics.ErrorRate * 100).ToString("F2") + "%",
                    ViolationTime = DateTime.UtcNow,
                    Severity = "Critical"
                });
            }

            result.IsCompliant = result.Violations.Count == 0;
            result.CompliancePercentage = (double)result.ComplianceByMetric.Count(kvp => kvp.Value) / result.ComplianceByMetric.Count;

            return result;
        }

        public async Task<List<PerformanceAlert>> GetActiveAlertsAsync()
        {
            var alerts = new List<PerformanceAlert>();
            var slaResult = await CheckSlaComplianceAsync();

            foreach (var violation in slaResult.Violations)
            {
                alerts.Add(new PerformanceAlert
                {
                    Type = "SLA_VIOLATION",
                    Message = $"SLA violation: {violation.Metric} - {violation.ActualValue} exceeds threshold {violation.Threshold}",
                    Severity = violation.Severity,
                    CreatedAt = violation.ViolationTime,
                    Metadata = new Dictionary<string, object>
                    {
                        ["metric"] = violation.Metric,
                        ["threshold"] = violation.Threshold,
                        ["actual"] = violation.ActualValue
                    }
                });
            }

            return alerts;
        }

        public async Task<List<SlowOperationReport>> GetSlowestOperationsAsync(int count = 10)
        {
            var endpointMetrics = await GetEndpointMetricsAsync(TimeSpan.FromHours(1));

            return endpointMetrics
                .OrderByDescending(e => e.AverageResponseTime)
                .Take(count)
                .Select(e => new SlowOperationReport
                {
                    Operation = $"{e.Method} {e.Endpoint}",
                    Type = "API_ENDPOINT",
                    AverageTime = e.AverageResponseTime,
                    MaxTime = e.MaxResponseTime,
                    Occurrences = e.RequestCount,
                    ImpactScore = e.AverageResponseTime.TotalMilliseconds * e.RequestCount,
                    OptimizationSuggestions = GenerateOptimizationSuggestions(e)
                })
                .ToList();
        }

        private List<string> GenerateOptimizationSuggestions(EndpointPerformanceMetrics endpoint)
        {
            var suggestions = new List<string>();

            if (endpoint.CacheHitRate < 0.5)
                suggestions.Add("Improve caching strategy - current hit rate is low");

            if (endpoint.AverageResponseTime > TimeSpan.FromSeconds(1))
                suggestions.Add("Consider database query optimization");

            if (endpoint.ErrorRate > 0.05)
                suggestions.Add("Investigate and fix error conditions");

            return suggestions;
        }

        private async Task CleanupOldMetricsIfNeeded()
        {
            // Cleanup logic to prevent memory overflow
            if (_apiMetrics.Count > MAX_METRICS_IN_MEMORY)
            {
                for (int i = 0; i < CLEANUP_BATCH_SIZE && _apiMetrics.TryDequeue(out _); i++) { }
            }
            
            if (_dbMetrics.Count > MAX_METRICS_IN_MEMORY)
            {
                for (int i = 0; i < CLEANUP_BATCH_SIZE && _dbMetrics.TryDequeue(out _); i++) { }
            }
            
            if (_cacheMetrics.Count > MAX_METRICS_IN_MEMORY)
            {
                for (int i = 0; i < CLEANUP_BATCH_SIZE && _cacheMetrics.TryDequeue(out _); i++) { }
            }
            
            if (_imageMetrics.Count > MAX_METRICS_IN_MEMORY)
            {
                for (int i = 0; i < CLEANUP_BATCH_SIZE && _imageMetrics.TryDequeue(out _); i++) { }
            }
        }

        public async Task<PerformanceReport> GenerateReportAsync(TimeSpan timeWindow, ReportType reportType = ReportType.Summary)
        {
            var report = new PerformanceReport
            {
                Type = reportType,
                TimeWindow = timeWindow,
                SystemMetrics = await GetSystemMetricsAsync(timeWindow),
                EndpointMetrics = await GetEndpointMetricsAsync(timeWindow),
                CacheMetrics = await GetCacheMetricsAsync(timeWindow),
                DatabaseMetrics = await GetDatabaseMetricsAsync(timeWindow)
            };

            // Generate insights based on the data
            var comparison = await CompareWithBaselineAsync();
            if (comparison.MeetsTarget)
            {
                report.KeyInsights.Add($"Performance target achieved: {comparison.ImprovementPercentage:F1}% improvement over baseline");
            }
            else
            {
                report.KeyInsights.Add($"Performance target not met: {comparison.ImprovementPercentage:F1}% improvement (target: 85%)");
            }

            if (report.CacheMetrics.OverallHitRate > 0.8)
            {
                report.KeyInsights.Add($"Excellent cache performance: {report.CacheMetrics.OverallHitRate:P1} hit rate");
            }
            else if (report.CacheMetrics.OverallHitRate < 0.5)
            {
                report.Recommendations.Add("Improve caching strategy to increase hit rate");
            }

            return report;
        }

        public async Task<byte[]> ExportPerformanceDataAsync(TimeSpan timeWindow, ExportFormat format = ExportFormat.Json)
        {
            var report = await GenerateReportAsync(timeWindow, ReportType.Detailed);

            switch (format)
            {
                case ExportFormat.Json:
                    var json = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
                    return System.Text.Encoding.UTF8.GetBytes(json);

                case ExportFormat.Csv:
                    // Simple CSV export of endpoint metrics
                    var csv = "Endpoint,Method,RequestCount,AverageResponseTime,ErrorRate\n";
                    foreach (var endpoint in report.EndpointMetrics)
                    {
                        csv += $"{endpoint.Endpoint},{endpoint.Method},{endpoint.RequestCount},{endpoint.AverageResponseTime.TotalMilliseconds},{endpoint.ErrorRate:P2}\n";
                    }
                    return System.Text.Encoding.UTF8.GetBytes(csv);

                default:
                    throw new NotSupportedException($"Export format {format} is not supported");
            }
        }

        public async Task CleanupOldDataAsync(TimeSpan olderThan)
        {
            var cutoff = DateTime.UtcNow - olderThan;

            // In a real implementation, this would clean up persistent storage
            // For now, we'll just log the cleanup operation
            _logger.LogInformation("Cleaning up performance data older than {Cutoff}", cutoff);

            // Clear in-memory metrics older than cutoff
            var apiMetricsToKeep = _apiMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            _apiMetrics.Clear();
            foreach (var metric in apiMetricsToKeep)
            {
                _apiMetrics.Enqueue(metric);
            }

            var dbMetricsToKeep = _dbMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            _dbMetrics.Clear();
            foreach (var metric in dbMetricsToKeep)
            {
                _dbMetrics.Enqueue(metric);
            }

            var cacheMetricsToKeep = _cacheMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            _cacheMetrics.Clear();
            foreach (var metric in cacheMetricsToKeep)
            {
                _cacheMetrics.Enqueue(metric);
            }

            var imageMetricsToKeep = _imageMetrics.Where(m => m.Timestamp >= cutoff).ToList();
            _imageMetrics.Clear();
            foreach (var metric in imageMetricsToKeep)
            {
                _imageMetrics.Enqueue(metric);
            }
        }

        public async Task<PerformanceServiceHealthCheck> GetHealthCheckAsync()
        {
            var healthCheck = new PerformanceServiceHealthCheck();

            try
            {
                // Check if data collection is working
                var recentMetrics = _apiMetrics.Where(m => m.Timestamp >= DateTime.UtcNow.AddMinutes(-5)).Count();
                healthCheck.DataCollectionWorking = recentMetrics > 0;

                if (!healthCheck.DataCollectionWorking)
                {
                    healthCheck.Issues.Add("No recent performance data collected");
                }

                // Check alerting system
                var alerts = await GetActiveAlertsAsync();
                healthCheck.AlertingWorking = true; // Assume working if no exceptions

                // Set overall health
                healthCheck.IsHealthy = healthCheck.DataCollectionWorking && healthCheck.AlertingWorking;
                healthCheck.Status = healthCheck.IsHealthy ? "Healthy" : "Unhealthy";

                // Add metrics
                healthCheck.Metrics["TotalApiMetrics"] = _apiMetrics.Count;
                healthCheck.Metrics["TotalDbMetrics"] = _dbMetrics.Count;
                healthCheck.Metrics["TotalCacheMetrics"] = _cacheMetrics.Count;
                healthCheck.Metrics["TotalImageMetrics"] = _imageMetrics.Count;
                healthCheck.Metrics["ActiveAlerts"] = alerts.Count;

                if (_apiMetrics.Any())
                {
                    healthCheck.LastDataPoint = _apiMetrics.Max(m => m.Timestamp);
                }

                return healthCheck;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                healthCheck.IsHealthy = false;
                healthCheck.Status = "Error";
                healthCheck.Issues.Add($"Health check error: {ex.Message}");
                return healthCheck;
            }
        }
    }
    
    // Internal metric classes
    internal class ApiRequestMetric
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string? CacheSource { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    internal class DatabaseQueryMetric
    {
        public string QueryType { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public int ResultCount { get; set; }
        public bool Success { get; set; }
        public string? AdditionalInfo { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    internal class CacheOperationMetric
    {
        public string Operation { get; set; } = string.Empty;
        public string CacheType { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public bool Hit { get; set; }
        public string? Key { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    internal class ImageProcessingMetric
    {
        public string Operation { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public long? FileSize { get; set; }
        public string? Format { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
