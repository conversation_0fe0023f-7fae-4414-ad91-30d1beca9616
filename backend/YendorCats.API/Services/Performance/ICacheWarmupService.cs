namespace YendorCats.API.Services.Performance
{
    /// <summary>
    /// Interface for cache warmup service
    /// Proactively loads popular content into cache for optimal performance
    /// </summary>
    public interface ICacheWarmupService
    {
        // Warmup operations
        /// <summary>
        /// Warm up cache for a specific category
        /// </summary>
        Task<WarmupResult> WarmupCategoryAsync(string category, WarmupStrategy strategy = WarmupStrategy.Popular);
        
        /// <summary>
        /// Warm up cache for all categories
        /// </summary>
        Task<BatchWarmupResult> WarmupAllCategoriesAsync(WarmupStrategy strategy = WarmupStrategy.Popular);
        
        /// <summary>
        /// Warm up cache with specific images
        /// </summary>
        Task<WarmupResult> WarmupSpecificImagesAsync(IEnumerable<long> imageIds);
        
        /// <summary>
        /// Warm up cache based on recent access patterns
        /// </summary>
        Task<WarmupResult> WarmupByAccessPatternsAsync(TimeSpan lookbackPeriod);
        
        /// <summary>
        /// Warm up cache for upcoming peak hours
        /// </summary>
        Task<WarmupResult> WarmupForPeakHoursAsync();
        
        // Scheduled warmup
        /// <summary>
        /// Schedule regular cache warmup
        /// </summary>
        Task ScheduleWarmupAsync(WarmupSchedule schedule);
        
        /// <summary>
        /// Cancel scheduled warmup
        /// </summary>
        Task CancelScheduledWarmupAsync(string scheduleId);
        
        /// <summary>
        /// Get active warmup schedules
        /// </summary>
        Task<List<WarmupSchedule>> GetActiveSchedulesAsync();
        
        // Intelligent warmup
        /// <summary>
        /// Analyze access patterns and recommend warmup strategy
        /// </summary>
        Task<WarmupRecommendation> AnalyzeAndRecommendAsync();
        
        /// <summary>
        /// Predict which content should be warmed up
        /// </summary>
        Task<List<long>> PredictContentToWarmupAsync(int count = 50);
        
        /// <summary>
        /// Warm up based on user behavior patterns
        /// </summary>
        Task<WarmupResult> WarmupByUserBehaviorAsync();
        
        // Monitoring and optimization
        /// <summary>
        /// Get warmup effectiveness metrics
        /// </summary>
        Task<WarmupEffectivenessMetrics> GetEffectivenessMetricsAsync();
        
        /// <summary>
        /// Get warmup history
        /// </summary>
        Task<List<WarmupHistoryEntry>> GetWarmupHistoryAsync(int count = 50);
        
        /// <summary>
        /// Optimize warmup strategy based on performance data
        /// </summary>
        Task<WarmupOptimizationResult> OptimizeWarmupStrategyAsync();
        
        // Health and status
        /// <summary>
        /// Get current warmup status
        /// </summary>
        Task<WarmupStatus> GetWarmupStatusAsync();
        
        /// <summary>
        /// Get health check for warmup service
        /// </summary>
        Task<WarmupHealthCheck> GetHealthCheckAsync();
    }
    
    /// <summary>
    /// Warmup strategy options
    /// </summary>
    public enum WarmupStrategy
    {
        Popular,        // Most accessed content
        Recent,         // Recently uploaded content
        Trending,       // Currently trending content
        Predictive,     // AI-predicted popular content
        Comprehensive,  // All active content
        Custom          // Custom selection criteria
    }
    
    /// <summary>
    /// Result of cache warmup operation
    /// </summary>
    public class WarmupResult
    {
        public bool Success { get; set; }
        public int TotalItems { get; set; }
        public int WarmedUpItems { get; set; }
        public int SkippedItems { get; set; }
        public int FailedItems { get; set; }
        public List<string> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public WarmupStrategy Strategy { get; set; }
        public string? Category { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public double SuccessRate => TotalItems > 0 ? (double)WarmedUpItems / TotalItems : 0;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
    
    /// <summary>
    /// Result of batch warmup operations
    /// </summary>
    public class BatchWarmupResult
    {
        public int TotalCategories { get; set; }
        public int SuccessfulCategories { get; set; }
        public int FailedCategories { get; set; }
        public List<WarmupResult> CategoryResults { get; set; } = new();
        public TimeSpan TotalDuration { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int TotalItemsWarmedUp { get; set; }
        public double OverallSuccessRate => TotalCategories > 0 ? (double)SuccessfulCategories / TotalCategories : 0;
    }
    
    /// <summary>
    /// Warmup schedule configuration
    /// </summary>
    public class WarmupSchedule
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string CronExpression { get; set; } = string.Empty;
        public WarmupStrategy Strategy { get; set; }
        public List<string> Categories { get; set; } = new();
        public int MaxItems { get; set; } = 100;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
        public Dictionary<string, object> Configuration { get; set; } = new();
    }
    
    /// <summary>
    /// Warmup recommendation based on analysis
    /// </summary>
    public class WarmupRecommendation
    {
        public WarmupStrategy RecommendedStrategy { get; set; }
        public List<string> PriorityCategories { get; set; } = new();
        public List<long> PriorityImages { get; set; } = new();
        public int RecommendedFrequencyHours { get; set; }
        public TimeSpan OptimalWarmupTime { get; set; }
        public List<string> Reasons { get; set; } = new();
        public double ConfidenceScore { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> AnalysisData { get; set; } = new();
    }
    
    /// <summary>
    /// Warmup effectiveness metrics
    /// </summary>
    public class WarmupEffectivenessMetrics
    {
        public double CacheHitRateImprovement { get; set; }
        public TimeSpan ResponseTimeImprovement { get; set; }
        public double WarmupEfficiencyScore { get; set; }
        public int TotalWarmupOperations { get; set; }
        public int SuccessfulWarmupOperations { get; set; }
        public Dictionary<string, double> EffectivenessByCategory { get; set; } = new();
        public Dictionary<WarmupStrategy, double> EffectivenessByStrategy { get; set; } = new();
        public List<string> TopPerformingStrategies { get; set; } = new();
        public List<string> UnderperformingAreas { get; set; } = new();
        public DateTime AnalysisPeriodStart { get; set; }
        public DateTime AnalysisPeriodEnd { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Warmup history entry
    /// </summary>
    public class WarmupHistoryEntry
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; }
        public WarmupStrategy Strategy { get; set; }
        public string? Category { get; set; }
        public int ItemsWarmedUp { get; set; }
        public TimeSpan Duration { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public double EffectivenessScore { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
    
    /// <summary>
    /// Warmup optimization result
    /// </summary>
    public class WarmupOptimizationResult
    {
        public bool OptimizationApplied { get; set; }
        public WarmupStrategy OptimalStrategy { get; set; }
        public int OptimalFrequencyHours { get; set; }
        public List<string> OptimalCategories { get; set; } = new();
        public double ExpectedImprovement { get; set; }
        public List<string> OptimizationChanges { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public DateTime OptimizedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> OptimizationData { get; set; } = new();
    }
    
    /// <summary>
    /// Current warmup status
    /// </summary>
    public class WarmupStatus
    {
        public bool IsWarmupInProgress { get; set; }
        public string? CurrentOperation { get; set; }
        public int ActiveSchedules { get; set; }
        public DateTime? LastWarmupTime { get; set; }
        public DateTime? NextScheduledWarmup { get; set; }
        public WarmupResult? LastWarmupResult { get; set; }
        public List<string> RecentErrors { get; set; } = new();
        public Dictionary<string, object> Statistics { get; set; } = new();
        public DateTime StatusTime { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Health check for warmup service
    /// </summary>
    public class WarmupHealthCheck
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public bool SchedulerWorking { get; set; }
        public bool WarmupOperationsWorking { get; set; }
        public int PendingOperations { get; set; }
        public int FailedOperations { get; set; }
        public DateTime? LastSuccessfulWarmup { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.UtcNow;
    }
}
