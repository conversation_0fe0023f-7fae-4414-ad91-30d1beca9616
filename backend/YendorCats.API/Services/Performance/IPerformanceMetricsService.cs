namespace YendorCats.API.Services.Performance
{
    /// <summary>
    /// Interface for performance metrics collection and monitoring
    /// Tracks system performance to ensure 85-90% improvement targets are met
    /// </summary>
    public interface IPerformanceMetricsService
    {
        // Metrics collection
        /// <summary>
        /// Record API request performance
        /// </summary>
        Task RecordApiRequestAsync(string endpoint, string method, TimeSpan duration, 
            bool success, string? cacheSource = null);
        
        /// <summary>
        /// Record database query performance
        /// </summary>
        Task RecordDatabaseQueryAsync(string queryType, TimeSpan duration, int resultCount, 
            bool success, string? additionalInfo = null);
        
        /// <summary>
        /// Record cache operation performance
        /// </summary>
        Task RecordCacheOperationAsync(string operation, string cacheType, TimeSpan duration, 
            bool hit, string? key = null);
        
        /// <summary>
        /// Record image processing performance
        /// </summary>
        Task RecordImageProcessingAsync(string operation, TimeSpan duration, bool success, 
            long? fileSize = null, string? format = null);
        
        // Performance analysis
        /// <summary>
        /// Get overall system performance metrics
        /// </summary>
        Task<SystemPerformanceMetrics> GetSystemMetricsAsync(TimeSpan? timeWindow = null);
        
        /// <summary>
        /// Get API endpoint performance breakdown
        /// </summary>
        Task<List<EndpointPerformanceMetrics>> GetEndpointMetricsAsync(TimeSpan? timeWindow = null);
        
        /// <summary>
        /// Get cache performance metrics
        /// </summary>
        Task<CachePerformanceMetrics> GetCacheMetricsAsync(TimeSpan? timeWindow = null);
        
        /// <summary>
        /// Get database performance metrics
        /// </summary>
        Task<DatabasePerformanceMetrics> GetDatabaseMetricsAsync(TimeSpan? timeWindow = null);
        
        // Performance comparison
        /// <summary>
        /// Compare current performance with baseline (pre-optimization)
        /// </summary>
        Task<PerformanceComparisonResult> CompareWithBaselineAsync();
        
        /// <summary>
        /// Get performance trends over time
        /// </summary>
        Task<PerformanceTrendAnalysis> GetPerformanceTrendsAsync(TimeSpan timeWindow);
        
        // Alerting and monitoring
        /// <summary>
        /// Check if performance meets SLA targets
        /// </summary>
        Task<SlaComplianceResult> CheckSlaComplianceAsync();
        
        /// <summary>
        /// Get performance alerts
        /// </summary>
        Task<List<PerformanceAlert>> GetActiveAlertsAsync();
        
        /// <summary>
        /// Get slowest operations for optimization
        /// </summary>
        Task<List<SlowOperationReport>> GetSlowestOperationsAsync(int count = 10);
        
        // Reporting
        /// <summary>
        /// Generate performance report
        /// </summary>
        Task<PerformanceReport> GenerateReportAsync(TimeSpan timeWindow, ReportType reportType = ReportType.Summary);
        
        /// <summary>
        /// Export performance data
        /// </summary>
        Task<byte[]> ExportPerformanceDataAsync(TimeSpan timeWindow, ExportFormat format = ExportFormat.Json);
        
        // Maintenance
        /// <summary>
        /// Clean up old performance data
        /// </summary>
        Task CleanupOldDataAsync(TimeSpan olderThan);
        
        /// <summary>
        /// Get service health check
        /// </summary>
        Task<PerformanceServiceHealthCheck> GetHealthCheckAsync();
    }
    
    /// <summary>
    /// Overall system performance metrics
    /// </summary>
    public class SystemPerformanceMetrics
    {
        public TimeSpan AverageResponseTime { get; set; }
        public TimeSpan MedianResponseTime { get; set; }
        public TimeSpan P95ResponseTime { get; set; }
        public TimeSpan P99ResponseTime { get; set; }
        public double ErrorRate { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double CacheHitRate { get; set; }
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int FailedRequests { get; set; }
        public TimeSpan TimeWindow { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, double> MetricsByCategory { get; set; } = new();
    }
    
    /// <summary>
    /// Performance metrics for specific API endpoints
    /// </summary>
    public class EndpointPerformanceMetrics
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int RequestCount { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public TimeSpan MinResponseTime { get; set; }
        public TimeSpan MaxResponseTime { get; set; }
        public double ErrorRate { get; set; }
        public double CacheHitRate { get; set; }
        public List<TimeSpan> RecentResponseTimes { get; set; } = new();
        public Dictionary<string, int> ResponseCodes { get; set; } = new();
    }
    
    /// <summary>
    /// Cache performance metrics
    /// </summary>
    public class CachePerformanceMetrics
    {
        public double OverallHitRate { get; set; }
        public double MemoryCacheHitRate { get; set; }
        public double DistributedCacheHitRate { get; set; }
        public TimeSpan AverageMemoryResponseTime { get; set; }
        public TimeSpan AverageDistributedResponseTime { get; set; }
        public int TotalCacheOperations { get; set; }
        public int CacheHits { get; set; }
        public int CacheMisses { get; set; }
        public Dictionary<string, double> HitRatesByCategory { get; set; } = new();
        public Dictionary<string, TimeSpan> ResponseTimesByOperation { get; set; } = new();
    }
    
    /// <summary>
    /// Database performance metrics
    /// </summary>
    public class DatabasePerformanceMetrics
    {
        public TimeSpan AverageQueryTime { get; set; }
        public TimeSpan SlowestQueryTime { get; set; }
        public int TotalQueries { get; set; }
        public int SuccessfulQueries { get; set; }
        public int FailedQueries { get; set; }
        public double ErrorRate { get; set; }
        public Dictionary<string, TimeSpan> QueryTimesByType { get; set; } = new();
        public Dictionary<string, int> QueryCountsByType { get; set; } = new();
        public List<string> SlowestQueries { get; set; } = new();
    }
    
    /// <summary>
    /// Performance comparison with baseline
    /// </summary>
    public class PerformanceComparisonResult
    {
        public TimeSpan BaselineResponseTime { get; set; }
        public TimeSpan CurrentResponseTime { get; set; }
        public double ImprovementPercentage { get; set; }
        public bool MeetsTarget { get; set; } // 85-90% improvement target
        public Dictionary<string, double> ImprovementByCategory { get; set; } = new();
        public List<string> RegressionAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public DateTime ComparisonDate { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Performance trend analysis
    /// </summary>
    public class PerformanceTrendAnalysis
    {
        public TrendDirection OverallTrend { get; set; }
        public double TrendSlope { get; set; }
        public List<PerformanceDataPoint> DataPoints { get; set; } = new();
        public Dictionary<string, TrendDirection> TrendsByMetric { get; set; } = new();
        public List<string> Insights { get; set; } = new();
        public DateTime AnalysisDate { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Performance data point for trend analysis
    /// </summary>
    public class PerformanceDataPoint
    {
        public DateTime Timestamp { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double ErrorRate { get; set; }
        public double CacheHitRate { get; set; }
    }
    
    /// <summary>
    /// SLA compliance result
    /// </summary>
    public class SlaComplianceResult
    {
        public bool IsCompliant { get; set; }
        public double CompliancePercentage { get; set; }
        public List<SlaViolation> Violations { get; set; } = new();
        public Dictionary<string, bool> ComplianceByMetric { get; set; } = new();
        public TimeSpan TimeWindow { get; set; }
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// SLA violation details
    /// </summary>
    public class SlaViolation
    {
        public string Metric { get; set; } = string.Empty;
        public string Threshold { get; set; } = string.Empty;
        public string ActualValue { get; set; } = string.Empty;
        public DateTime ViolationTime { get; set; }
        public TimeSpan Duration { get; set; }
        public string Severity { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// Performance alert
    /// </summary>
    public class PerformanceAlert
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Type { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
    
    /// <summary>
    /// Slow operation report
    /// </summary>
    public class SlowOperationReport
    {
        public string Operation { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public TimeSpan AverageTime { get; set; }
        public TimeSpan MaxTime { get; set; }
        public int Occurrences { get; set; }
        public double ImpactScore { get; set; }
        public List<string> OptimizationSuggestions { get; set; } = new();
    }
    
    /// <summary>
    /// Performance report
    /// </summary>
    public class PerformanceReport
    {
        public ReportType Type { get; set; }
        public TimeSpan TimeWindow { get; set; }
        public SystemPerformanceMetrics SystemMetrics { get; set; } = new();
        public List<EndpointPerformanceMetrics> EndpointMetrics { get; set; } = new();
        public CachePerformanceMetrics CacheMetrics { get; set; } = new();
        public DatabasePerformanceMetrics DatabaseMetrics { get; set; } = new();
        public List<string> KeyInsights { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Health check for performance service
    /// </summary>
    public class PerformanceServiceHealthCheck
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = "Unknown";
        public List<string> Issues { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public bool DataCollectionWorking { get; set; }
        public bool AlertingWorking { get; set; }
        public int PendingMetrics { get; set; }
        public DateTime LastDataPoint { get; set; }
        public DateTime LastChecked { get; set; } = DateTime.UtcNow;
    }
    
    /// <summary>
    /// Trend direction enumeration
    /// </summary>
    public enum TrendDirection
    {
        Improving,
        Stable,
        Degrading,
        Unknown
    }
    
    /// <summary>
    /// Report type enumeration
    /// </summary>
    public enum ReportType
    {
        Summary,
        Detailed,
        Executive,
        Technical
    }
    
    /// <summary>
    /// Export format enumeration
    /// </summary>
    public enum ExportFormat
    {
        Json,
        Csv,
        Excel
    }
}
