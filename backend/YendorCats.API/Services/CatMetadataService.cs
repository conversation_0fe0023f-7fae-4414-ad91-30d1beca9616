using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Implementation of comprehensive cat metadata and pedigree management service
    /// </summary>
    public class CatMetadataService : ICatMetadataService
    {
        private readonly IS3StorageService _s3StorageService;
        private readonly ILogger<CatMetadataService> _logger;

        public CatMetadataService(
            IS3StorageService s3StorageService,
            ILogger<CatMetadataService> logger)
        {
            _s3StorageService = s3StorageService;
            _logger = logger;
        }

        public async Task<CatteryStatistics> GetCatteryStatisticsAsync()
        {
            _logger.LogInformation("Gathering comprehensive cattery statistics");

            try
            {
                // Get all photos with metadata
                var allPhotos = await _s3StorageService.SearchByMetadataAsync(new Dictionary<string, string>());
                
                // Group by cat
                var catGroups = allPhotos
                    .Where(p => !string.IsNullOrEmpty(p.Metadata.GetValueOrDefault("cat-name", "")))
                    .GroupBy(p => p.Metadata.GetValueOrDefault("cat-id", p.Metadata.GetValueOrDefault("cat-name", "unknown")))
                    .ToList();

                var unlinkedPhotos = allPhotos.Count(p => string.IsNullOrEmpty(p.Metadata.GetValueOrDefault("cat-name", "")));

                // Calculate age-based statistics
                var catsByAge = new Dictionary<string, int>();
                var catsByStatus = new Dictionary<string, int>();
                var photosByType = new Dictionary<string, int>();

                var availableKittens = 0;
                var breedingQueens = 0;
                var studs = 0;
                var retiredCats = 0;

                foreach (var catGroup in catGroups)
                {
                    var representative = catGroup.First();
                    var status = representative.Metadata.GetValueOrDefault("breeding-status", "");
                    var availability = representative.Metadata.GetValueOrDefault("availability-status", "");
                    var gender = representative.Metadata.GetValueOrDefault("gender", "");

                    // Count by breeding status
                    switch (status.ToLower())
                    {
                        case "available-kitten":
                            availableKittens++;
                            break;
                        case "breeding-queen":
                            breedingQueens++;
                            break;
                        case "stud":
                            studs++;
                            break;
                        case "retired":
                            retiredCats++;
                            break;
                    }

                    // Count by age category
                    var age = representative.Metadata.GetValueOrDefault("age", "");
                    if (!string.IsNullOrEmpty(age))
                    {
                        var ageCategory = CategorizeAge(age);
                        catsByAge[ageCategory] = catsByAge.GetValueOrDefault(ageCategory, 0) + 1;
                    }

                    // Count by status
                    if (!string.IsNullOrEmpty(status))
                    {
                        catsByStatus[status] = catsByStatus.GetValueOrDefault(status, 0) + 1;
                    }

                    // Count photos by type
                    foreach (var photo in catGroup)
                    {
                        var photoType = photo.Metadata.GetValueOrDefault("photo-type", "general");
                        photosByType[photoType] = photosByType.GetValueOrDefault(photoType, 0) + 1;
                    }
                }

                var bloodlines = allPhotos
                    .Select(p => p.Metadata.GetValueOrDefault("bloodline", ""))
                    .Where(b => !string.IsNullOrEmpty(b))
                    .Distinct()
                    .Count();

                return new CatteryStatistics
                {
                    TotalCats = catGroups.Count,
                    TotalPhotos = allPhotos.Count,
                    UnlinkedPhotos = unlinkedPhotos,
                    AvailableKittens = availableKittens,
                    BreedingQueens = breedingQueens,
                    Studs = studs,
                    RetiredCats = retiredCats,
                    Bloodlines = bloodlines,
                    LastUpdated = DateTime.UtcNow,
                    PhotosByType = photosByType,
                    CatsByAge = catsByAge,
                    CatsByStatus = catsByStatus
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error gathering cattery statistics");
                throw;
            }
        }

        public async Task<List<CatProfile>> GetAllCatProfilesAsync()
        {
            _logger.LogInformation("Retrieving all cat profiles");

            try
            {
                var allPhotos = await _s3StorageService.SearchByMetadataAsync(new Dictionary<string, string>());
                
                var catProfiles = allPhotos
                    .Where(p => !string.IsNullOrEmpty(p.Metadata.GetValueOrDefault("cat-name", "")))
                    .GroupBy(p => p.Metadata.GetValueOrDefault("cat-id", p.Metadata.GetValueOrDefault("cat-name", "unknown")))
                    .Select(g => CreateCatProfileFromPhotos(g.Key, g.ToList()))
                    .OrderBy(c => c.CatName)
                    .ToList();

                _logger.LogInformation("Retrieved {Count} cat profiles", catProfiles.Count);
                return catProfiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat profiles");
                throw;
            }
        }

        public async Task<CatProfile?> GetCatProfileAsync(string catId)
        {
            _logger.LogInformation("Retrieving cat profile for ID: {CatId}", catId);

            try
            {
                var filters = new Dictionary<string, string> { ["cat-id"] = catId };
                var photos = await _s3StorageService.SearchByMetadataAsync(filters);

                if (!photos.Any())
                {
                    // Try searching by cat name as fallback
                    filters = new Dictionary<string, string> { ["cat-name"] = catId };
                    photos = await _s3StorageService.SearchByMetadataAsync(filters);
                }

                if (!photos.Any())
                {
                    _logger.LogWarning("Cat profile not found for ID: {CatId}", catId);
                    return null;
                }

                return CreateCatProfileFromPhotos(catId, photos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat profile for ID: {CatId}", catId);
                throw;
            }
        }

        public async Task<CatProfile> SaveCatProfileAsync(CatProfile profile)
        {
            _logger.LogInformation("Saving cat profile: {CatId} - {CatName}", profile.CatId, profile.CatName);

            try
            {
                // Update all photos associated with this cat
                var existingPhotos = await _s3StorageService.SearchByMetadataAsync(
                    new Dictionary<string, string> { ["cat-id"] = profile.CatId });

                var metadata = CreateMetadataFromProfile(profile);

                foreach (var photo in existingPhotos)
                {
                    await _s3StorageService.UpdateObjectMetadataAsync(photo.S3Object.Key, metadata);
                }

                profile.UpdatedAt = DateTime.UtcNow;
                _logger.LogInformation("Successfully saved cat profile: {CatId}", profile.CatId);
                return profile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving cat profile: {CatId}", profile.CatId);
                throw;
            }
        }

        public async Task<List<CatProfile>> SearchCatsAsync(CatSearchCriteria criteria)
        {
            _logger.LogInformation("Searching cats with criteria");

            try
            {
                var filters = new Dictionary<string, string>();

                if (!string.IsNullOrEmpty(criteria.CatName))
                    filters["cat-name"] = criteria.CatName;
                if (!string.IsNullOrEmpty(criteria.Breed))
                    filters["breed"] = criteria.Breed;
                if (!string.IsNullOrEmpty(criteria.Bloodline))
                    filters["bloodline"] = criteria.Bloodline;
                if (!string.IsNullOrEmpty(criteria.BreedingStatus))
                    filters["breeding-status"] = criteria.BreedingStatus;
                if (!string.IsNullOrEmpty(criteria.AvailabilityStatus))
                    filters["availability-status"] = criteria.AvailabilityStatus;
                if (!string.IsNullOrEmpty(criteria.Gender))
                    filters["gender"] = criteria.Gender;
                if (!string.IsNullOrEmpty(criteria.Color))
                    filters["color"] = criteria.Color;
                if (!string.IsNullOrEmpty(criteria.FatherId))
                    filters["father-id"] = criteria.FatherId;
                if (!string.IsNullOrEmpty(criteria.MotherId))
                    filters["mother-id"] = criteria.MotherId;

                var photos = await _s3StorageService.SearchByMetadataAsync(filters, criteria.Prefix ?? "");

                var results = photos
                    .Where(p => !string.IsNullOrEmpty(p.Metadata.GetValueOrDefault("cat-name", "")))
                    .GroupBy(p => p.Metadata.GetValueOrDefault("cat-id", p.Metadata.GetValueOrDefault("cat-name", "unknown")))
                    .Select(g => CreateCatProfileFromPhotos(g.Key, g.ToList()))
                    .Where(profile => MatchesDateCriteria(profile, criteria))
                    .ToList();

                _logger.LogInformation("Search returned {Count} matching cats", results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching cats");
                throw;
            }
        }

        public async Task<List<UnlinkedPhoto>> GetUnlinkedPhotosAsync()
        {
            _logger.LogInformation("Retrieving unlinked photos");

            try
            {
                var allPhotos = await _s3StorageService.SearchByMetadataAsync(new Dictionary<string, string>());

                var unlinkedPhotos = allPhotos
                    .Where(p => string.IsNullOrEmpty(p.Metadata.GetValueOrDefault("cat-name", "")))
                    .Select(p => new UnlinkedPhoto
                    {
                        S3Key = p.S3Object.Key,
                        Url = p.PublicUrl,
                        Size = p.S3Object.Size,
                        LastModified = p.S3Object.LastModified,
                        Metadata = p.Metadata
                    })
                    .OrderByDescending(p => p.LastModified)
                    .ToList();

                _logger.LogInformation("Found {Count} unlinked photos", unlinkedPhotos.Count);
                return unlinkedPhotos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unlinked photos");
                throw;
            }
        }

        public async Task<BulkOperationResult> LinkPhotosTosCatAsync(string catId, List<string> photoKeys)
        {
            _logger.LogInformation("Linking {Count} photos to cat: {CatId}", photoKeys.Count, catId);

            var result = new BulkOperationResult
            {
                TotalItems = photoKeys.Count,
                CompletedAt = DateTime.UtcNow
            };

            try
            {
                // Get cat profile to use existing metadata
                var catProfile = await GetCatProfileAsync(catId);
                if (catProfile == null)
                {
                    result.Errors.Add($"Cat profile not found: {catId}");
                    return result;
                }

                var metadata = CreateMetadataFromProfile(catProfile);

                foreach (var photoKey in photoKeys)
                {
                    try
                    {
                        await _s3StorageService.UpdateObjectMetadataAsync(photoKey, metadata);
                        result.SuccessfulItems++;
                    }
                    catch (Exception ex)
                    {
                        result.FailedItems++;
                        result.Errors.Add($"Failed to link {photoKey}: {ex.Message}");
                        _logger.LogError(ex, "Error linking photo {PhotoKey} to cat {CatId}", photoKey, catId);
                    }
                }

                result.Success = result.FailedItems == 0;
                result.Message = $"Linked {result.SuccessfulItems}/{result.TotalItems} photos to {catProfile.CatName}";

                _logger.LogInformation("Photo linking completed: {SuccessfulItems}/{TotalItems} successful", 
                    result.SuccessfulItems, result.TotalItems);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk photo linking operation");
                result.Errors.Add($"Operation failed: {ex.Message}");
                return result;
            }
        }

        public async Task<bool> UpdatePedigreeRelationshipsAsync(string catId, string? fatherId, string? motherId)
        {
            _logger.LogInformation("Updating pedigree relationships for cat: {CatId}", catId);

            try
            {
                var photos = await _s3StorageService.SearchByMetadataAsync(
                    new Dictionary<string, string> { ["cat-id"] = catId });

                if (!photos.Any())
                {
                    _logger.LogWarning("No photos found for cat: {CatId}", catId);
                    return false;
                }

                foreach (var photo in photos)
                {
                    var metadata = photo.Metadata.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                    
                    if (!string.IsNullOrEmpty(fatherId))
                        metadata["father-id"] = fatherId;
                    else
                        metadata.Remove("father-id");

                    if (!string.IsNullOrEmpty(motherId))
                        metadata["mother-id"] = motherId;
                    else
                        metadata.Remove("mother-id");

                    metadata["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

                    await _s3StorageService.UpdateObjectMetadataAsync(photo.S3Object.Key, metadata);
                }

                _logger.LogInformation("Successfully updated pedigree relationships for cat: {CatId}", catId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating pedigree relationships for cat: {CatId}", catId);
                throw;
            }
        }

        public async Task<FamilyTree> GetFamilyTreeAsync(string catId, int generations = 3)
        {
            _logger.LogInformation("Building family tree for cat: {CatId}, generations: {Generations}", catId, generations);

            try
            {
                var cat = await GetCatProfileAsync(catId);
                if (cat == null)
                {
                    throw new ArgumentException($"Cat not found: {catId}");
                }

                var familyTree = new FamilyTree
                {
                    Cat = cat,
                    Generation = 0
                };

                if (generations > 0)
                {
                    if (!string.IsNullOrEmpty(cat.FatherId))
                    {
                        familyTree.Father = await GetFamilyTreeAsync(cat.FatherId, generations - 1);
                        if (familyTree.Father != null)
                            familyTree.Father.Generation = 1;
                    }

                    if (!string.IsNullOrEmpty(cat.MotherId))
                    {
                        familyTree.Mother = await GetFamilyTreeAsync(cat.MotherId, generations - 1);
                        if (familyTree.Mother != null)
                            familyTree.Mother.Generation = 1;
                    }

                    // Find children
                    var children = await SearchCatsAsync(new CatSearchCriteria
                    {
                        FatherId = catId
                    });
                    var childrenFromMother = await SearchCatsAsync(new CatSearchCriteria
                    {
                        MotherId = catId
                    });

                    familyTree.Children = children.Concat(childrenFromMother)
                        .GroupBy(c => c.CatId)
                        .Select(g => g.First())
                        .ToList();
                }

                return familyTree;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building family tree for cat: {CatId}", catId);
                throw;
            }
        }

        public async Task<BulkOperationResult> ProcessLitterAsync(LitterData litterData)
        {
            _logger.LogInformation("Processing litter data for {Count} photos", litterData.PhotoKeys.Count);

            var result = new BulkOperationResult
            {
                TotalItems = litterData.PhotoKeys.Count,
                CompletedAt = DateTime.UtcNow
            };

            try
            {
                var baseMetadata = new Dictionary<string, string>(litterData.CommonMetadata)
                {
                    ["breed"] = "Maine Coon",
                    ["bloodline"] = litterData.Bloodline,
                    ["father-id"] = litterData.FatherId,
                    ["mother-id"] = litterData.MotherId,
                    ["birth-date"] = litterData.BirthDate.ToString("yyyy-MM-dd"),
                    ["breeding-status"] = "available-kitten",
                    ["availability-status"] = "available",
                    ["age"] = "kitten",
                    ["litter-date"] = litterData.BirthDate.ToString("yyyy-MM-dd"),
                    ["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
                };

                foreach (var photoKey in litterData.PhotoKeys)
                {
                    try
                    {
                        var metadata = new Dictionary<string, string>(baseMetadata);
                        
                        // Generate kitten-specific metadata if prefix provided
                        if (!string.IsNullOrEmpty(litterData.LitterPrefix))
                        {
                            var kittenNumber = litterData.PhotoKeys.IndexOf(photoKey) + 1;
                            metadata["cat-name"] = $"{litterData.LitterPrefix} Kitten {kittenNumber}";
                            metadata["cat-id"] = $"{litterData.LitterPrefix.ToLower().Replace(" ", "-")}-kitten-{kittenNumber}";
                        }

                        await _s3StorageService.UpdateObjectMetadataAsync(photoKey, metadata);
                        result.SuccessfulItems++;
                    }
                    catch (Exception ex)
                    {
                        result.FailedItems++;
                        result.Errors.Add($"Failed to process {photoKey}: {ex.Message}");
                        _logger.LogError(ex, "Error processing litter photo: {PhotoKey}", photoKey);
                    }
                }

                result.Success = result.FailedItems == 0;
                result.Message = $"Processed {result.SuccessfulItems}/{result.TotalItems} litter photos";

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing litter data");
                result.Errors.Add($"Litter processing failed: {ex.Message}");
                return result;
            }
        }

        public async Task<BulkOperationResult> BulkUpdatePhotosAsync(List<PhotoMetadataUpdate> updates)
        {
            _logger.LogInformation("Processing bulk update for {Count} photos", updates.Count);

            var result = new BulkOperationResult
            {
                TotalItems = updates.Count,
                CompletedAt = DateTime.UtcNow
            };

            try
            {
                foreach (var update in updates)
                {
                    try
                    {
                        var metadata = new Dictionary<string, string>(update.Metadata)
                        {
                            ["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
                        };

                        await _s3StorageService.UpdateObjectMetadataAsync(update.S3Key, metadata);
                        result.SuccessfulItems++;
                    }
                    catch (Exception ex)
                    {
                        result.FailedItems++;
                        result.Errors.Add($"Failed to update {update.S3Key}: {ex.Message}");
                        _logger.LogError(ex, "Error updating photo metadata: {S3Key}", update.S3Key);
                    }

                    // Small delay to avoid overwhelming S3
                    await Task.Delay(50);
                }

                result.Success = result.FailedItems == 0;
                result.Message = $"Updated {result.SuccessfulItems}/{result.TotalItems} photos";

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk photo update operation");
                result.Errors.Add($"Bulk update failed: {ex.Message}");
                return result;
            }
        }

        public async Task<PedigreeValidationResult> ValidatePedigreeIntegrityAsync()
        {
            _logger.LogInformation("Validating pedigree integrity");

            var result = new PedigreeValidationResult
            {
                ValidatedAt = DateTime.UtcNow
            };

            try
            {
                var allCats = await GetAllCatProfilesAsync();
                result.CatsValidated = allCats.Count;

                var catIds = allCats.Select(c => c.CatId).ToHashSet();

                foreach (var cat in allCats)
                {
                    // Validate father exists
                    if (!string.IsNullOrEmpty(cat.FatherId) && !catIds.Contains(cat.FatherId))
                    {
                        result.Errors.Add($"Cat {cat.CatName} ({cat.CatId}) references non-existent father: {cat.FatherId}");
                    }

                    // Validate mother exists
                    if (!string.IsNullOrEmpty(cat.MotherId) && !catIds.Contains(cat.MotherId))
                    {
                        result.Errors.Add($"Cat {cat.CatName} ({cat.CatId}) references non-existent mother: {cat.MotherId}");
                    }

                    // Check for self-referencing
                    if (cat.FatherId == cat.CatId || cat.MotherId == cat.CatId)
                    {
                        result.Errors.Add($"Cat {cat.CatName} ({cat.CatId}) references itself as parent");
                    }

                    // Validate breeding status consistency
                    if (cat.BreedingStatus == "stud" && cat.Gender?.ToLower() != "male")
                    {
                        result.Warnings.Add($"Cat {cat.CatName} is marked as stud but gender is not male");
                    }
                    
                    if (cat.BreedingStatus == "breeding-queen" && cat.Gender?.ToLower() != "female")
                    {
                        result.Warnings.Add($"Cat {cat.CatName} is marked as breeding queen but gender is not female");
                    }

                    result.RelationshipsValidated++;
                }

                result.IsValid = result.Errors.Count == 0;
                _logger.LogInformation("Pedigree validation completed: {IsValid}, {ErrorCount} errors, {WarningCount} warnings", 
                    result.IsValid, result.Errors.Count, result.Warnings.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating pedigree integrity");
                result.Errors.Add($"Validation failed: {ex.Message}");
                return result;
            }
        }

        public async Task<List<BloodlineInfo>> GetBloodlineInfoAsync()
        {
            _logger.LogInformation("Gathering bloodline information");

            try
            {
                var allCats = await GetAllCatProfilesAsync();
                
                var bloodlineGroups = allCats
                    .Where(c => !string.IsNullOrEmpty(c.Bloodline))
                    .GroupBy(c => c.Bloodline)
                    .Select(g => new BloodlineInfo
                    {
                        Name = g.Key,
                        CatCount = g.Count(),
                        PhotoCount = g.Sum(c => c.Photos.Count),
                        ChampionTitles = g.SelectMany(c => c.ChampionTitles.Split(',', StringSplitOptions.RemoveEmptyEntries))
                            .Select(t => t.Trim())
                            .Where(t => !string.IsNullOrEmpty(t))
                            .Distinct()
                            .ToList(),
                        EarliestBirthDate = g.Where(c => c.BirthDate.HasValue).Min(c => c.BirthDate) ?? DateTime.MinValue,
                        LatestBirthDate = g.Where(c => c.BirthDate.HasValue).Max(c => c.BirthDate) ?? DateTime.MaxValue,
                        FoundingCat = g.OrderBy(c => c.BirthDate ?? DateTime.MaxValue).FirstOrDefault()?.CatName ?? ""
                    })
                    .OrderByDescending(b => b.CatCount)
                    .ToList();

                _logger.LogInformation("Retrieved information for {Count} bloodlines", bloodlineGroups.Count);
                return bloodlineGroups;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error gathering bloodline information");
                throw;
            }
        }

        #region Private Helper Methods

        private CatProfile CreateCatProfileFromPhotos(string catId, List<S3ObjectWithMetadata> photos)
        {
            if (!photos.Any()) return new CatProfile { CatId = catId };

            var representative = photos.First().Metadata;
            var profile = new CatProfile
            {
                CatId = catId,
                CatName = representative.GetValueOrDefault("cat-name", ""),
                RegisteredName = representative.GetValueOrDefault("registered-name", ""),
                RegistrationNumber = representative.GetValueOrDefault("registration-number", ""),
                Breed = representative.GetValueOrDefault("breed", "Maine Coon"),
                Gender = representative.GetValueOrDefault("gender", ""),
                Color = representative.GetValueOrDefault("color", ""),
                Bloodline = representative.GetValueOrDefault("bloodline", ""),
                BreedingStatus = representative.GetValueOrDefault("breeding-status", ""),
                AvailabilityStatus = representative.GetValueOrDefault("availability-status", ""),
                Personality = representative.GetValueOrDefault("personality", ""),
                ChampionTitles = representative.GetValueOrDefault("champion-titles", ""),
                FatherId = representative.GetValueOrDefault("father-id", ""),
                MotherId = representative.GetValueOrDefault("mother-id", ""),
                CreatedBy = representative.GetValueOrDefault("created-by", ""),
                UpdatedBy = representative.GetValueOrDefault("updated-by", "")
            };

            // Parse birth date
            if (DateTime.TryParse(representative.GetValueOrDefault("birth-date", ""), out var birthDate))
            {
                profile.BirthDate = birthDate;
            }

            // Parse timestamps
            if (DateTime.TryParse(representative.GetValueOrDefault("created-at", ""), out var createdAt))
            {
                profile.CreatedAt = createdAt;
            }
            if (DateTime.TryParse(representative.GetValueOrDefault("updated-at", ""), out var updatedAt))
            {
                profile.UpdatedAt = updatedAt;
            }

            // Convert photos
            profile.Photos = photos.Select(p => new CatPhoto
            {
                S3Key = p.S3Object.Key,
                Url = p.PublicUrl,
                PhotoType = p.Metadata.GetValueOrDefault("photo-type", ""),
                AgeAtPhoto = p.Metadata.GetValueOrDefault("age-at-photo", ""),
                Description = p.Metadata.GetValueOrDefault("description", ""),
                Size = p.S3Object.Size,
                LastModified = p.S3Object.LastModified,
                Metadata = p.Metadata
            }).ToList();

            // Parse date taken for first photo
            var firstPhoto = profile.Photos.FirstOrDefault();
            if (firstPhoto != null && DateTime.TryParse(firstPhoto.Metadata.GetValueOrDefault("date-taken", ""), out var dateTaken))
            {
                firstPhoto.DateTaken = dateTaken;
            }

            return profile;
        }

        private Dictionary<string, string> CreateMetadataFromProfile(CatProfile profile)
        {
            var metadata = new Dictionary<string, string>
            {
                ["cat-id"] = profile.CatId,
                ["cat-name"] = profile.CatName,
                ["breed"] = profile.Breed,
                ["updated-at"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };

            if (!string.IsNullOrEmpty(profile.RegisteredName))
                metadata["registered-name"] = profile.RegisteredName;
            if (!string.IsNullOrEmpty(profile.RegistrationNumber))
                metadata["registration-number"] = profile.RegistrationNumber;
            if (!string.IsNullOrEmpty(profile.Gender))
                metadata["gender"] = profile.Gender;
            if (!string.IsNullOrEmpty(profile.Color))
                metadata["color"] = profile.Color;
            if (!string.IsNullOrEmpty(profile.Bloodline))
                metadata["bloodline"] = profile.Bloodline;
            if (profile.BirthDate.HasValue)
                metadata["birth-date"] = profile.BirthDate.Value.ToString("yyyy-MM-dd");
            if (!string.IsNullOrEmpty(profile.BreedingStatus))
                metadata["breeding-status"] = profile.BreedingStatus;
            if (!string.IsNullOrEmpty(profile.AvailabilityStatus))
                metadata["availability-status"] = profile.AvailabilityStatus;
            if (!string.IsNullOrEmpty(profile.Personality))
                metadata["personality"] = profile.Personality;
            if (!string.IsNullOrEmpty(profile.ChampionTitles))
                metadata["champion-titles"] = profile.ChampionTitles;
            if (!string.IsNullOrEmpty(profile.FatherId))
                metadata["father-id"] = profile.FatherId;
            if (!string.IsNullOrEmpty(profile.MotherId))
                metadata["mother-id"] = profile.MotherId;
            if (!string.IsNullOrEmpty(profile.UpdatedBy))
                metadata["updated-by"] = profile.UpdatedBy;

            return metadata;
        }

        private string CategorizeAge(string age)
        {
            var ageNormalized = age.ToLower();
            if (ageNormalized.Contains("newborn") || ageNormalized.Contains("0") || ageNormalized.Contains("week"))
                return "newborn";
            if (ageNormalized.Contains("kitten") || ageNormalized.Contains("young"))
                return "kitten";
            if (ageNormalized.Contains("adult") || ageNormalized.Contains("year"))
                return "adult";
            if (ageNormalized.Contains("senior") || ageNormalized.Contains("retired"))
                return "senior";
            return "unknown";
        }

        private bool MatchesDateCriteria(CatProfile profile, CatSearchCriteria criteria)
        {
            if (criteria.BornAfter.HasValue && profile.BirthDate.HasValue && profile.BirthDate < criteria.BornAfter)
                return false;
            if (criteria.BornBefore.HasValue && profile.BirthDate.HasValue && profile.BirthDate > criteria.BornBefore)
                return false;
            return true;
        }

        #endregion
    }
}
