using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Implementation of client management service
    /// </summary>
    public class ClientService : IClientService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ClientService> _logger;

        /// <summary>
        /// Constructor for client service
        /// </summary>
        /// <param name="context">Database context</param>
        /// <param name="logger">Logger</param>
        public ClientService(AppDbContext context, ILogger<ClientService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<ClientResponse>> GetAllClientsAsync(bool includeInactive = false)
        {
            _logger.LogInformation("Getting all clients. Include inactive: {IncludeInactive}", includeInactive);

            var query = _context.Clients.AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(c => c.IsActive);
            }

            var clients = await query
                .OrderBy(c => c.LastName)
                .ThenBy(c => c.FirstName)
                .Select(c => new ClientResponse
                {
                    Id = c.Id,
                    FirstName = c.FirstName,
                    LastName = c.LastName,
                    Email = c.Email,
                    Phone = c.Phone,
                    Address = c.Address,
                    City = c.City,
                    State = c.State,
                    PostalCode = c.PostalCode,
                    Country = c.Country,
                    Notes = c.Notes,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    IsActive = c.IsActive,
                    AppointmentCount = c.Appointments != null ? c.Appointments.Count : 0
                })
                .ToListAsync();

            return clients;
        }

        /// <inheritdoc />
        public async Task<ClientResponse?> GetClientByIdAsync(int id)
        {
            _logger.LogInformation("Getting client by ID: {Id}", id);

            var client = await _context.Clients
                .Include(c => c.Appointments)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (client == null)
            {
                _logger.LogWarning("Client not found: {Id}", id);
                return null;
            }

            return new ClientResponse
            {
                Id = client.Id,
                FirstName = client.FirstName,
                LastName = client.LastName,
                Email = client.Email,
                Phone = client.Phone,
                Address = client.Address,
                City = client.City,
                State = client.State,
                PostalCode = client.PostalCode,
                Country = client.Country,
                Notes = client.Notes,
                CreatedAt = client.CreatedAt,
                UpdatedAt = client.UpdatedAt,
                IsActive = client.IsActive,
                AppointmentCount = client.Appointments?.Count ?? 0
            };
        }

        /// <inheritdoc />
        public async Task<ClientResponse> CreateClientAsync(CreateClientRequest request)
        {
            _logger.LogInformation("Creating new client: {FirstName} {LastName}", request.FirstName, request.LastName);

            // Check if client with same email already exists
            var existingClient = await _context.Clients
                .FirstOrDefaultAsync(c => c.Email.ToLower() == request.Email.ToLower());

            if (existingClient != null)
            {
                _logger.LogWarning("Client with email already exists: {Email}", request.Email);
                throw new InvalidOperationException($"Client with email {request.Email} already exists");
            }

            var client = new Client
            {
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                Phone = request.Phone,
                Address = request.Address,
                City = request.City,
                State = request.State,
                PostalCode = request.PostalCode,
                Country = request.Country,
                Notes = request.Notes,
                CreatedAt = DateTime.UtcNow,
                IsActive = true
            };

            _context.Clients.Add(client);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Client created successfully: {Id}", client.Id);

            return new ClientResponse
            {
                Id = client.Id,
                FirstName = client.FirstName,
                LastName = client.LastName,
                Email = client.Email,
                Phone = client.Phone,
                Address = client.Address,
                City = client.City,
                State = client.State,
                PostalCode = client.PostalCode,
                Country = client.Country,
                Notes = client.Notes,
                CreatedAt = client.CreatedAt,
                UpdatedAt = client.UpdatedAt,
                IsActive = client.IsActive,
                AppointmentCount = 0
            };
        }

        /// <inheritdoc />
        public async Task<ClientResponse> UpdateClientAsync(int id, UpdateClientRequest request)
        {
            _logger.LogInformation("Updating client: {Id}", id);

            var client = await _context.Clients
                .Include(c => c.Appointments)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (client == null)
            {
                _logger.LogWarning("Client not found: {Id}", id);
                throw new KeyNotFoundException($"Client with ID {id} not found");
            }

            // Check if email is already taken by another client
            var existingClient = await _context.Clients
                .FirstOrDefaultAsync(c => c.Email.ToLower() == request.Email.ToLower() && c.Id != id);

            if (existingClient != null)
            {
                _logger.LogWarning("Email already in use by another client: {Email}", request.Email);
                throw new InvalidOperationException($"Email {request.Email} is already in use by another client");
            }

            // Update client properties
            client.FirstName = request.FirstName;
            client.LastName = request.LastName;
            client.Email = request.Email;
            client.Phone = request.Phone;
            client.Address = request.Address;
            client.City = request.City;
            client.State = request.State;
            client.PostalCode = request.PostalCode;
            client.Country = request.Country;
            client.Notes = request.Notes;
            client.IsActive = request.IsActive;
            client.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Client updated successfully: {Id}", client.Id);

            return new ClientResponse
            {
                Id = client.Id,
                FirstName = client.FirstName,
                LastName = client.LastName,
                Email = client.Email,
                Phone = client.Phone,
                Address = client.Address,
                City = client.City,
                State = client.State,
                PostalCode = client.PostalCode,
                Country = client.Country,
                Notes = client.Notes,
                CreatedAt = client.CreatedAt,
                UpdatedAt = client.UpdatedAt,
                IsActive = client.IsActive,
                AppointmentCount = client.Appointments?.Count ?? 0
            };
        }

        /// <inheritdoc />
        public async Task<bool> DeleteClientAsync(int id)
        {
            _logger.LogInformation("Deleting client: {Id}", id);

            var client = await _context.Clients.FindAsync(id);

            if (client == null)
            {
                _logger.LogWarning("Client not found: {Id}", id);
                return false;
            }

            // Soft delete - mark as inactive
            client.IsActive = false;
            client.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Client marked as inactive: {Id}", client.Id);

            return true;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<ClientResponse>> SearchClientsAsync(string searchTerm, bool includeInactive = false)
        {
            _logger.LogInformation("Searching clients with term: {SearchTerm}, Include inactive: {IncludeInactive}", 
                searchTerm, includeInactive);

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetAllClientsAsync(includeInactive);
            }

            searchTerm = searchTerm.ToLower();

            var query = _context.Clients.AsQueryable();

            if (!includeInactive)
            {
                query = query.Where(c => c.IsActive);
            }

            var clients = await query
                .Where(c => c.FirstName.ToLower().Contains(searchTerm) ||
                           c.LastName.ToLower().Contains(searchTerm) ||
                           c.Email.ToLower().Contains(searchTerm) ||
                           (c.Phone != null && c.Phone.Contains(searchTerm)))
                .OrderBy(c => c.LastName)
                .ThenBy(c => c.FirstName)
                .Select(c => new ClientResponse
                {
                    Id = c.Id,
                    FirstName = c.FirstName,
                    LastName = c.LastName,
                    Email = c.Email,
                    Phone = c.Phone,
                    Address = c.Address,
                    City = c.City,
                    State = c.State,
                    PostalCode = c.PostalCode,
                    Country = c.Country,
                    Notes = c.Notes,
                    CreatedAt = c.CreatedAt,
                    UpdatedAt = c.UpdatedAt,
                    IsActive = c.IsActive,
                    AppointmentCount = c.Appointments != null ? c.Appointments.Count : 0
                })
                .ToListAsync();

            return clients;
        }
    }
}
