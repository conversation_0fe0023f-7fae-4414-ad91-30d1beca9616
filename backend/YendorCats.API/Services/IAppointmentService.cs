using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Interface for appointment management service
    /// </summary>
    public interface IAppointmentService
    {
        /// <summary>
        /// Get all appointments
        /// </summary>
        /// <param name="includeCompleted">Whether to include completed appointments</param>
        /// <param name="includeCancelled">Whether to include cancelled appointments</param>
        /// <returns>List of appointments</returns>
        Task<IEnumerable<AppointmentResponse>> GetAllAppointmentsAsync(bool includeCompleted = true, bool includeCancelled = false);
        
        /// <summary>
        /// Get appointment by ID
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Appointment details</returns>
        Task<AppointmentResponse?> GetAppointmentByIdAsync(int id);
        
        /// <summary>
        /// Get appointments for a specific client
        /// </summary>
        /// <param name="clientId">Client ID</param>
        /// <param name="includeCompleted">Whether to include completed appointments</param>
        /// <param name="includeCancelled">Whether to include cancelled appointments</param>
        /// <returns>List of appointments for the client</returns>
        Task<IEnumerable<AppointmentResponse>> GetAppointmentsByClientIdAsync(int clientId, bool includeCompleted = true, bool includeCancelled = false);
        
        /// <summary>
        /// Get appointments for a specific date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="includeCompleted">Whether to include completed appointments</param>
        /// <param name="includeCancelled">Whether to include cancelled appointments</param>
        /// <returns>List of appointments in the date range</returns>
        Task<IEnumerable<AppointmentResponse>> GetAppointmentsByDateRangeAsync(DateTime startDate, DateTime endDate, bool includeCompleted = true, bool includeCancelled = false);
        
        /// <summary>
        /// Create a new appointment
        /// </summary>
        /// <param name="request">Appointment creation request</param>
        /// <param name="createdById">ID of the admin user creating the appointment</param>
        /// <returns>Created appointment</returns>
        Task<AppointmentResponse> CreateAppointmentAsync(CreateAppointmentRequest request, int createdById);
        
        /// <summary>
        /// Update an existing appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="request">Appointment update request</param>
        /// <returns>Updated appointment</returns>
        Task<AppointmentResponse> UpdateAppointmentAsync(int id, UpdateAppointmentRequest request);
        
        /// <summary>
        /// Cancel an appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="notes">Optional notes about cancellation</param>
        /// <returns>Cancelled appointment</returns>
        Task<AppointmentResponse> CancelAppointmentAsync(int id, string? notes = null);
        
        /// <summary>
        /// Mark an appointment as completed
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="notes">Optional notes about completion</param>
        /// <returns>Completed appointment</returns>
        Task<AppointmentResponse> CompleteAppointmentAsync(int id, string? notes = null);
        
        /// <summary>
        /// Get upcoming appointments
        /// </summary>
        /// <param name="days">Number of days to look ahead</param>
        /// <returns>List of upcoming appointments</returns>
        Task<IEnumerable<AppointmentResponse>> GetUpcomingAppointmentsAsync(int days = 7);
        
        /// <summary>
        /// Send reminder emails for upcoming appointments
        /// </summary>
        /// <param name="days">Number of days to look ahead</param>
        /// <returns>Number of reminders sent</returns>
        Task<int> SendAppointmentRemindersAsync(int days = 1);
    }
}
