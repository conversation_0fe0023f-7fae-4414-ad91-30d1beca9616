{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=YOUR_DB_PASSWORD;Port=3306;", "SqliteConnection": "Data Source=Data/yendorcats.db"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Vault": {"Address": "http://localhost:8200", "Token": "YOUR_VAULT_TOKEN_HERE", "SecretPath": "secret/yendorcats/app-secrets"}, "AWS": {"Region": "us-west-004", "UseCredentialsFromSecrets": false, "S3": {"BucketName": "YOUR_BUCKET_NAME", "UseDirectS3Urls": true, "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/YOUR_BUCKET_NAME/{key}", "UseCdn": false, "CdnDomain": "", "AccessKey": "YOUR_B2_ACCESS_KEY", "SecretKey": "YOUR_B2_SECRET_KEY"}}}