<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <UseAppHost>false</UseAppHost>
    <Watch>true</Watch>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.400" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.416.16" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.400.118" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.11" />
    <PackageReference Include="MetadataExtractor" Version="2.8.1" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.11" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.10" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
    <PackageReference Include="VaultSharp" Version="1.17.5.1" />
  </ItemGroup>

  <ItemGroup>
    <Watch Include="../../frontend/**/*.*" />
  </ItemGroup>

  <!-- Frontend build targets disabled for containerized deployment -->
  <!-- Frontend is served by a separate nginx container -->

  <!-- Clean wwwroot before build to remove old files (containerized mode) -->
  <Target Name="CleanWwwroot" BeforeTargets="Build" Condition="'$(CONTAINERIZED_BUILD)' != 'true'">
    <ItemGroup>
      <WwwrootFilesToDelete Include="wwwroot/**/*" />
      <WwwrootDirectoriesToDelete Include="$([System.IO.Directory]::GetDirectories('$(MSBuildProjectDirectory)/wwwroot', '*', System.IO.SearchOption.AllDirectories))" />
    </ItemGroup>
    <Delete Files="@(WwwrootFilesToDelete)" />
    <RemoveDir Directories="@(WwwrootDirectoriesToDelete)" />
    <MakeDir Directories="$(MSBuildProjectDirectory)/wwwroot" Condition="!Exists('$(MSBuildProjectDirectory)/wwwroot')" />
    <Message Text="Cleaned wwwroot directory" Importance="high" />
  </Target>

  <!-- Custom target to copy frontend files to wwwroot (disabled in containers) -->
  <Target Name="CopyFrontendToWwwroot" BeforeTargets="Build" Condition="'$(CONTAINERIZED_BUILD)' != 'true'">
    <ItemGroup>
      <FrontendFiles Include="../../frontend/**/*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(FrontendFiles)" DestinationFiles="@(FrontendFiles->'wwwroot/%(RecursiveDir)%(Filename)%(Extension)')" />
    <Message Text="Copied frontend files to wwwroot" Importance="high" />
  </Target>

  <!-- Verify files were copied correctly (disabled in containers) -->
  <Target Name="VerifyFrontendCopy" AfterTargets="Build" Condition="'$(OS)' == 'Windows_NT' AND '$(CONTAINERIZED_BUILD)' != 'true'">
    <Exec Command="powershell -ExecutionPolicy Bypass -File $(ProjectDir)VerifyWwwroot.ps1" />
  </Target>

  <Target Name="VerifyFrontendCopyUnix" AfterTargets="Build" Condition="'$(OS)' != 'Windows_NT' AND '$(CONTAINERIZED_BUILD)' != 'true'">
    <Exec Command="bash &quot;$(ProjectDir)verify-wwwroot.sh&quot;" />
  </Target>

</Project>