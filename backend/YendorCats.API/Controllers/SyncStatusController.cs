using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using YendorCats.API.Services.B2Sync;
using YendorCats.API.Services.Performance;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Sync Status Controller for monitoring B2 synchronization
    /// Provides real-time status and health monitoring for sync operations
    /// </summary>
    [ApiController]
    [Route("api/v2/sync")]
    [Produces("application/json")]
    public class SyncStatusController : ControllerBase
    {
        private readonly IB2SyncService _syncService;
        private readonly IPerformanceMetricsService _performanceMetrics;
        private readonly ILogger<SyncStatusController> _logger;

        public SyncStatusController(
            IB2SyncService syncService,
            IPerformanceMetricsService performanceMetrics,
            ILogger<SyncStatusController> logger)
        {
            _syncService = syncService;
            _performanceMetrics = performanceMetrics;
            _logger = logger;
        }

        /// <summary>
        /// Get current sync status (public endpoint for status monitoring)
        /// </summary>
        [HttpGet("status")]
        [ResponseCache(Duration = 30, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<object>> GetSyncStatus()
        {
            try
            {
                var status = await _syncService.GetSyncStatusAsync();
                var healthCheck = await _syncService.GetHealthCheckAsync();
                
                var publicStatus = new
                {
                    IsHealthy = status.IsHealthy,
                    Status = healthCheck.Status,
                    LastSuccessfulSync = status.LastSuccessfulSync,
                    PendingOperations = status.PendingOperations,
                    SuccessRate = status.SuccessRate,
                    CheckedAt = DateTime.UtcNow
                };

                return Ok(publicStatus);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync status");
                return StatusCode(500, new { error = "Failed to get sync status" });
            }
        }

        /// <summary>
        /// Get detailed sync health check (requires authentication)
        /// </summary>
        [HttpGet("health")]
        [Authorize]
        public async Task<ActionResult<SyncHealthCheck>> GetSyncHealthCheck()
        {
            try
            {
                var healthCheck = await _syncService.GetHealthCheckAsync();
                return Ok(healthCheck);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync health check");
                return StatusCode(500, new { error = "Failed to get sync health check" });
            }
        }

        /// <summary>
        /// Get sync statistics summary
        /// </summary>
        [HttpGet("stats")]
        [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<object>> GetSyncStats()
        {
            try
            {
                var status = await _syncService.GetSyncStatusAsync();
                var recentLogs = await _syncService.GetRecentSyncLogsAsync(100);
                
                var stats = new
                {
                    OverallHealth = status.IsHealthy,
                    SuccessRate = status.SuccessRate,
                    TotalOperationsLast24h = recentLogs.Count,
                    SuccessfulOperations = recentLogs.Count(log => log.Status == "SUCCESS"),
                    FailedOperations = recentLogs.Count(log => log.Status == "FAILED"),
                    OperationsByType = recentLogs
                        .GroupBy(log => log.Operation)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    LastSuccessfulSync = status.LastSuccessfulSync,
                    LastFailedSync = status.LastFailedSync,
                    GeneratedAt = DateTime.UtcNow
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync stats");
                return StatusCode(500, new { error = "Failed to get sync stats" });
            }
        }

        /// <summary>
        /// Get recent sync operations (limited view for monitoring)
        /// </summary>
        [HttpGet("recent")]
        [ResponseCache(Duration = 30, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<object>> GetRecentSyncOperations([FromQuery] int count = 20)
        {
            try
            {
                if (count < 1 || count > 100)
                {
                    return BadRequest(new { error = "Count must be between 1 and 100" });
                }

                var logs = await _syncService.GetRecentSyncLogsAsync(count);
                
                // Return limited information for public monitoring
                var recentOps = logs.Select(log => new
                {
                    Operation = log.Operation,
                    Status = log.Status,
                    SyncedAt = log.SyncedAt,
                    HasError = !string.IsNullOrEmpty(log.ErrorMessage)
                }).ToList();

                return Ok(new
                {
                    Operations = recentOps,
                    Count = recentOps.Count,
                    GeneratedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent sync operations");
                return StatusCode(500, new { error = "Failed to get recent sync operations" });
            }
        }

        /// <summary>
        /// Get sync performance metrics
        /// </summary>
        [HttpGet("performance")]
        [Authorize]
        [ResponseCache(Duration = 120, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<object>> GetSyncPerformanceMetrics([FromQuery] int hours = 24)
        {
            try
            {
                var timeWindow = TimeSpan.FromHours(hours);
                var logs = await _syncService.GetRecentSyncLogsAsync(1000);
                var cutoffTime = DateTime.UtcNow - timeWindow;
                var recentLogs = logs.Where(log => log.SyncedAt >= cutoffTime).ToList();
                
                if (!recentLogs.Any())
                {
                    return Ok(new
                    {
                        Message = "No sync operations in the specified time window",
                        TimeWindow = timeWindow,
                        GeneratedAt = DateTime.UtcNow
                    });
                }

                var successfulOps = recentLogs.Where(log => log.Status == "SUCCESS").ToList();
                var failedOps = recentLogs.Where(log => log.Status == "FAILED").ToList();
                
                var metrics = new
                {
                    TimeWindow = timeWindow,
                    TotalOperations = recentLogs.Count,
                    SuccessfulOperations = successfulOps.Count,
                    FailedOperations = failedOps.Count,
                    SuccessRate = recentLogs.Count > 0 ? (double)successfulOps.Count / recentLogs.Count : 0,
                    OperationsByType = recentLogs
                        .GroupBy(log => log.Operation)
                        .ToDictionary(g => g.Key, g => new
                        {
                            Total = g.Count(),
                            Successful = g.Count(log => log.Status == "SUCCESS"),
                            Failed = g.Count(log => log.Status == "FAILED")
                        }),
                    OperationsByHour = recentLogs
                        .GroupBy(log => log.SyncedAt.Hour)
                        .OrderBy(g => g.Key)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    RecentErrors = failedOps
                        .Take(10)
                        .Select(log => new
                        {
                            Operation = log.Operation,
                            B2Key = log.B2Key,
                            ErrorMessage = log.ErrorMessage,
                            SyncedAt = log.SyncedAt
                        })
                        .ToList(),
                    GeneratedAt = DateTime.UtcNow
                };

                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync performance metrics");
                return StatusCode(500, new { error = "Failed to get sync performance metrics" });
            }
        }

        /// <summary>
        /// Get sync trends over time
        /// </summary>
        [HttpGet("trends")]
        [Authorize]
        [ResponseCache(Duration = 300, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<object>> GetSyncTrends([FromQuery] int days = 7)
        {
            try
            {
                if (days < 1 || days > 30)
                {
                    return BadRequest(new { error = "Days must be between 1 and 30" });
                }

                var logs = await _syncService.GetRecentSyncLogsAsync(10000); // Get more logs for trend analysis
                var cutoffTime = DateTime.UtcNow.AddDays(-days);
                var recentLogs = logs.Where(log => log.SyncedAt >= cutoffTime).ToList();
                
                var dailyTrends = recentLogs
                    .GroupBy(log => log.SyncedAt.Date)
                    .OrderBy(g => g.Key)
                    .Select(g => new
                    {
                        Date = g.Key,
                        TotalOperations = g.Count(),
                        SuccessfulOperations = g.Count(log => log.Status == "SUCCESS"),
                        FailedOperations = g.Count(log => log.Status == "FAILED"),
                        SuccessRate = g.Count() > 0 ? (double)g.Count(log => log.Status == "SUCCESS") / g.Count() : 0,
                        OperationsByType = g.GroupBy(log => log.Operation)
                            .ToDictionary(og => og.Key, og => og.Count())
                    })
                    .ToList();

                var trends = new
                {
                    Period = $"Last {days} days",
                    DailyTrends = dailyTrends,
                    Summary = new
                    {
                        TotalDays = dailyTrends.Count,
                        AverageOperationsPerDay = dailyTrends.Any() ? dailyTrends.Average(d => d.TotalOperations) : 0,
                        AverageSuccessRate = dailyTrends.Any() ? dailyTrends.Average(d => d.SuccessRate) : 0,
                        BestDay = dailyTrends.OrderByDescending(d => d.SuccessRate).FirstOrDefault(),
                        WorstDay = dailyTrends.OrderBy(d => d.SuccessRate).FirstOrDefault()
                    },
                    GeneratedAt = DateTime.UtcNow
                };

                return Ok(trends);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync trends");
                return StatusCode(500, new { error = "Failed to get sync trends" });
            }
        }

        /// <summary>
        /// Webhook endpoint for B2 sync notifications (if implemented)
        /// </summary>
        [HttpPost("webhook")]
        public async Task<ActionResult> SyncWebhook([FromBody] object payload)
        {
            try
            {
                // This would handle webhooks from B2 or other sync triggers
                _logger.LogInformation("Sync webhook received: {Payload}", payload);
                
                // Process webhook payload here
                // This could trigger sync operations based on B2 events
                
                return Ok(new { message = "Webhook processed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing sync webhook");
                return StatusCode(500, new { error = "Webhook processing failed" });
            }
        }

        /// <summary>
        /// Simple ping endpoint for monitoring systems
        /// </summary>
        [HttpGet("ping")]
        public ActionResult Ping()
        {
            return Ok(new
            {
                Status = "OK",
                Service = "Sync Status API",
                Timestamp = DateTime.UtcNow,
                Version = "2.0"
            });
        }
    }
}
