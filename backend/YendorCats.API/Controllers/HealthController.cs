using Microsoft.AspNetCore.Mvc;
using System.Reflection;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Health check controller for monitoring service availability
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly ILogger<HealthController> _logger;

        public HealthController(ILogger<HealthController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Simple health check endpoint
        /// </summary>
        /// <returns>Health status</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public ActionResult GetHealth()
        {
            try
            {
                var response = new
                {
                    status = "healthy",
                    timestamp = DateTime.UtcNow,
                    service = "YendorCats API",
                    version = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "unknown",
                    environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "unknown"
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Health check failed");
                return StatusCode(500, new { status = "unhealthy", error = ex.Message });
            }
        }
    }


}
