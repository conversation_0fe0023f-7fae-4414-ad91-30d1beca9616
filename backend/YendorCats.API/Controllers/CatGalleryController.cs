using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using YendorCats.API.Models;
using YendorCats.API.Services;
using YendorCats.API.Data;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Attributes;

namespace YendorCats.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize("SuperAdmin", "Admin", "Editor")]  // ✅ SECURE: Require admin authentication
    public class CatGalleryController : ControllerBase
    {
        private readonly ILogger<CatGalleryController> _logger;
        private readonly IS3StorageService _s3StorageService;
        private readonly AppDbContext _context;
        private readonly string[] _validCategories = { "studs", "queens", "kittens", "gallery" };

        public CatGalleryController(
            ILogger<CatGalleryController> logger,
            IS3StorageService s3StorageService,
            AppDbContext context)
        {
            _logger = logger;
            _s3StorageService = s3StorageService;
            _context = context;
        }

        [HttpGet("category/{category}")]
        public async Task<ActionResult<IEnumerable<CatGalleryImage>>> GetCategoryImages(
            string category,
            [FromQuery] string orderBy = "date",
            [FromQuery] bool descending = true)
        {
            if (!_validCategories.Contains(category.ToLower()))
            {
                return BadRequest($"Invalid category. Valid categories are: {string.Join(", ", _validCategories)}");
            }

            try
            {
                var catImages = await ScanS3ForImagesAsync(category);

                IOrderedEnumerable<CatGalleryImage> orderedImages;
                switch (orderBy.ToLower())
                {
                    case "name":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.CatName)
                            : catImages.OrderBy(img => img.CatName);
                        break;
                    case "age":
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.Age)
                            : catImages.OrderBy(img => img.Age);
                        break;
                    default:
                        orderedImages = descending
                            ? catImages.OrderByDescending(img => img.DateTaken).ThenBy(img => img.OrderNumber)
                            : catImages.OrderBy(img => img.DateTaken).ThenBy(img => img.OrderNumber);
                        break;
                }

                return orderedImages.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cat gallery images for category {Category}", category);
                return StatusCode(500, "An error occurred while retrieving cat gallery images");
            }
        }

        private async Task<List<CatGalleryImage>> ScanS3ForImagesAsync(string category)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = new List<CatGalleryImage>();

            try
            {
                // First, try to get images from database (uploaded with metadata)
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                if (dbImages.Any())
                {
                    _logger.LogInformation("Found {Count} images in database for category {Category}", dbImages.Count, category);
                    result.AddRange(dbImages);
                }

                // Also scan S3 for any images not in database (legacy or direct uploads)
                var prefix = $"{category}/";
                var s3Files = await _s3StorageService.ListFilesAsync(prefix);
                
                if (!s3Files.Any())
                {
                    _logger.LogInformation("No S3 files found for category {Category}", category);
                    return result;
                }

                // Filter out files that are already in database before fetching metadata
                var dbImageKeys = dbImages.Select(img => ExtractS3KeyFromUrl(img.ImageUrl)).ToHashSet();
                var s3FilesToProcess = s3Files
                    .Where(s3Object => !dbImageKeys.Contains(s3Object.Key))
                    .Where(s3Object => IsImageFile(s3Object.Key))
                    .ToList();

                if (!s3FilesToProcess.Any())
                {
                    _logger.LogInformation("No new S3 files to process for category {Category}", category);
                    return result;
                }

                _logger.LogInformation("Processing {Count} S3 files for category {Category}", s3FilesToProcess.Count, category);

                // Process S3 files in parallel batches for better performance
                var s3Images = await ProcessS3FilesInBatches(s3FilesToProcess, category);
                result.AddRange(s3Images);

                stopwatch.Stop();
                _logger.LogInformation("Total images found for category {Category}: {Count} (DB: {DbCount}, S3: {S3Count}) in {ElapsedMs}ms",
                    category, result.Count, dbImages.Count, s3Images.Count, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scanning S3 for images in category {Category}", category);
                throw;
            }
        }

        private async Task<List<CatGalleryImage>> ProcessS3FilesInBatches(List<Amazon.S3.Model.S3Object> s3Files, string category)
        {
            const int batchSize = 10; // Process 10 files at a time to avoid overwhelming S3
            var result = new List<CatGalleryImage>();
            var totalBatches = (int)Math.Ceiling((double)s3Files.Count / batchSize);

            for (int i = 0; i < totalBatches; i++)
            {
                var batch = s3Files.Skip(i * batchSize).Take(batchSize).ToList();
                var batchTasks = batch.Select(s3Object => ProcessSingleS3File(s3Object, category)).ToArray();
                
                try
                {
                    var batchResults = await Task.WhenAll(batchTasks);
                    var validResults = batchResults.Where(r => r != null).ToList();
                    result.AddRange(validResults);
                    
                    _logger.LogDebug("Processed batch {BatchNumber}/{TotalBatches} for category {Category}: {SuccessCount}/{BatchSize} successful",
                        i + 1, totalBatches, category, validResults.Count, batch.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing batch {BatchNumber} for category {Category}", i + 1, category);
                    
                    // Process failed batch items individually to salvage what we can
                    foreach (var s3Object in batch)
                    {
                        try
                        {
                            var catImage = await ProcessSingleS3File(s3Object, category);
                            if (catImage != null)
                            {
                                result.Add(catImage);
                            }
                        }
                        catch (Exception individualEx)
                        {
                            _logger.LogWarning(individualEx, "Failed to process individual S3 file: {S3Key}", s3Object.Key);
                        }
                    }
                }

                // Small delay between batches to avoid rate limiting
                if (i < totalBatches - 1)
                {
                    await Task.Delay(100);
                }
            }

            return result;
        }

        private async Task<CatGalleryImage?> ProcessSingleS3File(Amazon.S3.Model.S3Object s3Object, string category)
        {
            try
            {
                var imageUrl = $"https://f004.backblazeb2.com/file/yendor/{s3Object.Key}";
                var metadata = await _s3StorageService.GetObjectMetadataAsync(s3Object.Key);
                var catImage = CatGalleryImage.FromS3Metadata(metadata, imageUrl, category, null);

                // Try to extract cat name from folder structure as fallback
                if (string.IsNullOrEmpty(catImage.CatName))
                {
                    catImage.CatName = ExtractCatNameFromPath(s3Object.Key);
                }

                return catImage;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to process S3 file: {S3Key}", s3Object.Key);
                return null;
            }
        }

        private string ExtractS3KeyFromUrl(string imageUrl)
        {
            try
            {
                // Extract S3 key from URL like "https://f004.backblazeb2.com/file/yendor/category/catname/filename.jpg"
                var uri = new Uri(imageUrl);
                var path = uri.AbsolutePath;
                var filePrefix = "/file/yendor/";
                
                if (path.StartsWith(filePrefix))
                {
                    return path.Substring(filePrefix.Length);
                }
                
                return imageUrl;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract S3 key from URL: {ImageUrl}", imageUrl);
                return imageUrl;
            }
        }

        private bool IsImageFile(string s3Key)
        {
            if (string.IsNullOrEmpty(s3Key))
                return false;

            var extension = System.IO.Path.GetExtension(s3Key).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => true,
                _ => false
            };
        }

        private string ExtractCatNameFromPath(string s3Key)
        {
            try
            {
                // S3 key format: category/catname/filename.jpg
                var parts = s3Key.Split('/');
                if (parts.Length >= 3)
                {
                    var catName = parts[1];
                    if (!string.IsNullOrEmpty(catName) && catName != ".bzEmpty")
                    {
                        // Capitalize first letter
                        return char.ToUpper(catName[0]) + catName.Substring(1);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract cat name from S3 key: {Key}", s3Key);
            }

            return "Maine Coon Cat";
        }
    }
}
