using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Diagnostics;
using YendorCats.API.Services.Gallery;
using YendorCats.API.Services.Performance;
using YendorCats.API.Services.B2Sync;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Administrative Gallery Controller for management operations
    /// Requires authentication and admin privileges
    /// </summary>
    [ApiController]
    [Route("api/v2/admin/gallery")]
    [Authorize(Roles = "Admin")]
    [Produces("application/json")]
    public class AdminGalleryController : ControllerBase
    {
        private readonly IGalleryService _galleryService;
        private readonly IB2SyncService _syncService;
        private readonly IPerformanceMetricsService _performanceMetrics;
        private readonly ICacheWarmupService _cacheWarmupService;
        private readonly ILogger<AdminGalleryController> _logger;

        public AdminGalleryController(
            IGalleryService galleryService,
            IB2SyncService syncService,
            IPerformanceMetricsService performanceMetrics,
            ICacheWarmupService cacheWarmupService,
            ILogger<AdminGalleryController> logger)
        {
            _galleryService = galleryService;
            _syncService = syncService;
            _performanceMetrics = performanceMetrics;
            _cacheWarmupService = cacheWarmupService;
            _logger = logger;
        }

        /// <summary>
        /// Get comprehensive gallery health check
        /// </summary>
        [HttpGet("health")]
        public async Task<ActionResult<object>> GetHealthCheck()
        {
            try
            {
                var galleryHealth = await _galleryService.GetHealthCheckAsync();
                var syncHealth = await _syncService.GetHealthCheckAsync();
                var warmupHealth = await _cacheWarmupService.GetHealthCheckAsync();
                
                var overallHealth = new
                {
                    IsHealthy = galleryHealth.IsHealthy && syncHealth.IsHealthy && warmupHealth.IsHealthy,
                    Status = galleryHealth.IsHealthy && syncHealth.IsHealthy && warmupHealth.IsHealthy ? "Healthy" : "Degraded",
                    Components = new
                    {
                        Gallery = galleryHealth,
                        Sync = syncHealth,
                        CacheWarmup = warmupHealth
                    },
                    CheckedAt = DateTime.UtcNow
                };

                return Ok(overallHealth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting admin health check");
                return StatusCode(500, new { error = "Health check failed" });
            }
        }

        /// <summary>
        /// Get performance metrics dashboard
        /// </summary>
        [HttpGet("performance")]
        public async Task<ActionResult<object>> GetPerformanceMetrics([FromQuery] int hours = 24)
        {
            try
            {
                var timeWindow = TimeSpan.FromHours(hours);
                var systemMetrics = await _performanceMetrics.GetSystemMetricsAsync(timeWindow);
                var endpointMetrics = await _performanceMetrics.GetEndpointMetricsAsync(timeWindow);
                var cacheMetrics = await _performanceMetrics.GetCacheMetricsAsync(timeWindow);
                var databaseMetrics = await _performanceMetrics.GetDatabaseMetricsAsync(timeWindow);
                var comparisonResult = await _performanceMetrics.CompareWithBaselineAsync();

                var dashboard = new
                {
                    TimeWindow = timeWindow,
                    System = systemMetrics,
                    Endpoints = endpointMetrics,
                    Cache = cacheMetrics,
                    Database = databaseMetrics,
                    Comparison = comparisonResult,
                    GeneratedAt = DateTime.UtcNow
                };

                return Ok(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance metrics");
                return StatusCode(500, new { error = "Failed to get performance metrics" });
            }
        }

        /// <summary>
        /// Get cache statistics and management
        /// </summary>
        [HttpGet("cache/stats")]
        public async Task<ActionResult<CacheStats>> GetCacheStats()
        {
            try
            {
                var stats = await _galleryService.GetCacheStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache stats");
                return StatusCode(500, new { error = "Failed to get cache stats" });
            }
        }

        /// <summary>
        /// Warm up cache for specific category
        /// </summary>
        [HttpPost("cache/warmup/{category}")]
        public async Task<ActionResult<object>> WarmupCache(string category, [FromQuery] int imageCount = 50)
        {
            try
            {
                if (!IsValidCategory(category))
                {
                    return BadRequest(new { error = "Invalid category" });
                }

                var result = await _cacheWarmupService.WarmupCategoryAsync(category);
                await _galleryService.WarmCacheAsync(category, imageCount);

                return Ok(new
                {
                    Success = result.Success,
                    Category = category,
                    ItemsWarmedUp = result.WarmedUpItems,
                    Duration = result.Duration,
                    Message = $"Cache warmed up for {category} category"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error warming up cache for category {Category}", category);
                return StatusCode(500, new { error = "Cache warmup failed" });
            }
        }

        /// <summary>
        /// Invalidate cache for category or specific image
        /// </summary>
        [HttpDelete("cache")]
        public async Task<ActionResult<object>> InvalidateCache(
            [FromQuery] string? category = null,
            [FromQuery] long? imageId = null,
            [FromQuery] string? storageKey = null)
        {
            try
            {
                await _galleryService.InvalidateCacheAsync(category, imageId, storageKey);

                return Ok(new
                {
                    Success = true,
                    Message = "Cache invalidated successfully",
                    Category = category,
                    ImageId = imageId,
                    StorageKey = storageKey,
                    InvalidatedAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating cache");
                return StatusCode(500, new { error = "Cache invalidation failed" });
            }
        }

        /// <summary>
        /// Clear all cache (use with extreme caution)
        /// </summary>
        [HttpDelete("cache/all")]
        public async Task<ActionResult<object>> ClearAllCache()
        {
            try
            {
                await _galleryService.ClearAllCacheAsync();

                _logger.LogWarning("All cache cleared by admin user: {UserId}", User.Identity?.Name);

                return Ok(new
                {
                    Success = true,
                    Message = "All cache cleared - performance will be temporarily impacted",
                    ClearedAt = DateTime.UtcNow,
                    Warning = "Cache will rebuild automatically as requests are made"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all cache");
                return StatusCode(500, new { error = "Cache clear failed" });
            }
        }

        /// <summary>
        /// Get B2 sync status
        /// </summary>
        [HttpGet("sync/status")]
        public async Task<ActionResult<SyncStatus>> GetSyncStatus()
        {
            try
            {
                var status = await _syncService.GetSyncStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync status");
                return StatusCode(500, new { error = "Failed to get sync status" });
            }
        }

        /// <summary>
        /// Get recent sync logs
        /// </summary>
        [HttpGet("sync/logs")]
        public async Task<ActionResult<List<B2SyncLog>>> GetSyncLogs([FromQuery] int count = 50)
        {
            try
            {
                if (count < 1 || count > 500)
                {
                    return BadRequest(new { error = "Count must be between 1 and 500" });
                }

                var logs = await _syncService.GetRecentSyncLogsAsync(count);
                return Ok(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync logs");
                return StatusCode(500, new { error = "Failed to get sync logs" });
            }
        }

        /// <summary>
        /// Retry failed sync operations
        /// </summary>
        [HttpPost("sync/retry-failed")]
        public async Task<ActionResult<BatchSyncResult>> RetryFailedSyncOperations()
        {
            try
            {
                var result = await _syncService.RetryFailedOperationsAsync();
                
                _logger.LogInformation("Retry failed sync operations completed: {Successful}/{Total} successful",
                    result.SuccessfulItems, result.TotalItems);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrying failed sync operations");
                return StatusCode(500, new { error = "Retry failed sync operations failed" });
            }
        }

        /// <summary>
        /// Perform consistency verification
        /// </summary>
        [HttpPost("sync/verify")]
        public async Task<ActionResult<VerificationResult>> VerifyConsistency(
            [FromQuery] string? category = null,
            [FromQuery] int batchSize = 100)
        {
            try
            {
                if (batchSize < 1 || batchSize > 1000)
                {
                    return BadRequest(new { error = "Batch size must be between 1 and 1000" });
                }

                if (!string.IsNullOrEmpty(category) && !IsValidCategory(category))
                {
                    return BadRequest(new { error = "Invalid category" });
                }

                var result = await _syncService.VerifyConsistencyAsync(category, batchSize);
                
                _logger.LogInformation("Consistency verification completed: {Consistent}/{Total} consistent",
                    result.ConsistentEntries, result.TotalChecked);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying consistency");
                return StatusCode(500, new { error = "Consistency verification failed" });
            }
        }

        /// <summary>
        /// Get slowest operations for optimization
        /// </summary>
        [HttpGet("performance/slow-operations")]
        public async Task<ActionResult<List<SlowOperationReport>>> GetSlowestOperations([FromQuery] int count = 10)
        {
            try
            {
                if (count < 1 || count > 50)
                {
                    return BadRequest(new { error = "Count must be between 1 and 50" });
                }

                var slowOperations = await _performanceMetrics.GetSlowestOperationsAsync(count);
                return Ok(slowOperations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting slowest operations");
                return StatusCode(500, new { error = "Failed to get slowest operations" });
            }
        }

        /// <summary>
        /// Generate performance report
        /// </summary>
        [HttpGet("performance/report")]
        public async Task<ActionResult<PerformanceReport>> GeneratePerformanceReport(
            [FromQuery] int hours = 24,
            [FromQuery] ReportType reportType = ReportType.Summary)
        {
            try
            {
                var timeWindow = TimeSpan.FromHours(hours);
                var report = await _performanceMetrics.GenerateReportAsync(timeWindow, reportType);
                
                return Ok(report);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating performance report");
                return StatusCode(500, new { error = "Failed to generate performance report" });
            }
        }

        /// <summary>
        /// Get SLA compliance status
        /// </summary>
        [HttpGet("performance/sla")]
        public async Task<ActionResult<SlaComplianceResult>> GetSlaCompliance()
        {
            try
            {
                var compliance = await _performanceMetrics.CheckSlaComplianceAsync();
                return Ok(compliance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking SLA compliance");
                return StatusCode(500, new { error = "Failed to check SLA compliance" });
            }
        }

        /// <summary>
        /// Get active performance alerts
        /// </summary>
        [HttpGet("performance/alerts")]
        public async Task<ActionResult<List<PerformanceAlert>>> GetPerformanceAlerts()
        {
            try
            {
                var alerts = await _performanceMetrics.GetActiveAlertsAsync();
                return Ok(alerts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting performance alerts");
                return StatusCode(500, new { error = "Failed to get performance alerts" });
            }
        }

        /// <summary>
        /// Check if category is valid
        /// </summary>
        private bool IsValidCategory(string category)
        {
            var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
            return validCategories.Contains(category.ToLower());
        }
    }
}
