using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for managing appointments
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize]
    public class AdminAppointmentController : ControllerBase
    {
        private readonly IAppointmentService _appointmentService;
        private readonly IClientService _clientService;
        private readonly ILogger<AdminAppointmentController> _logger;

        /// <summary>
        /// Constructor for the appointment controller
        /// </summary>
        /// <param name="appointmentService">Appointment service</param>
        /// <param name="clientService">Client service</param>
        /// <param name="logger">Logger</param>
        public AdminAppointmentController(
            IAppointmentService appointmentService,
            IClientService clientService,
            ILogger<AdminAppointmentController> logger)
        {
            _appointmentService = appointmentService;
            _clientService = clientService;
            _logger = logger;
        }

        /// <summary>
        /// Get all appointments
        /// </summary>
        /// <param name="includeCompleted">Whether to include completed appointments</param>
        /// <param name="includeCancelled">Whether to include cancelled appointments</param>
        /// <returns>List of appointments</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllAppointments(
            [FromQuery] bool includeCompleted = true,
            [FromQuery] bool includeCancelled = false)
        {
            try
            {
                var appointments = await _appointmentService.GetAllAppointmentsAsync(includeCompleted, includeCancelled);
                return Ok(new
                {
                    success = true,
                    appointments,
                    count = appointments.Count(),
                    includeCompleted,
                    includeCancelled
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all appointments");
                return StatusCode(500, new { message = "An error occurred while retrieving appointments" });
            }
        }

        /// <summary>
        /// Get appointment by ID
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <returns>Appointment details</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetAppointmentById(int id)
        {
            try
            {
                var appointment = await _appointmentService.GetAppointmentByIdAsync(id);
                if (appointment == null)
                {
                    return NotFound(new { message = "Appointment not found" });
                }

                return Ok(new
                {
                    success = true,
                    appointment
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointment by ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving appointment" });
            }
        }

        /// <summary>
        /// Get appointments for a specific client
        /// </summary>
        /// <param name="clientId">Client ID</param>
        /// <param name="includeCompleted">Whether to include completed appointments</param>
        /// <param name="includeCancelled">Whether to include cancelled appointments</param>
        /// <returns>List of client's appointments</returns>
        [HttpGet("client/{clientId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetAppointmentsByClientId(
            int clientId,
            [FromQuery] bool includeCompleted = true,
            [FromQuery] bool includeCancelled = false)
        {
            try
            {
                // Verify client exists
                var client = await _clientService.GetClientByIdAsync(clientId);
                if (client == null)
                {
                    return NotFound(new { message = $"Client with ID {clientId} not found" });
                }

                var appointments = await _appointmentService.GetAppointmentsByClientIdAsync(clientId, includeCompleted, includeCancelled);

                return Ok(new
                {
                    success = true,
                    appointments,
                    count = appointments.Count(),
                    client,
                    includeCompleted,
                    includeCancelled
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments for client: {ClientId}", clientId);
                return StatusCode(500, new { message = "An error occurred while retrieving appointments" });
            }
        }

        /// <summary>
        /// Get appointments for a specific date range
        /// </summary>
        /// <param name="startDate">Start date</param>
        /// <param name="endDate">End date</param>
        /// <param name="includeCompleted">Whether to include completed appointments</param>
        /// <param name="includeCancelled">Whether to include cancelled appointments</param>
        /// <returns>List of appointments in the date range</returns>
        [HttpGet("daterange")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> GetAppointmentsByDateRange(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate,
            [FromQuery] bool includeCompleted = true,
            [FromQuery] bool includeCancelled = false)
        {
            try
            {
                if (endDate < startDate)
                {
                    return BadRequest(new { message = "End date must be after start date" });
                }

                var appointments = await _appointmentService.GetAppointmentsByDateRangeAsync(startDate, endDate, includeCompleted, includeCancelled);

                return Ok(new
                {
                    success = true,
                    appointments,
                    count = appointments.Count(),
                    startDate,
                    endDate,
                    includeCompleted,
                    includeCancelled
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting appointments for date range: {StartDate} to {EndDate}", startDate, endDate);
                return StatusCode(500, new { message = "An error occurred while retrieving appointments" });
            }
        }

        /// <summary>
        /// Create a new appointment
        /// </summary>
        /// <param name="request">Appointment creation request</param>
        /// <returns>Created appointment</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateAppointment([FromBody] CreateAppointmentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get current admin user ID
                var admin = HttpContext.GetAdminUser();
                if (admin == null)
                {
                    return Unauthorized(new { message = "Admin user not found" });
                }

                var appointment = await _appointmentService.CreateAppointmentAsync(request, admin.Id);

                return CreatedAtAction(nameof(GetAppointmentById), new { id = appointment.Id }, new
                {
                    success = true,
                    appointment,
                    message = "Appointment created successfully"
                });
            }
            catch (KeyNotFoundException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating appointment");
                return StatusCode(500, new { message = "An error occurred while creating appointment" });
            }
        }

        /// <summary>
        /// Update an existing appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="request">Appointment update request</param>
        /// <returns>Updated appointment</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateAppointment(int id, [FromBody] UpdateAppointmentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var appointment = await _appointmentService.UpdateAppointmentAsync(id, request);

                return Ok(new
                {
                    success = true,
                    appointment,
                    message = "Appointment updated successfully"
                });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating appointment: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating appointment" });
            }
        }

        /// <summary>
        /// Cancel an appointment
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="notes">Optional notes about cancellation</param>
        /// <returns>Cancelled appointment</returns>
        [HttpPost("{id}/cancel")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CancelAppointment(int id, [FromBody] string? notes = null)
        {
            try
            {
                var appointment = await _appointmentService.CancelAppointmentAsync(id, notes);

                return Ok(new
                {
                    success = true,
                    appointment,
                    message = "Appointment cancelled successfully"
                });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling appointment: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while cancelling appointment" });
            }
        }

        /// <summary>
        /// Mark an appointment as completed
        /// </summary>
        /// <param name="id">Appointment ID</param>
        /// <param name="notes">Optional notes about completion</param>
        /// <returns>Completed appointment</returns>
        [HttpPost("{id}/complete")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> CompleteAppointment(int id, [FromBody] string? notes = null)
        {
            try
            {
                var appointment = await _appointmentService.CompleteAppointmentAsync(id, notes);

                return Ok(new
                {
                    success = true,
                    appointment,
                    message = "Appointment marked as completed successfully"
                });
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing appointment: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while completing appointment" });
            }
        }

        /// <summary>
        /// Get upcoming appointments
        /// </summary>
        /// <param name="days">Number of days to look ahead</param>
        /// <returns>List of upcoming appointments</returns>
        [HttpGet("upcoming")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUpcomingAppointments([FromQuery] int days = 7)
        {
            try
            {
                var appointments = await _appointmentService.GetUpcomingAppointmentsAsync(days);

                return Ok(new
                {
                    success = true,
                    appointments,
                    count = appointments.Count(),
                    days
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting upcoming appointments for the next {Days} days", days);
                return StatusCode(500, new { message = "An error occurred while retrieving upcoming appointments" });
            }
        }

        /// <summary>
        /// Send reminder emails for upcoming appointments
        /// </summary>
        /// <param name="days">Number of days to look ahead</param>
        /// <returns>Number of reminders sent</returns>
        [HttpPost("send-reminders")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SendAppointmentReminders([FromQuery] int days = 1)
        {
            try
            {
                var remindersSent = await _appointmentService.SendAppointmentRemindersAsync(days);

                return Ok(new
                {
                    success = true,
                    remindersSent,
                    days,
                    message = $"Sent {remindersSent} appointment reminders"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending appointment reminders for the next {Days} days", days);
                return StatusCode(500, new { message = "An error occurred while sending appointment reminders" });
            }
        }
    }
}
