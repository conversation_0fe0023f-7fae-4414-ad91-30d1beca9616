using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Attributes;
using YendorCats.API.Models;
using YendorCats.API.Services;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// Controller for managing clients
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [AdminAuthorize]
    public class AdminClientController : ControllerBase
    {
        private readonly IClientService _clientService;
        private readonly ILogger<AdminClientController> _logger;

        /// <summary>
        /// Constructor for the client controller
        /// </summary>
        /// <param name="clientService">Client service</param>
        /// <param name="logger">Logger</param>
        public AdminClientController(IClientService clientService, ILogger<AdminClientController> logger)
        {
            _clientService = clientService;
            _logger = logger;
        }

        /// <summary>
        /// Get all clients
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive clients</param>
        /// <returns>List of clients</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllClients([FromQuery] bool includeInactive = false)
        {
            try
            {
                var clients = await _clientService.GetAllClientsAsync(includeInactive);
                return Ok(new
                {
                    success = true,
                    clients,
                    count = clients.Count(),
                    includeInactive
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all clients");
                return StatusCode(500, new { message = "An error occurred while retrieving clients" });
            }
        }

        /// <summary>
        /// Get client by ID
        /// </summary>
        /// <param name="id">Client ID</param>
        /// <returns>Client details</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetClientById(int id)
        {
            try
            {
                var client = await _clientService.GetClientByIdAsync(id);
                if (client == null)
                {
                    return NotFound(new { message = "Client not found" });
                }

                return Ok(new
                {
                    success = true,
                    client
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client by ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving client" });
            }
        }

        /// <summary>
        /// Create a new client
        /// </summary>
        /// <param name="request">Client creation request</param>
        /// <returns>Created client</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateClient([FromBody] CreateClientRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var client = await _clientService.CreateClientAsync(request);

                return CreatedAtAction(nameof(GetClientById), new { id = client.Id }, new
                {
                    success = true,
                    client,
                    message = "Client created successfully"
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client");
                return StatusCode(500, new { message = "An error occurred while creating client" });
            }
        }

        /// <summary>
        /// Update an existing client
        /// </summary>
        /// <param name="id">Client ID</param>
        /// <param name="request">Client update request</param>
        /// <returns>Updated client</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateClient(int id, [FromBody] UpdateClientRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var client = await _clientService.UpdateClientAsync(id, request);

                return Ok(new
                {
                    success = true,
                    client,
                    message = "Client updated successfully"
                });
            }
            catch (KeyNotFoundException)
            {
                return NotFound(new { message = $"Client with ID {id} not found" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating client: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating client" });
            }
        }

        /// <summary>
        /// Delete a client (mark as inactive)
        /// </summary>
        /// <param name="id">Client ID</param>
        /// <returns>Success result</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteClient(int id)
        {
            try
            {
                var result = await _clientService.DeleteClientAsync(id);
                if (!result)
                {
                    return NotFound(new { message = $"Client with ID {id} not found" });
                }

                return Ok(new
                {
                    success = true,
                    message = "Client marked as inactive successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting client: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting client" });
            }
        }

        /// <summary>
        /// Search clients by name or email
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="includeInactive">Whether to include inactive clients</param>
        /// <returns>List of matching clients</returns>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SearchClients([FromQuery] string searchTerm, [FromQuery] bool includeInactive = false)
        {
            try
            {
                var clients = await _clientService.SearchClientsAsync(searchTerm, includeInactive);
                return Ok(new
                {
                    success = true,
                    clients,
                    count = clients.Count(),
                    searchTerm,
                    includeInactive
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching clients with term: {SearchTerm}", searchTerm);
                return StatusCode(500, new { message = "An error occurred while searching clients" });
            }
        }
    }
}
