using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Diagnostics;
using YendorCats.API.Services.Gallery;
using YendorCats.API.Services.Performance;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Controllers
{
    /// <summary>
    /// High-performance Gallery API v2 Controller
    /// Optimized for 85-90% performance improvement over S3 scanning
    /// Target: sub-500ms response times with multi-level caching
    /// </summary>
    [ApiController]
    [Route("api/v2/gallery")]
    [Produces("application/json")]
    public class GalleryV2Controller : ControllerBase
    {
        private readonly IGalleryService _galleryService;
        private readonly IPerformanceMetricsService _performanceMetrics;
        private readonly ILogger<GalleryV2Controller> _logger;

        public GalleryV2Controller(
            IGalleryService galleryService,
            IPerformanceMetricsService performanceMetrics,
            ILogger<GalleryV2Controller> logger)
        {
            _galleryService = galleryService;
            _performanceMetrics = performanceMetrics;
            _logger = logger;
        }

        /// <summary>
        /// Get paginated images for a category with high-performance caching
        /// </summary>
        /// <param name="category">Category name (studs, queens, kittens, gallery)</param>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Items per page (max 100)</param>
        /// <param name="sortBy">Sort field (DateTaken, DateUploaded, CatName, Title, FileSize, AccessCount)</param>
        /// <param name="descending">Sort direction</param>
        /// <param name="useCache">Whether to use caching</param>
        /// <returns>Paginated gallery images</returns>
        [HttpGet("{category}")]
        [ResponseCache(Duration = 300, Location = ResponseCacheLocation.Any, VaryByQueryKeys = new[] { "page", "pageSize", "sortBy", "descending" })]
        public async Task<ActionResult<PagedResult<GalleryImageDto>>> GetCategoryImages(
            string category,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "DateTaken",
            [FromQuery] bool descending = true,
            [FromQuery] bool useCache = true)
        {
            var stopwatch = Stopwatch.StartNew();
            var requestId = HttpContext.TraceIdentifier;
            
            try
            {
                // Validate parameters
                var validationResult = ValidatePaginationParameters(page, pageSize);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { error = validationResult.ErrorMessage });
                }

                if (!IsValidCategory(category))
                {
                    return BadRequest(new { error = "Invalid category. Valid categories: studs, queens, kittens, gallery" });
                }

                if (!IsValidSortField(sortBy))
                {
                    return BadRequest(new { error = "Invalid sort field" });
                }

                _logger.LogInformation("Gallery V2 request: Category={Category}, Page={Page}, PageSize={PageSize}, Sort={SortBy}",
                    category, page, pageSize, sortBy);

                // Get images with caching
                var result = await _galleryService.GetCategoryImagesAsync(category, page, pageSize, sortBy, descending, useCache);
                
                stopwatch.Stop();
                
                // Add performance headers
                AddPerformanceHeaders(result, stopwatch.Elapsed, requestId);
                
                // Record performance metrics
                await _performanceMetrics.RecordApiRequestAsync(
                    $"GET /api/v2/gallery/{category}", 
                    "GET", 
                    stopwatch.Elapsed, 
                    true, 
                    result.CacheSource);

                _logger.LogInformation("Gallery V2 response: Category={Category}, Items={Count}, Time={Time}ms, Cache={CacheSource}",
                    category, result.ItemCount, stopwatch.Elapsed.TotalMilliseconds, result.CacheSource);

                return Ok(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                await _performanceMetrics.RecordApiRequestAsync(
                    $"GET /api/v2/gallery/{category}", 
                    "GET", 
                    stopwatch.Elapsed, 
                    false);

                _logger.LogError(ex, "Error getting category images: Category={Category}, Page={Page}", category, page);
                return StatusCode(500, new { error = "Internal server error", requestId });
            }
        }

        /// <summary>
        /// Get lightweight category images for mobile/list views
        /// </summary>
        [HttpGet("{category}/lightweight")]
        [ResponseCache(Duration = 120, Location = ResponseCacheLocation.Any, VaryByQueryKeys = new[] { "page", "pageSize", "sortBy", "descending" })]
        public async Task<ActionResult<PagedResult<GalleryImageDto>>> GetCategoryImagesLightweight(
            string category,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "DateTaken",
            [FromQuery] bool descending = true)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var validationResult = ValidatePaginationParameters(page, pageSize);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { error = validationResult.ErrorMessage });
                }

                if (!IsValidCategory(category))
                {
                    return BadRequest(new { error = "Invalid category" });
                }

                var result = await _galleryService.GetCategoryImagesLightweightAsync(category, page, pageSize, sortBy, descending);
                
                stopwatch.Stop();
                AddPerformanceHeaders(result, stopwatch.Elapsed, HttpContext.TraceIdentifier);
                
                await _performanceMetrics.RecordApiRequestAsync(
                    $"GET /api/v2/gallery/{category}/lightweight", 
                    "GET", 
                    stopwatch.Elapsed, 
                    true, 
                    result.CacheSource);

                return Ok(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                await _performanceMetrics.RecordApiRequestAsync(
                    $"GET /api/v2/gallery/{category}/lightweight", 
                    "GET", 
                    stopwatch.Elapsed, 
                    false);

                _logger.LogError(ex, "Error getting lightweight category images: Category={Category}", category);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Get single image by ID
        /// </summary>
        [HttpGet("image/{id:long}")]
        [ResponseCache(Duration = 600, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<GalleryImageDto>> GetImageById(long id, [FromQuery] bool useCache = true)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var image = await _galleryService.GetImageByIdAsync(id, useCache);
                
                if (image == null)
                {
                    return NotFound(new { error = "Image not found", id });
                }

                stopwatch.Stop();
                
                // Track access for analytics
                _ = Task.Run(async () => await _galleryService.IncrementAccessCountAsync(id));
                
                Response.Headers.Add("X-Query-Time", stopwatch.Elapsed.TotalMilliseconds.ToString("F0"));
                Response.Headers.Add("X-Cache-Source", image.CacheSource ?? "unknown");
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/image/{id}", 
                    "GET", 
                    stopwatch.Elapsed, 
                    true, 
                    image.CacheSource);

                return Ok(image);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/image/{id}", 
                    "GET", 
                    stopwatch.Elapsed, 
                    false);

                _logger.LogError(ex, "Error getting image by ID: {ImageId}", id);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Search images with full-text search
        /// </summary>
        [HttpGet("search")]
        [ResponseCache(Duration = 180, Location = ResponseCacheLocation.Any, VaryByQueryKeys = new[] { "q", "category", "page", "pageSize", "sortBy", "descending" })]
        public async Task<ActionResult<PagedResult<GalleryImageDto>>> SearchImages(
            [FromQuery] string q,
            [FromQuery] string? category = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string sortBy = "DateTaken",
            [FromQuery] bool descending = true,
            [FromQuery] bool useCache = true)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                if (string.IsNullOrWhiteSpace(q))
                {
                    return BadRequest(new { error = "Search query is required" });
                }

                var validationResult = ValidatePaginationParameters(page, pageSize);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { error = validationResult.ErrorMessage });
                }

                if (!string.IsNullOrEmpty(category) && !IsValidCategory(category))
                {
                    return BadRequest(new { error = "Invalid category" });
                }

                var result = await _galleryService.SearchImagesAsync(q, category, page, pageSize, sortBy, descending, useCache);
                
                stopwatch.Stop();
                AddPerformanceHeaders(result, stopwatch.Elapsed, HttpContext.TraceIdentifier);
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/search", 
                    "GET", 
                    stopwatch.Elapsed, 
                    true, 
                    result.CacheSource);

                return Ok(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/search", 
                    "GET", 
                    stopwatch.Elapsed, 
                    false);

                _logger.LogError(ex, "Error searching images: Query={Query}, Category={Category}", q, category);
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Get category statistics
        /// </summary>
        [HttpGet("stats")]
        [ResponseCache(Duration = 900, Location = ResponseCacheLocation.Any)]
        public async Task<ActionResult<Dictionary<string, int>>> GetCategoryStats([FromQuery] bool useCache = true)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var stats = await _galleryService.GetCategoryStatsAsync(useCache);
                
                stopwatch.Stop();
                Response.Headers.Add("X-Query-Time", stopwatch.Elapsed.TotalMilliseconds.ToString("F0"));
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/stats", 
                    "GET", 
                    stopwatch.Elapsed, 
                    true);

                return Ok(stats);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/stats", 
                    "GET", 
                    stopwatch.Elapsed, 
                    false);

                _logger.LogError(ex, "Error getting category stats");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Get popular images
        /// </summary>
        [HttpGet("popular")]
        [ResponseCache(Duration = 600, Location = ResponseCacheLocation.Any, VaryByQueryKeys = new[] { "count", "category" })]
        public async Task<ActionResult<List<GalleryImageDto>>> GetPopularImages(
            [FromQuery] int count = 10,
            [FromQuery] string? category = null,
            [FromQuery] bool useCache = true)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                if (count < 1 || count > 50)
                {
                    return BadRequest(new { error = "Count must be between 1 and 50" });
                }

                if (!string.IsNullOrEmpty(category) && !IsValidCategory(category))
                {
                    return BadRequest(new { error = "Invalid category" });
                }

                var images = await _galleryService.GetPopularImagesAsync(count, category, useCache);
                
                stopwatch.Stop();
                Response.Headers.Add("X-Query-Time", stopwatch.Elapsed.TotalMilliseconds.ToString("F0"));
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/popular", 
                    "GET", 
                    stopwatch.Elapsed, 
                    true);

                return Ok(images);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                await _performanceMetrics.RecordApiRequestAsync(
                    "GET /api/v2/gallery/popular", 
                    "GET", 
                    stopwatch.Elapsed, 
                    false);

                _logger.LogError(ex, "Error getting popular images");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }

        /// <summary>
        /// Add performance headers to response
        /// </summary>
        private void AddPerformanceHeaders(PagedResult<GalleryImageDto> result, TimeSpan queryTime, string requestId)
        {
            Response.Headers.Add("X-Total-Count", result.TotalCount.ToString());
            Response.Headers.Add("X-Page", result.Page.ToString());
            Response.Headers.Add("X-Page-Size", result.PageSize.ToString());
            Response.Headers.Add("X-Total-Pages", result.TotalPages.ToString());
            Response.Headers.Add("X-Cache-Source", result.CacheSource ?? "unknown");
            Response.Headers.Add("X-Query-Time", queryTime.TotalMilliseconds.ToString("F0"));
            Response.Headers.Add("X-Request-Id", requestId);
            Response.Headers.Add("X-Generated-At", result.GeneratedAt.ToString("O"));
            
            if (result.DatabaseQueryTime.HasValue)
            {
                Response.Headers.Add("X-DB-Query-Time", result.DatabaseQueryTime.Value.TotalMilliseconds.ToString("F0"));
            }
        }

        /// <summary>
        /// Validate pagination parameters
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidatePaginationParameters(int page, int pageSize)
        {
            return PagedResult<object>.ValidatePagination(page, pageSize, 100);
        }

        /// <summary>
        /// Check if category is valid
        /// </summary>
        private bool IsValidCategory(string category)
        {
            var validCategories = new[] { "studs", "queens", "kittens", "gallery" };
            return validCategories.Contains(category.ToLower());
        }

        /// <summary>
        /// Check if sort field is valid
        /// </summary>
        private bool IsValidSortField(string sortBy)
        {
            var validSortFields = new[] { "datetaken", "dateuploaded", "catname", "title", "filesize", "accesscount", "sortorder" };
            return validSortFields.Contains(sortBy.ToLower());
        }
    }
}
