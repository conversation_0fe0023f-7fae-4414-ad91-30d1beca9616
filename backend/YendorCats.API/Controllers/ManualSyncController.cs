using Microsoft.AspNetCore.Mvc;
using YendorCats.API.Data;
using YendorCats.API.Models;
using YendorCats.API.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;

namespace YendorCats.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [AllowAnonymous]
    public class ManualSyncController : ControllerBase
    {
        private readonly ILogger<ManualSyncController> _logger;
        private readonly AppDbContext _context;
        private readonly IPhotoIndexService _photoIndexService;
        private readonly IS3StorageService _s3StorageService;
        private readonly IMetadataSyncService _metadataSyncService;

        public ManualSyncController(
            ILogger<ManualSyncController> logger,
            AppDbContext context,
            IPhotoIndexService photoIndexService,
            IS3StorageService s3StorageService,
            IMetadataSyncService metadataSyncService)
        {
            _logger = logger;
            _context = context;
            _photoIndexService = photoIndexService;
            _s3StorageService = s3StorageService;
            _metadataSyncService = metadataSyncService;
        }

        [HttpPost("populate-from-known-structure")]
        public async Task<IActionResult> PopulateFromKnownStructure()
        {
            try
            {
                _logger.LogInformation("Starting manual population from known S3 structure");

                // Clear existing data
                await _context.CatGalleryImages.ExecuteDeleteAsync();
                await _context.SaveChangesAsync();
                _logger.LogInformation("Cleared existing gallery images");

                var imagesAdded = 0;
                var publicUrlTemplate = "https://f004.backblazeb2.com/file/yendor/{key}";

                // Queens category images
                var queensImages = new[]
                {
                    "queens/Cat1/TESTING-IMG_4275.jpg",
                    "queens/Cat1/TESTING-IMG_4400.jpg",
                    "queens/IMG_4406.jpg",
                    "queens/IMG_6277.jpg",
                    "queens/IMG_7867.jpg",
                    "queens/IMG_8520.jpg",
                    "queens/IMG_9260.jpg",
                    "queens/TESTING-IMG_4406.jpg",
                    "queens/TESTING-IMG_6277.jpg",
                    "queens/TESTING-IMG_7867 - Copy.jpg",
                    "queens/TESTING-IMG_8520.jpg",
                    "queens/TESTING-IMG_9260.jpg"
                };

                foreach (var imagePath in queensImages)
                {
                    try
                    {
                        var image = CreateImageFromPath(imagePath, "queens", publicUrlTemplate);
                        if (image != null)
                        {
                            _context.CatGalleryImages.Add(image);
                            imagesAdded++;
                            _logger.LogInformation("Added queen image: {ImagePath}", imagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error adding queen image: {ImagePath}", imagePath);
                    }
                }

                // Studs category images
                var studsImages = new[]
                {
                    "studs/Dennis/501052875_24370176559253409_1044655023082516492_n.jpg",
                    "studs/Dennis/508553919_24370176722586726_843428285223772910_n.jpg",
                    "studs/Dennis/509441678_24370176445920087_7305555872133503420_n.jpg",
                    "studs/Dennis/511009104_24370176572586741_2917073592025095125_n.jpg",
                    "studs/Dennis/511273385_24370176772586721_2403458704145963937_n.jpg",
                    "studs/Soren/501049857_24370542575883474_6494822973238495745_n.jpg",
                    "studs/Soren/509926341_24370542622550136_6573720351090828202_n.jpg",
                    "studs/Soren/511218592_24370542779216787_7392575042167449259_n (1).jpg",
                    "studs/Soren/511218592_24370542779216787_7392575042167449259_n.jpg",
                    "studs/Soren/511991955_24370542529216812_1432263306594410478_n (1).jpg",
                    "studs/Soren/511991955_24370542529216812_1432263306594410478_n.jpg",
                    "studs/Soren/512647539_24370542745883457_5378466661915711333_n.jpg",
                    "studs/louie/510586750_24370060715931660_903145896275112471_n.jpg",
                    "studs/louie/510810423_24370060692598329_3344585189072069690_n.jpg",
                    "studs/louie/510851562_24370060629265002_2305014083613095276_n.jpg",
                    "studs/louie/511025583_24370061139264951_3716431353154280156_n.jpg",
                    "studs/louie/511277439_24370061375931594_530508242522424627_n.jpg",
                    "studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg",
                    "studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg"
                };

                foreach (var imagePath in studsImages)
                {
                    try
                    {
                        var image = CreateImageFromPath(imagePath, "studs", publicUrlTemplate);
                        if (image != null)
                        {
                            _context.CatGalleryImages.Add(image);
                            imagesAdded++;
                            _logger.LogInformation("Added stud image: {ImagePath}", imagePath);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error adding stud image: {ImagePath}", imagePath);
                    }
                }

                _logger.LogInformation("About to save {ImagesAdded} images to database", imagesAdded);

                // Try using direct SQL with constraint disabling
                _logger.LogInformation("Trying to save images with direct SQL and disabled constraints");
                
                // First clear the existing images
                await _context.Database.ExecuteSqlRawAsync("DELETE FROM CatGalleryImages");
                
                // Disable foreign key checks
                await _context.Database.ExecuteSqlRawAsync("PRAGMA foreign_keys = OFF;");
                
                // Create a list of SQL parameters for our images
                var sqlParams = new List<Microsoft.Data.Sqlite.SqliteParameter>();
                var sql = new System.Text.StringBuilder();
                
                sql.AppendLine("INSERT INTO CatGalleryImages (");
                sql.AppendLine("    Filename, StorageKey, OriginalFileName, Category, StorageProvider, StorageBucketName,");
                sql.AppendLine("    CatId, CatName, S3Key, S3Bucket, S3Url, FileSize, Width, Height,");
                sql.AppendLine("    ContentType, MimeType, Format, FileFormat, DateModified, DateTaken, DateUploaded,");
                sql.AppendLine("    CreatedAt, UpdatedAt, DisplayOrder, SortOrder, IsActive, IsPublic, IsFeatured,");
                sql.AppendLine("    Breed, Gender, ViewCount, AccessCount, LikeCount, DownloadCount, Description, CreatedBy");
                sql.AppendLine(") VALUES ");
                
                int paramIndex = 0;
                int successCount = 0;
                
                foreach (var imagePath in queensImages.Concat(studsImages))
                {
                    var category = imagePath.StartsWith("queens/") ? "queens" : "studs";
                    var pathParts = imagePath.Split('/');
                    string catName;
                    string fileName;
                    
                    if (pathParts.Length == 2)
                    {
                        catName = "General";
                        fileName = pathParts[1];
                    }
                    else if (pathParts.Length == 3)
                    {
                        catName = pathParts[1];
                        fileName = pathParts[2];
                    }
                    else
                    {
                        continue;
                    }
                    
                    var s3Key = imagePath;
                    var publicUrl = publicUrlTemplate.Replace("{key}", s3Key);
                    var fileExtension = Path.GetExtension(fileName).TrimStart('.').ToLowerInvariant();
                    var mimeType = GetMimeType(fileName);
                    var now = DateTime.UtcNow;
                    var catId = Guid.NewGuid().ToString();
                    
                    // Skip if filename or storageKey is too long
                    if (fileName.Length > 255 || s3Key.Length > 500)
                    {
                        continue;
                    }
                    
                    if (successCount > 0)
                    {
                        sql.AppendLine(",");
                    }
                    
                    sql.Append("(");
                    for (int i = 0; i < 36; i++)
                    {
                        if (i > 0) sql.Append(", ");
                        sql.Append($"@p{paramIndex}");
                        paramIndex++;
                    }
                    sql.Append(")");
                    
                    // Add parameters in the same order as columns
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 36}", fileName));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 35}", s3Key));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 34}", fileName));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 33}", category));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 32}", "S3"));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 31}", "yendor"));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 30}", catId));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 29}", catName));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 28}", s3Key));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 27}", "yendor"));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 26}", publicUrl));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 25}", 1000000));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 24}", 800));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 23}", 600));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 22}", mimeType));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 21}", mimeType));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 20}", fileExtension));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 19}", fileExtension));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 18}", now));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 17}", now));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 16}", now));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 15}", now));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 14}", now));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 13}", 0));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 12}", 0));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 11}", true));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 10}", true));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 9}", false));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 8}", "Maine Coon"));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 7}", category == "studs" ? "M" : category == "queens" ? "F" : null));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 6}", 0));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 5}", 0));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 4}", 0));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 3}", 0));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 2}", $"Beautiful {catName} from our {category} collection"));
                    sqlParams.Add(new Microsoft.Data.Sqlite.SqliteParameter($"@p{paramIndex - 1}", "ManualSync"));
                    
                    successCount++;
                }
                
                if (successCount > 0)
                {
                    try
                    {
                        // Execute the SQL with all parameters
                        await _context.Database.ExecuteSqlRawAsync(sql.ToString(), sqlParams);
                        _logger.LogInformation("Successfully saved {Count} images via direct SQL", successCount);
                    }
                    catch (Exception sqlEx)
                    {
                        _logger.LogError(sqlEx, "Error executing direct SQL: {Error}", sqlEx.Message);
                    }
                    finally
                    {
                        // Re-enable foreign key checks
                        await _context.Database.ExecuteSqlRawAsync("PRAGMA foreign_keys = ON;");
                    }
                }

                // Also update the photo index
                await UpdatePhotoIndex();

                _logger.LogInformation("Manual population completed: {ImagesAdded} images added", imagesAdded);

                return Ok(new
                {
                    success = true,
                    message = $"Successfully populated {imagesAdded} images from known S3 structure",
                    imagesAdded = imagesAdded
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during manual population");
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        private CatGalleryImage? CreateImageFromPath(string s3Key, string category, string publicUrlTemplate)
        {
            try
            {
                var pathParts = s3Key.Split('/');
                string catName;
                string fileName;

                if (pathParts.Length == 2)
                {
                    // Direct in category: queens/IMG_4406.jpg
                    catName = "General";
                    fileName = pathParts[1];
                }
                else if (pathParts.Length == 3)
                {
                    // In cat subfolder: queens/Cat1/TESTING-IMG_4275.jpg
                    catName = pathParts[1];
                    fileName = pathParts[2];
                }
                else
                {
                    _logger.LogWarning("Invalid path structure: {S3Key}", s3Key);
                    return null;
                }

                var publicUrl = publicUrlTemplate.Replace("{key}", s3Key);
                var now = DateTime.UtcNow;
                var fileExtension = Path.GetExtension(fileName).TrimStart('.').ToLowerInvariant();
                var mimeType = GetMimeType(fileName);

                // Validate field lengths to avoid constraint violations
                if (fileName.Length > 255)
                {
                    _logger.LogError("Filename too long: {FileName}", fileName);
                    return null;
                }

                if (s3Key.Length > 500)
                {
                    _logger.LogError("StorageKey too long: {StorageKey}", s3Key);
                    return null;
                }

                var image = new CatGalleryImage
                {
                    // Required fields
                    Filename = fileName,
                    StorageKey = s3Key,
                    OriginalFileName = fileName,
                    Category = category,
                    StorageProvider = "S3",
                    StorageBucketName = "yendor",

                    // Cat information (no foreign key reference)
                    CatId = Guid.NewGuid().ToString(),  // Generate a unique ID
                    CatName = catName,

                    // S3 specific fields
                    S3Key = s3Key,
                    S3Bucket = "yendor",
                    S3Url = publicUrl,

                    // File properties
                    FileSize = 1000000,
                    Width = 800,
                    Height = 600,
                    ContentType = mimeType,
                    MimeType = mimeType,
                    Format = fileExtension,
                    FileFormat = fileExtension,

                    // Timestamps
                    DateModified = now,
                    DateTaken = now,
                    DateUploaded = now,
                    CreatedAt = now,
                    UpdatedAt = now,

                    // Display properties
                    DisplayOrder = 0,
                    SortOrder = 0,
                    IsActive = true,
                    IsPublic = true,
                    IsFeatured = false,

                    // Cat characteristics
                    Breed = "Maine Coon",
                    Gender = category == "studs" ? "M" : category == "queens" ? "F" : null,

                    // Analytics (initialize to zero)
                    ViewCount = 0,
                    AccessCount = 0,
                    LikeCount = 0,
                    DownloadCount = 0,

                    // Description
                    Description = $"Beautiful {catName} from our {category} collection",

                    // Audit
                    CreatedBy = "ManualSync"
                };

                _logger.LogDebug("Created image object for: {S3Key}", s3Key);
                return image;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating image from path: {S3Key}", s3Key);
                return null;
            }
        }

        private async Task UpdatePhotoIndex()
        {
            try
            {
                // Create photo index structure
                var index = new PhotoIndex
                {
                    LastUpdated = DateTime.UtcNow,
                    Categories = new Dictionary<string, Dictionary<string, List<string>>>(),
                    Metadata = new Dictionary<string, PhotoMetadata>()
                };

                // Get all images from database
                var allImages = await _context.CatGalleryImages.ToListAsync();

                foreach (var image in allImages)
                {
                    if (!index.Categories.ContainsKey(image.Category))
                    {
                        index.Categories[image.Category] = new Dictionary<string, List<string>>();
                    }

                    if (!index.Categories[image.Category].ContainsKey(image.CatName ?? "General"))
                    {
                        index.Categories[image.Category][image.CatName ?? "General"] = new List<string>();
                    }

                    index.Categories[image.Category][image.CatName ?? "General"].Add(image.Filename);

                    // Add metadata
                    if (!string.IsNullOrEmpty(image.StorageKey))
                    {
                        index.Metadata[image.StorageKey] = new PhotoMetadata
                        {
                            CatName = image.CatName ?? "General",
                            Age = image.Age.ToString(),
                            Description = image.Description ?? "",
                            Breed = image.Breed ?? "Maine Coon",
                            Gender = image.Gender ?? "",
                            Color = image.Color ?? "",
                            DateTaken = image.DateTaken?.ToString("yyyy-MM-dd") ?? "",
                            Personality = image.Personality ?? "",
                            Bloodline = image.Bloodline ?? ""
                        };
                    }
                }

                // Save the index file
                var indexFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "PhotoIndex.json");
                var directory = Path.GetDirectoryName(indexFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = System.Text.Json.JsonSerializer.Serialize(index, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                });

                await System.IO.File.WriteAllTextAsync(indexFilePath, json);
                _logger.LogInformation("Photo index updated successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating photo index");
            }
        }

        private string GetMimeType(string filename)
        {
            var extension = Path.GetExtension(filename).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".webp" => "image/webp",
                ".bmp" => "image/bmp",
                _ => "image/jpeg"
            };
        }

        [HttpGet("status")]
        public async Task<IActionResult> GetStatus()
        {
            var totalImages = await _context.CatGalleryImages.CountAsync();
            var categoryCounts = await _context.CatGalleryImages
                .GroupBy(i => i.Category)
                .Select(g => new { Category = g.Key, Count = g.Count() })
                .ToListAsync();

            return Ok(new
            {
                totalImages = totalImages,
                categoryCounts = categoryCounts,
                timestamp = DateTime.UtcNow
            });
        }
        
        [HttpGet("dynamic-refresh")]
        public async Task<IActionResult> DynamicRefresh()
        {
            try
            {
                _logger.LogInformation("Starting dynamic refresh of photo index from S3");
                
                // Test S3 connection first
                var connectionOk = await _s3StorageService.TestConnectionAsync();
                if (!connectionOk)
                {
                    _logger.LogError("S3 connection test failed. Cannot refresh index.");
                    return StatusCode(500, new { success = false, message = "S3 connection test failed. Check credentials and connection." });
                }
                
                // Step 1: Trigger immediate metadata sync from S3 to database
                var syncResult = await _metadataSyncService.TriggerImmediateSyncAsync();
                
                if (!syncResult.Success)
                {
                    _logger.LogError("Metadata sync failed: {Message}", syncResult.Message);
                    return StatusCode(500, new { 
                        success = false, 
                        message = "Metadata sync failed", 
                        errors = syncResult.Errors 
                    });
                }
                
                // Step 2: Refresh the photo index using PhotoIndexService
                var refreshResult = await _photoIndexService.RefreshIndexAsync();
                
                // Get some stats about the updated index
                var categories = new[] { "studs", "queens", "kittens" };
                var categoryCounts = new Dictionary<string, int>();
                
                foreach (var category in categories)
                {
                    var images = await _photoIndexService.GetCategoryPhotosAsync(category);
                    categoryCounts[category] = images.Count;
                }
                
                return Ok(new { 
                    success = true, 
                    message = "Photo index dynamically refreshed from S3", 
                    syncStats = new {
                        categoriesProcessed = syncResult.CategoriesProcessed,
                        imagesProcessed = syncResult.ImagesProcessed,
                        imagesAdded = syncResult.ImagesAdded,
                        imagesUpdated = syncResult.ImagesUpdated,
                        duration = syncResult.Duration.TotalSeconds
                    },
                    indexRefreshed = refreshResult,
                    categoryCounts = categoryCounts
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error dynamically refreshing photo index");
                return StatusCode(500, new { success = false, message = $"Error: {ex.Message}" });
            }
        }
        
        [HttpGet("list-s3-contents")]
        public async Task<IActionResult> ListS3Contents(string category = "")
        {
            try
            {
                _logger.LogInformation("Listing S3 contents for category: {Category}", 
                    string.IsNullOrEmpty(category) ? "all" : category);
                
                var prefix = string.IsNullOrEmpty(category) ? "" : $"{category}/";
                var files = await _s3StorageService.ListFilesAsync(prefix);
                
                var groupedFiles = files
                    .Select(file => new {
                        Key = file.Key,
                        Size = file.Size,
                        LastModified = file.LastModified,
                        Path = file.Key.Split('/'),
                        IsImage = IsImageFile(file.Key)
                    })
                    .Where(file => file.IsImage)
                    .GroupBy(file => {
                        if (file.Path.Length >= 1) {
                            return file.Path[0]; // Group by first path segment (category)
                        }
                        return "unknown";
                    })
                    .ToDictionary(
                        g => g.Key,
                        g => g.GroupBy(file => {
                            if (file.Path.Length >= 2) {
                                return file.Path.Length >= 3 ? file.Path[1] : "General"; // Group by second path segment (cat name)
                            }
                            return "unknown";
                        }).ToDictionary(
                            sg => sg.Key,
                            sg => sg.Select(file => new {
                                FileName = file.Path.Last(),
                                Size = file.Size,
                                LastModified = file.LastModified
                            }).ToList()
                        )
                    );
                
                return Ok(new { 
                    success = true, 
                    totalFiles = files.Count,
                    imageFilesCount = files.Count(f => IsImageFile(f.Key)),
                    groupedFiles = groupedFiles
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error listing S3 contents");
                return StatusCode(500, new { success = false, message = $"Error: {ex.Message}" });
            }
        }
        
        private bool IsImageFile(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp" }.Contains(extension);
        }
    }

    public class PhotoIndex
    {
        public DateTime LastUpdated { get; set; }
        public Dictionary<string, Dictionary<string, List<string>>> Categories { get; set; } = new();
        public Dictionary<string, PhotoMetadata> Metadata { get; set; } = new();
    }

    public class PhotoMetadata
    {
        public string CatName { get; set; } = "";
        public string Age { get; set; } = "";
        public string Description { get; set; } = "";
        public string Breed { get; set; } = "";
        public string Gender { get; set; } = "";
        public string Color { get; set; } = "";
        public string DateTaken { get; set; } = "";
        public string Personality { get; set; } = "";
        public string Bloodline { get; set; } = "";
    }
}
