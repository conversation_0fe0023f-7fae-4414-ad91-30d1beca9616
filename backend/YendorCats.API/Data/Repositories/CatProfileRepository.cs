using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Data.Repositories
{
    /// <summary>
    /// Cat profile repository implementation with breeding program support
    /// Optimized for pedigree tracking and breeding management
    /// </summary>
    public class CatProfileRepository : ICatProfileRepository
    {
        private readonly AppDbContext _context;
        private readonly ILogger<CatProfileRepository> _logger;

        public CatProfileRepository(AppDbContext context, ILogger<CatProfileRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        // Core CRUD operations
        public async Task<CatProfile?> GetByIdAsync(int id)
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.Id == id);
        }
        
        public async Task<CatProfile?> GetByCatIdAsync(string catId)
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.CatId == catId);
        }
        
        public async Task<List<CatProfile>> GetByCatIdsAsync(IEnumerable<string> catIds)
        {
            var catIdList = catIds.ToList();
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => catIdList.Contains(p.CatId))
                .ToListAsync();
        }
        
        public async Task<bool> ExistsAsync(string catId)
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .AnyAsync(p => p.CatId == catId);
        }
        
        public async Task<CatProfile> AddAsync(CatProfile profile)
        {
            profile.CreatedAt = DateTime.UtcNow;
            profile.UpdatedAt = DateTime.UtcNow;
            
            _context.CatProfiles.Add(profile);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Added new cat profile: {CatId} - {CatName}", profile.CatId, profile.CatName);
            return profile;
        }
        
        public async Task<CatProfile> UpdateAsync(CatProfile profile)
        {
            profile.UpdatedAt = DateTime.UtcNow;
            
            _context.CatProfiles.Update(profile);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Updated cat profile: {CatId} - {CatName}", profile.CatId, profile.CatName);
            return profile;
        }
        
        public async Task<bool> DeleteAsync(int id)
        {
            var profile = await _context.CatProfiles.FindAsync(id);
            if (profile == null)
                return false;
            
            _context.CatProfiles.Remove(profile);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Deleted cat profile: {CatId} - {CatName}", profile.CatId, profile.CatName);
            return true;
        }
        
        public async Task<List<CatProfile>> BulkUpdateAsync(IEnumerable<CatProfile> profiles)
        {
            var profileList = profiles.ToList();
            var utcNow = DateTime.UtcNow;
            
            foreach (var profile in profileList)
            {
                profile.UpdatedAt = utcNow;
            }
            
            _context.CatProfiles.UpdateRange(profileList);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Bulk updated {Count} cat profiles", profileList.Count);
            return profileList;
        }
        
        public async Task<List<CatProfile>> BulkInsertAsync(IEnumerable<CatProfile> profiles)
        {
            var profileList = profiles.ToList();
            var utcNow = DateTime.UtcNow;
            
            foreach (var profile in profileList)
            {
                profile.CreatedAt = utcNow;
                profile.UpdatedAt = utcNow;
            }
            
            _context.CatProfiles.AddRange(profileList);
            await _context.SaveChangesAsync();
            
            _logger.LogInformation("Bulk inserted {Count} cat profiles", profileList.Count);
            return profileList;
        }
        
        // Listing and pagination
        
        public async Task<PagedResult<CatProfile>> GetAllAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true)
        {
            var query = _context.CatProfiles.AsNoTracking();
            
            // Apply filters
            if (activeOnly)
                query = query.Where(p => p.IsActive);
            
            if (publicOnly)
                query = query.Where(p => p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByBreedingStatusAsync(
            string breedingStatus,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.BreedingStatus == breedingStatus && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByAvailabilityStatusAsync(
            string availabilityStatus,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.AvailabilityStatus == availabilityStatus && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByGenderAsync(
            string gender,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Gender == gender && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByBreedAsync(
            string breed,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Breed == breed && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByBloodlineAsync(
            string bloodline,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Bloodline == bloodline && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        // Search functionality
        
        public async Task<PagedResult<CatProfile>> SearchAsync(
            string query,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var searchQuery = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic);
            
            // Apply search filters
            if (!string.IsNullOrWhiteSpace(query))
            {
                var searchTerm = query.ToLower();
                searchQuery = searchQuery.Where(p => 
                    p.CatName.ToLower().Contains(searchTerm) ||
                    p.CatId.ToLower().Contains(searchTerm) ||
                    (p.RegisteredName != null && p.RegisteredName.ToLower().Contains(searchTerm)) ||
                    (p.Breed != null && p.Breed.ToLower().Contains(searchTerm)) ||
                    (p.Bloodline != null && p.Bloodline.ToLower().Contains(searchTerm)) ||
                    (p.Notes != null && p.Notes.ToLower().Contains(searchTerm)) ||
                    (p.SireName != null && p.SireName.ToLower().Contains(searchTerm)) ||
                    (p.DamName != null && p.DamName.ToLower().Contains(searchTerm)));
            }
            
            // Apply sorting
            searchQuery = ApplySorting(searchQuery, sortBy, descending);
            
            // Get total count
            var totalCount = await searchQuery.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await searchQuery.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByAgeRangeAsync(
            int minAge,
            int maxAge,
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true)
        {
            var today = DateTime.UtcNow;
            var minBirthDate = today.AddDays(-maxAge);
            var maxBirthDate = today.AddDays(-minAge);
            
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.BirthDate >= minBirthDate && p.BirthDate <= maxBirthDate && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByTagsAsync(
            IEnumerable<string> tags,
            bool matchAll = false,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var tagList = tags.Select(t => t.ToLower()).ToList();
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic);
            
            // Apply tag filtering
            if (matchAll)
            {
                // All tags must be present
                foreach (var tag in tagList)
                {
                    query = query.Where(p => p.Tags != null && p.Tags.ToLower().Contains(tag));
                }
            }
            else
            {
                // Any tag must be present
                query = query.Where(p => p.Tags != null && tagList.Any(tag => p.Tags.ToLower().Contains(tag)));
            }
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        // Pedigree and breeding operations
        
        public async Task<PagedResult<CatProfile>> GetOffspringAsync(
            string parentCatId,
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => (p.SireId == parentCatId || p.DamId == parentCatId) && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetSiblingsAsync(
            string catId,
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true)
        {
            var cat = await _context.CatProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.CatId == catId);
            
            if (cat == null)
                return new PagedResult<CatProfile>();
            
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.CatId != catId && 
                           ((p.SireId == cat.SireId && cat.SireId != null) || 
                            (p.DamId == cat.DamId && cat.DamId != null)) && 
                           p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PedigreeInfo> GetPedigreeAsync(string catId, int generations = 3)
        {
            var cat = await _context.CatProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.CatId == catId);
            
            if (cat == null)
                return new PedigreeInfo();
            
            var pedigreeInfo = new PedigreeInfo
            {
                Cat = cat,
                GenerationsRetrieved = generations
            };
            
            // Get immediate parents
            if (!string.IsNullOrEmpty(cat.SireId))
            {
                pedigreeInfo.Father = await _context.CatProfiles
                    .AsNoTracking()
                    .FirstOrDefaultAsync(p => p.CatId == cat.SireId);
            }
            
            if (!string.IsNullOrEmpty(cat.DamId))
            {
                pedigreeInfo.Mother = await _context.CatProfiles
                    .AsNoTracking()
                    .FirstOrDefaultAsync(p => p.CatId == cat.DamId);
            }
            
            // Get grandparents
            if (pedigreeInfo.Father != null)
            {
                if (!string.IsNullOrEmpty(pedigreeInfo.Father.SireId))
                {
                    pedigreeInfo.PaternalGrandfather = await _context.CatProfiles
                        .AsNoTracking()
                        .FirstOrDefaultAsync(p => p.CatId == pedigreeInfo.Father.SireId);
                }
                
                if (!string.IsNullOrEmpty(pedigreeInfo.Father.DamId))
                {
                    pedigreeInfo.PaternalGrandmother = await _context.CatProfiles
                        .AsNoTracking()
                        .FirstOrDefaultAsync(p => p.CatId == pedigreeInfo.Father.DamId);
                }
            }
            
            if (pedigreeInfo.Mother != null)
            {
                if (!string.IsNullOrEmpty(pedigreeInfo.Mother.SireId))
                {
                    pedigreeInfo.MaternalGrandfather = await _context.CatProfiles
                        .AsNoTracking()
                        .FirstOrDefaultAsync(p => p.CatId == pedigreeInfo.Mother.SireId);
                }
                
                if (!string.IsNullOrEmpty(pedigreeInfo.Mother.DamId))
                {
                    pedigreeInfo.MaternalGrandmother = await _context.CatProfiles
                        .AsNoTracking()
                        .FirstOrDefaultAsync(p => p.CatId == pedigreeInfo.Mother.DamId);
                }
            }
            
            // Get extended ancestors for additional generations
            if (generations > 3)
            {
                var ancestorIds = new List<string>();
                
                // Collect all ancestor IDs from grandparents
                if (pedigreeInfo.PaternalGrandfather != null)
                {
                    if (!string.IsNullOrEmpty(pedigreeInfo.PaternalGrandfather.SireId))
                        ancestorIds.Add(pedigreeInfo.PaternalGrandfather.SireId);
                    if (!string.IsNullOrEmpty(pedigreeInfo.PaternalGrandfather.DamId))
                        ancestorIds.Add(pedigreeInfo.PaternalGrandfather.DamId);
                }
                
                if (pedigreeInfo.PaternalGrandmother != null)
                {
                    if (!string.IsNullOrEmpty(pedigreeInfo.PaternalGrandmother.SireId))
                        ancestorIds.Add(pedigreeInfo.PaternalGrandmother.SireId);
                    if (!string.IsNullOrEmpty(pedigreeInfo.PaternalGrandmother.DamId))
                        ancestorIds.Add(pedigreeInfo.PaternalGrandmother.DamId);
                }
                
                if (pedigreeInfo.MaternalGrandfather != null)
                {
                    if (!string.IsNullOrEmpty(pedigreeInfo.MaternalGrandfather.SireId))
                        ancestorIds.Add(pedigreeInfo.MaternalGrandfather.SireId);
                    if (!string.IsNullOrEmpty(pedigreeInfo.MaternalGrandfather.DamId))
                        ancestorIds.Add(pedigreeInfo.MaternalGrandfather.DamId);
                }
                
                if (pedigreeInfo.MaternalGrandmother != null)
                {
                    if (!string.IsNullOrEmpty(pedigreeInfo.MaternalGrandmother.SireId))
                        ancestorIds.Add(pedigreeInfo.MaternalGrandmother.SireId);
                    if (!string.IsNullOrEmpty(pedigreeInfo.MaternalGrandmother.DamId))
                        ancestorIds.Add(pedigreeInfo.MaternalGrandmother.DamId);
                }
                
                if (ancestorIds.Any())
                {
                    pedigreeInfo.ExtendedAncestors = await _context.CatProfiles
                        .AsNoTracking()
                        .Where(p => ancestorIds.Contains(p.CatId))
                        .ToListAsync();
                }
            }
            
            // Check if pedigree is complete
            pedigreeInfo.IsComplete = pedigreeInfo.Father != null && pedigreeInfo.Mother != null;
            
            return pedigreeInfo;
        }
        
        public async Task<PagedResult<CatProfile>> GetAvailableBreedingPartnersAsync(
            string catId,
            int page = 1,
            int pageSize = 20)
        {
            var cat = await _context.CatProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.CatId == catId);
            
            if (cat == null)
                return new PagedResult<CatProfile>();
            
            // Get opposite gender cats that are available for breeding
            var oppositeGender = cat.Gender == "M" ? "F" : "M";
            
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Gender == oppositeGender && 
                           p.BreedingStatus == "Available" && 
                           p.IsActive && p.IsPublic && 
                           p.CatId != catId);
            
            // Apply sorting by breeding suitability
            query = query.OrderBy(p => p.CatName);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<BreedingHistory> GetBreedingHistoryAsync(string catId)
        {
            var cat = await _context.CatProfiles
                .AsNoTracking()
                .FirstOrDefaultAsync(p => p.CatId == catId);
            
            if (cat == null)
                return new BreedingHistory();
            
            // Get offspring
            var offspring = await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.SireId == catId || p.DamId == catId)
                .OrderBy(p => p.BirthDate)
                .ToListAsync();
            
            // Get breeding partners
            var partnerIds = offspring
                .Select(o => cat.Gender == "M" ? o.DamId : o.SireId)
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()
                .ToList();
            
            var partners = await _context.CatProfiles
                .AsNoTracking()
                .Where(p => partnerIds.Contains(p.CatId))
                .ToListAsync();
            
            // Group offspring by litter (same birth date and breeding partner)
            var breedingRecords = new List<BreedingRecord>();
            
            foreach (var partner in partners)
            {
                var litterOffspring = offspring
                    .Where(o => (cat.Gender == "M" ? o.DamId : o.SireId) == partner.CatId)
                    .GroupBy(o => o.BirthDate?.Date)
                    .ToList();
                
                foreach (var litter in litterOffspring)
                {
                    var litterDate = litter.Key;
                    var litterList = litter.ToList();
                    
                    breedingRecords.Add(new BreedingRecord
                    {
                        Partner = partner,
                        LitterDate = litterDate,
                        LitterSize = litterList.Count,
                        Offspring = litterList,
                        BreedingDate = litterDate?.AddDays(-65) ?? DateTime.MinValue // Estimate breeding date
                    });
                }
            }
            
            var history = new BreedingHistory
            {
                Cat = cat,
                BreedingRecords = breedingRecords.OrderBy(r => r.BreedingDate).ToList(),
                TotalLitters = breedingRecords.Count,
                TotalOffspring = offspring.Count,
                FirstBreedingDate = breedingRecords.Any() ? breedingRecords.Min(r => r.BreedingDate) : null,
                LastBreedingDate = breedingRecords.Any() ? breedingRecords.Max(r => r.BreedingDate) : null,
                BreedingPartners = partners,
                Offspring = offspring
            };
            
            return history;
        }
        
        // Specialized queries
        
        public async Task<PagedResult<CatProfile>> GetKittensAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true)
        {
            var oneYearAgo = DateTime.UtcNow.AddYears(-1);
            
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.BirthDate >= oneYearAgo && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetAvailableStudsAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Gender == "M" && p.BreedingStatus == "Available" && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetAvailableQueensAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Gender == "F" && p.BreedingStatus == "Available" && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetChampionsAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.Titles != null && p.Titles.Contains("Champion") && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetFeaturedAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsFeatured && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetRetiredAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.BreedingStatus == "Retired" && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = ApplySorting(query, sortBy, descending);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        // Statistics and analytics
        
        public async Task<Dictionary<string, int>> GetBreedingStatusStatsAsync()
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic)
                .GroupBy(p => p.BreedingStatus)
                .ToDictionaryAsync(g => g.Key ?? "Unknown", g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetAvailabilityStatusStatsAsync()
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic)
                .GroupBy(p => p.AvailabilityStatus)
                .ToDictionaryAsync(g => g.Key ?? "Unknown", g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetBreedStatsAsync()
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic)
                .GroupBy(p => p.Breed)
                .ToDictionaryAsync(g => g.Key ?? "Unknown", g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetBloodlineStatsAsync()
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic)
                .GroupBy(p => p.Bloodline)
                .ToDictionaryAsync(g => g.Key ?? "Unknown", g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetGenderStatsAsync()
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic)
                .GroupBy(p => p.Gender)
                .ToDictionaryAsync(g => g.Key ?? "Unknown", g => g.Count());
        }
        
        public async Task<Dictionary<string, int>> GetAgeDistributionStatsAsync()
        {
            var profiles = await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && p.IsPublic && p.BirthDate != null)
                .Select(p => p.BirthDate)
                .ToListAsync();
            
            var today = DateTime.UtcNow;
            var ageDistribution = new Dictionary<string, int>
            {
                ["0-1 years"] = 0,
                ["1-2 years"] = 0,
                ["2-5 years"] = 0,
                ["5-10 years"] = 0,
                ["10+ years"] = 0
            };
            
            foreach (var birthDate in profiles)
            {
                if (birthDate == null) continue;
                
                var age = today.Year - birthDate.Value.Year;
                if (today.DayOfYear < birthDate.Value.DayOfYear)
                    age--;
                
                switch (age)
                {
                    case < 1:
                        ageDistribution["0-1 years"]++;
                        break;
                    case < 2:
                        ageDistribution["1-2 years"]++;
                        break;
                    case < 5:
                        ageDistribution["2-5 years"]++;
                        break;
                    case < 10:
                        ageDistribution["5-10 years"]++;
                        break;
                    default:
                        ageDistribution["10+ years"]++;
                        break;
                }
            }
            
            return ageDistribution;
        }
        
        public async Task<int> GetTotalCountAsync(bool activeOnly = true, bool publicOnly = true)
        {
            var query = _context.CatProfiles.AsNoTracking();
            
            if (activeOnly)
                query = query.Where(p => p.IsActive);
            
            if (publicOnly)
                query = query.Where(p => p.IsPublic);
            
            return await query.CountAsync();
        }
        
        public async Task<int> GetCountByBreedingStatusAsync(string breedingStatus)
        {
            return await _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.BreedingStatus == breedingStatus && p.IsActive && p.IsPublic)
                .CountAsync();
        }
        
        public async Task<PagedResult<CatProfile>> GetProfilesWithMissingInfoAsync(
            int page = 1,
            int pageSize = 20)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && 
                           (p.BirthDate == null || 
                            string.IsNullOrEmpty(p.Breed) || 
                            string.IsNullOrEmpty(p.Gender) ||
                            string.IsNullOrEmpty(p.BreedingStatus)));
            
            // Apply sorting
            query = query.OrderBy(p => p.CatName);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetProfilesNeedingHealthCheckAsync(
            int page = 1,
            int pageSize = 20)
        {
            var sixMonthsAgo = DateTime.UtcNow.AddMonths(-6);
            
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.IsActive && 
                           (p.LastHealthCheck == null || p.LastHealthCheck < sixMonthsAgo));
            
            // Apply sorting by last health check date
            query = query.OrderBy(p => p.LastHealthCheck ?? DateTime.MinValue);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<bool> UpdateDisplayOrdersAsync(Dictionary<int, int> profileIds)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                var ids = profileIds.Keys.ToList();
                var profiles = await _context.CatProfiles
                    .Where(p => ids.Contains(p.Id))
                    .ToListAsync();
                
                foreach (var profile in profiles)
                {
                    if (profileIds.TryGetValue(profile.Id, out var displayOrder))
                    {
                        profile.DisplayOrder = displayOrder;
                        profile.UpdatedAt = DateTime.UtcNow;
                    }
                }
                
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                
                _logger.LogInformation("Updated display order for {Count} profiles", profiles.Count);
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to update display orders");
                return false;
            }
        }
        
        public async Task<PagedResult<CatProfile>> GetByRegistrationBodyAsync(
            string registrationBody,
            int page = 1,
            int pageSize = 20)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.RegistrationBody == registrationBody && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = query.OrderBy(p => p.CatName);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        public async Task<PagedResult<CatProfile>> GetByLocationAsync(
            string location,
            int page = 1,
            int pageSize = 20)
        {
            var query = _context.CatProfiles
                .AsNoTracking()
                .Where(p => p.CurrentLocation == location && p.IsActive && p.IsPublic);
            
            // Apply sorting
            query = query.OrderBy(p => p.CatName);
            
            // Get total count
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var skip = (page - 1) * pageSize;
            var items = await query.Skip(skip).Take(pageSize).ToListAsync();
            
            return new PagedResult<CatProfile>
            {
                Items = items,
                Page = page,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < Math.Ceiling(totalCount / (double)pageSize)
            };
        }
        
        // Helper methods
        
        private IQueryable<CatProfile> ApplySorting(IQueryable<CatProfile> query, string sortBy, bool descending)
        {
            return sortBy.ToLower() switch
            {
                "catname" => descending ? query.OrderByDescending(p => p.CatName) : query.OrderBy(p => p.CatName),
                "catid" => descending ? query.OrderByDescending(p => p.CatId) : query.OrderBy(p => p.CatId),
                "birthdate" => descending ? query.OrderByDescending(p => p.BirthDate) : query.OrderBy(p => p.BirthDate),
                "breed" => descending ? query.OrderByDescending(p => p.Breed) : query.OrderBy(p => p.Breed),
                "gender" => descending ? query.OrderByDescending(p => p.Gender) : query.OrderBy(p => p.Gender),
                "breedingstatus" => descending ? query.OrderByDescending(p => p.BreedingStatus) : query.OrderBy(p => p.BreedingStatus),
                "availabilitystatus" => descending ? query.OrderByDescending(p => p.AvailabilityStatus) : query.OrderBy(p => p.AvailabilityStatus),
                "createdat" => descending ? query.OrderByDescending(p => p.CreatedAt) : query.OrderBy(p => p.CreatedAt),
                "updatedat" => descending ? query.OrderByDescending(p => p.UpdatedAt) : query.OrderBy(p => p.UpdatedAt),
                "displayorder" => descending ? query.OrderByDescending(p => p.DisplayOrder) : query.OrderBy(p => p.DisplayOrder),
                _ => descending ? query.OrderByDescending(p => p.CatName) : query.OrderBy(p => p.CatName)
            };
        }
    }
}