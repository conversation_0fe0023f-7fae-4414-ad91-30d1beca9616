using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Data.Repositories
{
    /// <summary>
    /// Repository interface for cat profile operations with breeding program support
    /// Optimized for pedigree tracking and breeding management
    /// </summary>
    public interface ICatProfileRepository
    {
        // Core CRUD operations
        
        /// <summary>
        /// Get a single cat profile by ID
        /// </summary>
        /// <param name="id">The cat profile ID</param>
        /// <returns>The cat profile entity or null if not found</returns>
        Task<CatProfile?> GetByIdAsync(int id);
        
        /// <summary>
        /// Get a cat profile by cat ID
        /// </summary>
        /// <param name="catId">The cat ID</param>
        /// <returns>The cat profile entity or null if not found</returns>
        Task<CatProfile?> GetByCatIdAsync(string catId);
        
        /// <summary>
        /// Get multiple cat profiles by cat IDs
        /// </summary>
        /// <param name="catIds">The cat IDs to retrieve</param>
        /// <returns>List of cat profiles</returns>
        Task<List<CatProfile>> GetByCatIdsAsync(IEnumerable<string> catIds);
        
        /// <summary>
        /// Check if a cat profile exists by cat ID
        /// </summary>
        /// <param name="catId">The cat ID to check</param>
        /// <returns>True if the profile exists, false otherwise</returns>
        Task<bool> ExistsAsync(string catId);
        
        /// <summary>
        /// Add a new cat profile
        /// </summary>
        /// <param name="profile">The cat profile to add</param>
        /// <returns>The added cat profile with generated ID</returns>
        Task<CatProfile> AddAsync(CatProfile profile);
        
        /// <summary>
        /// Update an existing cat profile
        /// </summary>
        /// <param name="profile">The cat profile to update</param>
        /// <returns>The updated cat profile</returns>
        Task<CatProfile> UpdateAsync(CatProfile profile);
        
        /// <summary>
        /// Delete a cat profile by ID
        /// </summary>
        /// <param name="id">The cat profile ID to delete</param>
        /// <returns>True if deleted, false if not found</returns>
        Task<bool> DeleteAsync(int id);
        
        /// <summary>
        /// Bulk update multiple cat profiles
        /// </summary>
        /// <param name="profiles">The cat profiles to update</param>
        /// <returns>The updated cat profiles</returns>
        Task<List<CatProfile>> BulkUpdateAsync(IEnumerable<CatProfile> profiles);
        
        /// <summary>
        /// Bulk insert multiple cat profiles
        /// </summary>
        /// <param name="profiles">The cat profiles to insert</param>
        /// <returns>The inserted cat profiles with generated IDs</returns>
        Task<List<CatProfile>> BulkInsertAsync(IEnumerable<CatProfile> profiles);
        
        // Listing and pagination
        
        /// <summary>
        /// Get all cat profiles with pagination and filtering
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by (CatName, BirthDate, BreedingStatus)</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <param name="activeOnly">Whether to include only active profiles</param>
        /// <param name="publicOnly">Whether to include only public profiles</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetAllAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false,
            bool activeOnly = true,
            bool publicOnly = true);
        
        /// <summary>
        /// Get cat profiles by breeding status
        /// </summary>
        /// <param name="breedingStatus">The breeding status to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByBreedingStatusAsync(
            string breedingStatus,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get cat profiles by availability status
        /// </summary>
        /// <param name="availabilityStatus">The availability status to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByAvailabilityStatusAsync(
            string availabilityStatus,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get cat profiles by gender
        /// </summary>
        /// <param name="gender">The gender to filter by (M/F)</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByGenderAsync(
            string gender,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get cat profiles by breed
        /// </summary>
        /// <param name="breed">The breed to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByBreedAsync(
            string breed,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get cat profiles by bloodline
        /// </summary>
        /// <param name="bloodline">The bloodline to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByBloodlineAsync(
            string bloodline,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        // Search functionality
        
        /// <summary>
        /// Search cat profiles by text query
        /// Searches across cat name, registered name, breed, bloodline, and notes
        /// </summary>
        /// <param name="query">The search query</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> SearchAsync(
            string query,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get cat profiles by age range
        /// </summary>
        /// <param name="minAge">The minimum age in days</param>
        /// <param name="maxAge">The maximum age in days</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByAgeRangeAsync(
            int minAge,
            int maxAge,
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true);
        
        /// <summary>
        /// Get cat profiles by tags
        /// </summary>
        /// <param name="tags">The tags to search for</param>
        /// <param name="matchAll">Whether to match all tags or any tag</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of cat profiles</returns>
        Task<PagedResult<CatProfile>> GetByTagsAsync(
            IEnumerable<string> tags,
            bool matchAll = false,
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        // Pedigree and breeding operations
        
        /// <summary>
        /// Get offspring of a cat profile
        /// </summary>
        /// <param name="parentCatId">The parent cat ID</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of offspring profiles</returns>
        Task<PagedResult<CatProfile>> GetOffspringAsync(
            string parentCatId,
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true);
        
        /// <summary>
        /// Get siblings of a cat profile
        /// </summary>
        /// <param name="catId">The cat ID</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of sibling profiles</returns>
        Task<PagedResult<CatProfile>> GetSiblingsAsync(
            string catId,
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true);
        
        /// <summary>
        /// Get pedigree information for a cat profile
        /// </summary>
        /// <param name="catId">The cat ID</param>
        /// <param name="generations">The number of generations to retrieve</param>
        /// <returns>Pedigree information with ancestors</returns>
        Task<PedigreeInfo> GetPedigreeAsync(string catId, int generations = 3);
        
        /// <summary>
        /// Get available breeding partners for a cat
        /// </summary>
        /// <param name="catId">The cat ID</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of potential breeding partners</returns>
        Task<PagedResult<CatProfile>> GetAvailableBreedingPartnersAsync(
            string catId,
            int page = 1,
            int pageSize = 20);
        
        /// <summary>
        /// Get breeding history for a cat
        /// </summary>
        /// <param name="catId">The cat ID</param>
        /// <returns>Breeding history information</returns>
        Task<BreedingHistory> GetBreedingHistoryAsync(string catId);
        
        // Specialized queries
        
        /// <summary>
        /// Get kittens (cats under 1 year old)
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of kitten profiles</returns>
        Task<PagedResult<CatProfile>> GetKittensAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "BirthDate",
            bool descending = true);
        
        /// <summary>
        /// Get available studs
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of available stud profiles</returns>
        Task<PagedResult<CatProfile>> GetAvailableStudsAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get available queens
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of available queen profiles</returns>
        Task<PagedResult<CatProfile>> GetAvailableQueensAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get champions (cats with champion titles)
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of champion profiles</returns>
        Task<PagedResult<CatProfile>> GetChampionsAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        /// <summary>
        /// Get featured cats
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of featured profiles</returns>
        Task<PagedResult<CatProfile>> GetFeaturedAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "DisplayOrder",
            bool descending = false);
        
        /// <summary>
        /// Get retired cats
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of retired profiles</returns>
        Task<PagedResult<CatProfile>> GetRetiredAsync(
            int page = 1,
            int pageSize = 20,
            string sortBy = "CatName",
            bool descending = false);
        
        // Statistics and analytics
        
        /// <summary>
        /// Get breeding status statistics
        /// </summary>
        /// <returns>Dictionary of breeding status to counts</returns>
        Task<Dictionary<string, int>> GetBreedingStatusStatsAsync();
        
        /// <summary>
        /// Get availability status statistics
        /// </summary>
        /// <returns>Dictionary of availability status to counts</returns>
        Task<Dictionary<string, int>> GetAvailabilityStatusStatsAsync();
        
        /// <summary>
        /// Get breed statistics
        /// </summary>
        /// <returns>Dictionary of breed to counts</returns>
        Task<Dictionary<string, int>> GetBreedStatsAsync();
        
        /// <summary>
        /// Get bloodline statistics
        /// </summary>
        /// <returns>Dictionary of bloodline to counts</returns>
        Task<Dictionary<string, int>> GetBloodlineStatsAsync();
        
        /// <summary>
        /// Get gender statistics
        /// </summary>
        /// <returns>Dictionary of gender to counts</returns>
        Task<Dictionary<string, int>> GetGenderStatsAsync();
        
        /// <summary>
        /// Get age distribution statistics
        /// </summary>
        /// <returns>Dictionary of age ranges to counts</returns>
        Task<Dictionary<string, int>> GetAgeDistributionStatsAsync();
        
        /// <summary>
        /// Get total count of profiles
        /// </summary>
        /// <param name="activeOnly">Whether to count only active profiles</param>
        /// <param name="publicOnly">Whether to count only public profiles</param>
        /// <returns>The total count</returns>
        Task<int> GetTotalCountAsync(bool activeOnly = true, bool publicOnly = true);
        
        /// <summary>
        /// Get count by breeding status
        /// </summary>
        /// <param name="breedingStatus">The breeding status to count</param>
        /// <returns>The count</returns>
        Task<int> GetCountByBreedingStatusAsync(string breedingStatus);
        
        /// <summary>
        /// Get profiles with missing information
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of profiles with missing information</returns>
        Task<PagedResult<CatProfile>> GetProfilesWithMissingInfoAsync(
            int page = 1,
            int pageSize = 20);
        
        /// <summary>
        /// Get profiles needing health check
        /// </summary>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of profiles needing health check</returns>
        Task<PagedResult<CatProfile>> GetProfilesNeedingHealthCheckAsync(
            int page = 1,
            int pageSize = 20);
        
        /// <summary>
        /// Update display order for multiple profiles
        /// </summary>
        /// <param name="profileIds">The profile IDs with their new display orders</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateDisplayOrdersAsync(Dictionary<int, int> profileIds);
        
        /// <summary>
        /// Get profiles by registration body
        /// </summary>
        /// <param name="registrationBody">The registration body to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of profiles</returns>
        Task<PagedResult<CatProfile>> GetByRegistrationBodyAsync(
            string registrationBody,
            int page = 1,
            int pageSize = 20);
        
        /// <summary>
        /// Get profiles by current location
        /// </summary>
        /// <param name="location">The location to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of profiles</returns>
        Task<PagedResult<CatProfile>> GetByLocationAsync(
            string location,
            int page = 1,
            int pageSize = 20);
    }
    
    /// <summary>
    /// Pedigree information with ancestors
    /// </summary>
    public class PedigreeInfo
    {
        public CatProfile Cat { get; set; } = new();
        public CatProfile? Father { get; set; }
        public CatProfile? Mother { get; set; }
        public CatProfile? PaternalGrandfather { get; set; }
        public CatProfile? PaternalGrandmother { get; set; }
        public CatProfile? MaternalGrandfather { get; set; }
        public CatProfile? MaternalGrandmother { get; set; }
        public List<CatProfile> ExtendedAncestors { get; set; } = new();
        public int GenerationsRetrieved { get; set; }
        public bool IsComplete { get; set; }
    }
    
    /// <summary>
    /// Breeding history information
    /// </summary>
    public class BreedingHistory
    {
        public CatProfile Cat { get; set; } = new();
        public List<BreedingRecord> BreedingRecords { get; set; } = new();
        public int TotalLitters { get; set; }
        public int TotalOffspring { get; set; }
        public DateTime? FirstBreedingDate { get; set; }
        public DateTime? LastBreedingDate { get; set; }
        public List<CatProfile> BreedingPartners { get; set; } = new();
        public List<CatProfile> Offspring { get; set; } = new();
    }
    
    /// <summary>
    /// Individual breeding record
    /// </summary>
    public class BreedingRecord
    {
        public DateTime BreedingDate { get; set; }
        public CatProfile Partner { get; set; } = new();
        public DateTime? LitterDate { get; set; }
        public int? LitterSize { get; set; }
        public List<CatProfile> Offspring { get; set; } = new();
        public string? Notes { get; set; }
    }
}