using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using YendorCats.API.Models;
using YendorCats.API.Models.DTOs;

namespace YendorCats.API.Data.Repositories
{
    /// <summary>
    /// High-performance repository interface for gallery image operations
    /// Optimized for the hybrid storage architecture with comprehensive caching support
    /// </summary>
    public interface IGalleryRepository
    {
        // Core CRUD operations
        
        /// <summary>
        /// Get a single gallery image by ID
        /// </summary>
        /// <param name="id">The gallery image ID</param>
        /// <returns>The gallery image entity or null if not found</returns>
        Task<CatGalleryImage?> GetByIdAsync(long id);
        
        /// <summary>
        /// Get a gallery image by storage key
        /// </summary>
        /// <param name="storageKey">The storage key (S3 key or B2 key)</param>
        /// <returns>The gallery image entity or null if not found</returns>
        Task<CatGalleryImage?> GetByStorageKeyAsync(string storageKey);
        
        /// <summary>
        /// Check if a gallery image exists by storage key
        /// </summary>
        /// <param name="storageKey">The storage key to check</param>
        /// <returns>True if the image exists, false otherwise</returns>
        Task<bool> ExistsAsync(string storageKey);
        
        /// <summary>
        /// Add a new gallery image
        /// </summary>
        /// <param name="image">The gallery image to add</param>
        /// <returns>The added gallery image with generated ID</returns>
        Task<CatGalleryImage> AddAsync(CatGalleryImage image);
        
        /// <summary>
        /// Update an existing gallery image
        /// </summary>
        /// <param name="image">The gallery image to update</param>
        /// <returns>The updated gallery image</returns>
        Task<CatGalleryImage> UpdateAsync(CatGalleryImage image);
        
        /// <summary>
        /// Delete a gallery image by ID
        /// </summary>
        /// <param name="id">The gallery image ID to delete</param>
        /// <returns>True if deleted, false if not found</returns>
        Task<bool> DeleteAsync(long id);
        
        /// <summary>
        /// Bulk update multiple gallery images
        /// </summary>
        /// <param name="images">The gallery images to update</param>
        /// <returns>The updated gallery images</returns>
        Task<List<CatGalleryImage>> BulkUpdateAsync(IEnumerable<CatGalleryImage> images);
        
        /// <summary>
        /// Bulk insert multiple gallery images
        /// </summary>
        /// <param name="images">The gallery images to insert</param>
        /// <returns>The inserted gallery images with generated IDs</returns>
        Task<List<CatGalleryImage>> BulkInsertAsync(IEnumerable<CatGalleryImage> images);
        
        // High-performance category queries
        
        /// <summary>
        /// Get gallery images by category with pagination and sorting
        /// PERFORMANCE CRITICAL - Target: <200ms response time
        /// </summary>
        /// <param name="category">The category to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by (DateTaken, CatName, SortOrder)</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <param name="activeOnly">Whether to include only active images</param>
        /// <param name="publicOnly">Whether to include only public images</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetCategoryImagesAsync(
            string category, 
            int page = 1, 
            int pageSize = 20, 
            string sortBy = "DateTaken", 
            bool descending = true,
            bool activeOnly = true,
            bool publicOnly = true);
        
        /// <summary>
        /// Get gallery images by multiple categories
        /// </summary>
        /// <param name="categories">The categories to include</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetMultipleCategoriesAsync(
            IEnumerable<string> categories,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        /// <summary>
        /// Get gallery images by cat ID
        /// </summary>
        /// <param name="catId">The cat ID to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetByCatIdAsync(
            string catId,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true,
            bool activeOnly = false,
            bool publicOnly = false);
        
        /// <summary>
        /// Get gallery images by cat name
        /// </summary>
        /// <param name="catName">The cat name to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetByCatNameAsync(
            string catName,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        // Search and filtering
        
        /// <summary>
        /// Search gallery images by text query
        /// Searches across cat name, title, description, breed, bloodline, and tags
        /// </summary>
        /// <param name="query">The search query</param>
        /// <param name="category">Optional category filter</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> SearchAsync(
            string query,
            string? category = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        /// <summary>
        /// Get gallery images by breed
        /// </summary>
        /// <param name="breed">The breed to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetByBreedAsync(
            string breed,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        /// <summary>
        /// Get gallery images by bloodline
        /// </summary>
        /// <param name="bloodline">The bloodline to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetByBloodlineAsync(
            string bloodline,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        /// <summary>
        /// Get gallery images by date range
        /// </summary>
        /// <param name="startDate">The start date (inclusive)</param>
        /// <param name="endDate">The end date (inclusive)</param>
        /// <param name="category">Optional category filter</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetByDateRangeAsync(
            DateTime startDate,
            DateTime endDate,
            string? category = null,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        /// <summary>
        /// Get gallery images by tags
        /// </summary>
        /// <param name="tags">The tags to search for</param>
        /// <param name="matchAll">Whether to match all tags or any tag</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <param name="sortBy">The field to sort by</param>
        /// <param name="descending">Whether to sort in descending order</param>
        /// <returns>Paged result of gallery images</returns>
        Task<PagedResult<CatGalleryImage>> GetByTagsAsync(
            IEnumerable<string> tags,
            bool matchAll = false,
            int page = 1,
            int pageSize = 20,
            string sortBy = "DateTaken",
            bool descending = true);
        
        // Analytics and statistics
        
        /// <summary>
        /// Get the total count of images in a category
        /// </summary>
        /// <param name="category">The category to count</param>
        /// <param name="activeOnly">Whether to count only active images</param>
        /// <param name="publicOnly">Whether to count only public images</param>
        /// <returns>The total count</returns>
        Task<int> GetCategoryCountAsync(string category, bool activeOnly = true, bool publicOnly = true);
        
        /// <summary>
        /// Get statistics for all categories
        /// </summary>
        /// <param name="activeOnly">Whether to include only active images</param>
        /// <param name="publicOnly">Whether to include only public images</param>
        /// <returns>Dictionary of category names to counts</returns>
        Task<Dictionary<string, int>> GetCategoryStatsAsync(bool activeOnly = true, bool publicOnly = true);
        
        /// <summary>
        /// Get recently accessed images
        /// </summary>
        /// <param name="count">The number of images to retrieve</param>
        /// <param name="category">Optional category filter</param>
        /// <returns>List of recently accessed images</returns>
        Task<List<CatGalleryImage>> GetRecentlyAccessedAsync(int count = 10, string? category = null);
        
        /// <summary>
        /// Get popular images by access count
        /// </summary>
        /// <param name="count">The number of images to retrieve</param>
        /// <param name="category">Optional category filter</param>
        /// <param name="timeRange">Optional time range for popularity calculation</param>
        /// <returns>List of popular images</returns>
        Task<List<CatGalleryImage>> GetPopularImagesAsync(int count = 10, string? category = null, TimeSpan? timeRange = null);
        
        /// <summary>
        /// Get recently uploaded images
        /// </summary>
        /// <param name="count">The number of images to retrieve</param>
        /// <param name="category">Optional category filter</param>
        /// <returns>List of recently uploaded images</returns>
        Task<List<CatGalleryImage>> GetRecentlyUploadedAsync(int count = 10, string? category = null);
        
        /// <summary>
        /// Get featured images
        /// </summary>
        /// <param name="count">The number of images to retrieve</param>
        /// <param name="category">Optional category filter</param>
        /// <returns>List of featured images</returns>
        Task<List<CatGalleryImage>> GetFeaturedImagesAsync(int count = 10, string? category = null);
        
        /// <summary>
        /// Get breed statistics
        /// </summary>
        /// <returns>Dictionary of breed names to counts</returns>
        Task<Dictionary<string, int>> GetBreedStatsAsync();
        
        /// <summary>
        /// Get bloodline statistics
        /// </summary>
        /// <returns>Dictionary of bloodline names to counts</returns>
        Task<Dictionary<string, int>> GetBloodlineStatsAsync();
        
        /// <summary>
        /// Get storage provider statistics
        /// </summary>
        /// <returns>Dictionary of storage provider names to counts</returns>
        Task<Dictionary<string, int>> GetStorageProviderStatsAsync();
        
        // Performance optimization
        
        /// <summary>
        /// Increment the access count for an image
        /// </summary>
        /// <param name="id">The image ID</param>
        /// <returns>True if successful, false if image not found</returns>
        Task<bool> IncrementAccessCountAsync(long id);
        
        /// <summary>
        /// Update the last accessed timestamp for an image
        /// </summary>
        /// <param name="id">The image ID</param>
        /// <returns>True if successful, false if image not found</returns>
        Task<bool> UpdateLastAccessedAsync(long id);
        
        /// <summary>
        /// Get images that should be cached for performance
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <param name="count">The number of images to retrieve</param>
        /// <returns>List of images for cache warming</returns>
        Task<List<CatGalleryImage>> GetImagesForCacheWarmupAsync(string? category = null, int count = 100);
        
        /// <summary>
        /// Update sort order for multiple images
        /// </summary>
        /// <param name="imageIds">The image IDs with their new sort orders</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateSortOrdersAsync(Dictionary<long, int> imageIds);
        
        /// <summary>
        /// Get duplicate images based on file hash or metadata
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <returns>List of groups of duplicate images</returns>
        Task<List<List<CatGalleryImage>>> GetDuplicateImagesAsync(string? category = null);
        
        /// <summary>
        /// Get images with missing metadata
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of images with missing metadata</returns>
        Task<PagedResult<CatGalleryImage>> GetImagesWithMissingMetadataAsync(
            string? category = null,
            int page = 1,
            int pageSize = 20);
        
        /// <summary>
        /// Get images by storage provider
        /// </summary>
        /// <param name="storageProvider">The storage provider to filter by</param>
        /// <param name="page">The page number (1-based)</param>
        /// <param name="pageSize">The number of items per page</param>
        /// <returns>Paged result of images</returns>
        Task<PagedResult<CatGalleryImage>> GetByStorageProviderAsync(
            string storageProvider,
            int page = 1,
            int pageSize = 20);
        
        /// <summary>
        /// Get storage keys for migration purposes
        /// </summary>
        /// <param name="sourceProvider">The source storage provider</param>
        /// <param name="batchSize">The batch size for processing</param>
        /// <param name="skip">The number of items to skip</param>
        /// <returns>List of storage keys</returns>
        Task<List<string>> GetStorageKeysForMigrationAsync(
            string sourceProvider,
            int batchSize = 100,
            int skip = 0);

        // Thumbnail-specific methods

        /// <summary>
        /// Get images that don't have thumbnails
        /// </summary>
        /// <param name="category">Optional category filter</param>
        /// <param name="limit">Maximum number of images to return</param>
        /// <returns>List of images without thumbnails</returns>
        Task<List<CatGalleryImage>> GetImagesWithoutThumbnailsAsync(string? category = null, int limit = 100);

        /// <summary>
        /// Get total count of all images
        /// </summary>
        /// <returns>Total image count</returns>
        Task<int> GetTotalCountAsync();

        /// <summary>
        /// Get total count of images with filtering
        /// </summary>
        /// <param name="activeOnly">Whether to count only active images</param>
        /// <returns>Filtered image count</returns>
        Task<int> GetTotalCountAsync(bool activeOnly);

        /// <summary>
        /// Get count of images that have thumbnails
        /// </summary>
        /// <returns>Count of images with thumbnails</returns>
        Task<int> GetImagesWithThumbnailsCountAsync();

        /// <summary>
        /// Get recently accessed images for warmup purposes
        /// </summary>
        /// <param name="since">Get images accessed since this date</param>
        /// <param name="limit">Maximum number of images to return</param>
        /// <returns>List of recently accessed images</returns>
        Task<List<CatGalleryImage>> GetRecentlyAccessedImagesAsync(DateTime since, int limit = 100);

        /// <summary>
        /// Get all images (for migration purposes)
        /// </summary>
        /// <returns>All images in the database</returns>
        Task<List<CatGalleryImage>> GetAllAsync();

        /// <summary>
        /// Get all images with filtering options
        /// </summary>
        /// <param name="activeOnly">Whether to include only active images</param>
        /// <param name="publicOnly">Whether to include only public images</param>
        /// <returns>Filtered list of images</returns>
        Task<List<CatGalleryImage>> GetAllAsync(bool activeOnly, bool publicOnly = false);

        /// <summary>
        /// Get image by filename
        /// </summary>
        /// <param name="filename">The filename to search for</param>
        /// <returns>The matching image or null</returns>
        Task<CatGalleryImage?> GetByFilenameAsync(string filename);

        /// <summary>
        /// Get images by S3 key
        /// </summary>
        /// <param name="s3Key">The S3 key to search for</param>
        /// <returns>Image with matching S3 key</returns>
        Task<CatGalleryImage?> GetByS3KeyAsync(string s3Key);

        /// <summary>
        /// Get count of images by cat ID
        /// </summary>
        /// <param name="catId">The cat ID</param>
        /// <returns>Count of images for the cat</returns>
        Task<int> GetCountByCatIdAsync(string catId);

        /// <summary>
        /// Get images needing B2 sync
        /// </summary>
        /// <param name="batchSize">Number of images to return</param>
        /// <returns>Images that need B2 sync</returns>
        Task<List<CatGalleryImage>> GetImagesNeedingB2SyncAsync(int batchSize = 100);

        /// <summary>
        /// Get health statistics for the gallery
        /// </summary>
        /// <returns>Health statistics</returns>
        Task<Dictionary<string, object>> GetHealthStatsAsync();

        /// <summary>
        /// Soft delete an image
        /// </summary>
        /// <param name="imageId">The image ID to soft delete</param>
        /// <returns>True if successful</returns>
        Task<bool> SoftDeleteAsync(long imageId);

        /// <summary>
        /// Get images by multiple IDs
        /// </summary>
        /// <param name="imageIds">List of image IDs</param>
        /// <returns>Images matching the IDs</returns>
        Task<List<CatGalleryImage>> GetByIdsAsync(IEnumerable<long> imageIds);

        /// <summary>
        /// Get category images with lightweight data
        /// </summary>
        /// <param name="category">Category name</param>
        /// <param name="page">Page number</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>Lightweight image data</returns>
        Task<PagedResult<CatGalleryImage>> GetCategoryImagesLightweightAsync(string category, int page = 1, int pageSize = 20);

        /// <summary>
        /// Advanced search with multiple criteria
        /// </summary>
        /// <param name="searchCriteria">Search criteria object</param>
        /// <returns>Search results</returns>
        Task<PagedResult<CatGalleryImage>> AdvancedSearchAsync(object searchCriteria);

        /// <summary>
        /// Get random images
        /// </summary>
        /// <param name="count">Number of random images</param>
        /// <param name="category">Optional category filter</param>
        /// <returns>Random images</returns>
        Task<List<CatGalleryImage>> GetRandomImagesAsync(int count, string? category = null);

        /// <summary>
        /// Get images by cat ID with different signature
        /// </summary>
        /// <param name="catId">Cat ID</param>
        /// <param name="includeInactive">Include inactive images</param>
        /// <returns>Images for the cat</returns>
        Task<List<CatGalleryImage>> GetImagesByCatIdAsync(string catId, bool includeInactive = false);
    }
}
