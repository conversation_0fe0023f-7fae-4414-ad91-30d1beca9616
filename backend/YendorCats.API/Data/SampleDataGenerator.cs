using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using YendorCats.API.Models;

namespace YendorCats.API.Data
{
    /// <summary>
    /// Generates comprehensive sample data for testing API endpoints with dual storage providers
    /// Supports both S3 and Backblaze B2 storage configurations
    /// </summary>
    public class SampleDataGenerator
    {
        private readonly AppDbContext _context;
        private readonly ILogger<SampleDataGenerator> _logger;
        private readonly Random _random;

        public SampleDataGenerator(AppDbContext context, ILogger<SampleDataGenerator> logger)
        {
            _context = context;
            _logger = logger;
            _random = new Random(42); // Fixed seed for consistent test data
        }

        /// <summary>
        /// Generate complete sample dataset with profiles, images, and sync logs
        /// </summary>
        /// <param name="profileCount">Number of cat profiles to generate</param>
        /// <param name="imagesPerProfile">Average number of images per profile</param>
        /// <param name="enableB2Sync">Whether to generate B2 sync data</param>
        /// <returns>Generation statistics</returns>
        public async Task<SampleDataStats> GenerateCompleteDatasetAsync(
            int profileCount = 50, 
            int imagesPerProfile = 3, 
            bool enableB2Sync = true)
        {
            _logger.LogInformation("Starting sample data generation: {ProfileCount} profiles, {ImagesPerProfile} images per profile, B2 sync: {EnableB2Sync}", 
                profileCount, imagesPerProfile, enableB2Sync);

            var stats = new SampleDataStats
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Clear existing data
                await ClearExistingDataAsync();

                // Generate cat profiles
                var profiles = await GenerateCatProfilesAsync(profileCount);
                stats.ProfilesGenerated = profiles.Count;

                // Generate gallery images
                var images = await GenerateGalleryImagesAsync(profiles, imagesPerProfile);
                stats.ImagesGenerated = images.Count;

                // Generate B2 sync logs if enabled
                if (enableB2Sync)
                {
                    var syncLogs = await GenerateB2SyncLogsAsync(images);
                    stats.SyncLogsGenerated = syncLogs.Count;
                }

                stats.EndTime = DateTime.UtcNow;
                stats.Success = true;

                _logger.LogInformation("Sample data generation completed successfully in {Duration}ms", 
                    (stats.EndTime - stats.StartTime).TotalMilliseconds);

                return stats;
            }
            catch (Exception ex)
            {
                stats.EndTime = DateTime.UtcNow;
                stats.Success = false;
                stats.ErrorMessage = ex.Message;

                _logger.LogError(ex, "Sample data generation failed");
                throw;
            }
        }

        /// <summary>
        /// Generate realistic cat profiles for Maine Coon breeding program
        /// </summary>
        private async Task<List<CatProfile>> GenerateCatProfilesAsync(int count)
        {
            var profiles = new List<CatProfile>();
            var maleNames = new[] { "Thunder", "Storm", "Shadow", "Rusty", "Tiger", "Leo", "Max", "Charlie", "Oliver", "Milo", "Simba", "Felix", "Oreo", "Smokey", "Gizmo", "Garfield", "Whiskers", "Midnight", "Bandit", "Copper", "Zeus", "Apollo", "Diesel", "Hunter", "Ranger", "Scout", "Bear", "Duke", "King", "Prince" };
            var femaleNames = new[] { "Luna", "Bella", "Chloe", "Sophie", "Lily", "Coco", "Nala", "Zoe", "Mia", "Daisy", "Rosie", "Gracie", "Molly", "Lola", "Princess", "Angel", "Misty", "Ginger", "Penny", "Ruby", "Stella", "Willow", "Jasmine", "Jade", "Crystal", "Diamond", "Pearl", "Amber", "Hazel", "Ivory" };
            var colors = new[] { "Brown Tabby", "Silver Tabby", "Red Tabby", "Cream Tabby", "Tortoiseshell", "Calico", "Black", "White", "Blue", "Cream", "Red", "Brown", "Silver", "Smoke", "Shaded", "Chinchilla" };
            var eyeColors = new[] { "Green", "Gold", "Amber", "Blue", "Copper", "Hazel", "Odd-eyed" };
            var bloodlines = new[] { "European Champion Line", "American Foundation Line", "Russian Import Line", "Scandinavian Elite Line", "Canadian Premium Line", "Australian Select Line", "New Zealand Heritage Line", "UK Championship Line", "German Excellence Line", "French Noble Line" };

            for (int i = 1; i <= count; i++)
            {
                var gender = _random.Next(2) == 0 ? "M" : "F";
                var name = gender == "M" ? maleNames[_random.Next(maleNames.Length)] : femaleNames[_random.Next(femaleNames.Length)];
                var birthDate = DateTime.UtcNow.AddYears(-_random.Next(1, 12)).AddDays(-_random.Next(1, 365));
                var age = DateTime.UtcNow.Year - birthDate.Year;
                var isBreeding = age >= 1 && age <= 8 && _random.Next(100) < 60; // 60% breeding rate
                var isRetired = age > 8 || _random.Next(100) < 10; // 10% retired rate
                var isStudService = gender == "M" && isBreeding && _random.Next(100) < 30; // 30% of breeding males offer stud service

                var profile = new CatProfile
                {
                    Name = name,
                    RegisteredName = $"CH YendorCats {name} {_random.Next(1000, 9999)}",
                    CatId = $"{name.ToLower()}-{i:D3}",
                    Breed = "Maine Coon",
                    Gender = gender,
                    DateOfBirth = birthDate,
                    Color = colors[_random.Next(colors.Length)],
                    EyeColor = eyeColors[_random.Next(eyeColors.Length)],
                    Weight = gender == "M" ?
                        (decimal)(_random.NextDouble() * 6 + 12) : // Males: 12-18 lbs
                        (decimal)(_random.NextDouble() * 4 + 8),   // Females: 8-12 lbs
                    Height = (decimal)(_random.NextDouble() * 5 + 25), // 25-30 inches
                    Status = isRetired ? "Retired" : "Active",
                    BreedingStatus = isRetired ? "Retired" : isBreeding ? "Breeding" : "Pet",
                    BloodlineType = isBreeding ? 
                        (_random.Next(100) < 20 ? "Champion" : "Foundation") : "Pet",
                    Bloodline = bloodlines[_random.Next(bloodlines.Length)],
                    Pedigree = GeneratePedigreeData(name, gender),
                    MicrochipNumber = GenerateMicrochipNumber(),
                    RegistrationNumber = $"MC{_random.Next(100000, 999999)}",
                    Awards = GenerateAwards(isBreeding, age),
                    HealthRecords = GenerateHealthRecords(age),
                    Description = GenerateDescription(name, gender, age, isBreeding),
                    IsActive = !isRetired,
                    IsPublic = _random.Next(100) < 85, // 85% public
                    IsBreeding = isBreeding,
                    IsStudService = isStudService,
                    IsRetired = isRetired,
                    ViewCount = _random.Next(10, 1000),
                    LikeCount = _random.Next(5, 500),
                    OffspringCount = isBreeding ? _random.Next(0, age * 3) : 0,
                    CreatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 1000)),
                    UpdatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 100))
                };

                profiles.Add(profile);
            }

            _context.CatProfiles.AddRange(profiles);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Generated {Count} cat profiles", profiles.Count);
            return profiles;
        }

        /// <summary>
        /// Generate realistic gallery images for both S3 and B2 storage
        /// </summary>
        private async Task<List<CatGalleryImage>> GenerateGalleryImagesAsync(List<CatProfile> profiles, int imagesPerProfile)
        {
            var images = new List<CatGalleryImage>();
            var categories = new[] { "gallery", "studs", "queens", "kittens", "champions", "retired" };
            var formats = new[] { "jpg", "jpeg", "png", "webp" };
            var storageProviders = new[] { "S3", "B2" };

            foreach (var profile in profiles)
            {
                var imageCount = _random.Next(1, imagesPerProfile + 2);
                
                for (int i = 1; i <= imageCount; i++)
                {
                    var storageProvider = storageProviders[_random.Next(storageProviders.Length)];
                    var format = formats[_random.Next(formats.Length)];
                    var filename = $"{profile.CatId}-{i:D3}.{format}";
                    var storageKey = $"cats/{profile.CatId}/{filename}";
                    var category = DetermineCategory(profile);
                    var dateTaken = profile.DateOfBirth?.AddDays(_random.Next(30, 365 * 5)) ?? DateTime.UtcNow.AddDays(-_random.Next(1, 365));

                    var image = new CatGalleryImage
                    {
                        Filename = filename,
                        StorageKey = storageKey,
                        OriginalFileName = filename,
                        FileSize = _random.Next(500000, 5000000), // 500KB - 5MB
                        ContentType = GetContentType(format),
                        MimeType = GetContentType(format),
                        Format = format.ToUpper(),
                        Width = _random.Next(1200, 4000),
                        Height = _random.Next(900, 3000),
                        CatName = profile.Name,
                        CatId = profile.CatId,
                        Category = category,
                        Title = $"{profile.Name} - {GetImageTitle(category, i)}",
                        Description = GenerateImageDescription(profile, category, i),
                        Alt = $"Photo of {profile.Name}, a {profile.Color} Maine Coon cat",
                        Tags = GenerateImageTags(profile, category),
                        ExifData = GenerateExifData(),
                        AgeAtPhoto = CalculateAgeAtPhoto(profile.DateOfBirth, dateTaken),
                        Breed = profile.Breed,
                        Bloodline = profile.Bloodline,
                        Gender = profile.Gender,
                        DateTaken = dateTaken,
                        DateUploaded = dateTaken.AddDays(_random.Next(1, 30)),
                        DateModified = DateTime.UtcNow.AddDays(-_random.Next(1, 30)),
                        CreatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 200)),
                        UpdatedAt = DateTime.UtcNow.AddDays(-_random.Next(1, 50)),
                        IsActive = _random.Next(100) < 90, // 90% active
                        IsPublic = profile.IsPublic && _random.Next(100) < 85, // 85% of profile's public images are public
                        IsFeatured = _random.Next(100) < 15, // 15% featured
                        DisplayOrder = i,
                        SortOrder = _random.Next(1, 100),
                        ViewCount = _random.Next(0, 2000),
                        LikeCount = _random.Next(0, 500),
                        DownloadCount = _random.Next(0, 100),
                        StorageProvider = storageProvider,
                        StorageBucketName = storageProvider == "S3" ? "yendorcats-gallery" : "yendorcats-b2-gallery",
                        CreatedBy = "SampleDataGenerator",
                        ModifiedBy = "SampleDataGenerator"
                    };

                    // Set storage-specific fields
                    if (storageProvider == "S3")
                    {
                        image.S3Key = storageKey;
                        image.S3Bucket = "yendorcats-gallery";
                        image.S3Url = $"https://yendorcats-gallery.s3.amazonaws.com/{storageKey}";
                    }
                    else
                    {
                        image.B2Key = storageKey;
                        image.B2Bucket = "yendorcats-b2-gallery";
                        image.B2Url = $"https://f002.backblazeb2.com/file/yendorcats-b2-gallery/{storageKey}";
                        image.B2FileId = $"4_{Guid.NewGuid():N}";
                    }

                    images.Add(image);
                }
            }

            _context.CatGalleryImages.AddRange(images);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Generated {Count} gallery images", images.Count);
            return images;
        }

        /// <summary>
        /// Generate B2 sync logs for migration tracking
        /// </summary>
        private async Task<List<B2SyncLog>> GenerateB2SyncLogsAsync(List<CatGalleryImage> images)
        {
            var syncLogs = new List<B2SyncLog>();
            var operations = new[] { "Upload", "Download", "Delete", "Migrate" };
            var statuses = new[] { "Completed", "Failed", "Cancelled" };
            var statusWeights = new[] { 85, 12, 3 }; // 85% success, 12% failure, 3% cancelled

            // Generate sync logs for S3 images that were migrated to B2
            var s3Images = images.Where(i => i.StorageProvider == "S3").ToList();
            
            foreach (var image in s3Images.Take(s3Images.Count / 2)) // Migrate half of S3 images
            {
                var operation = operations[_random.Next(operations.Length)];
                var status = GetWeightedRandomStatus(statuses, statusWeights);
                var startTime = DateTime.UtcNow.AddDays(-_random.Next(1, 90));
                var endTime = status == "Completed" ? startTime.AddMinutes(_random.Next(1, 30)) : 
                             status == "Failed" ? startTime.AddMinutes(_random.Next(1, 60)) : 
                             startTime.AddMinutes(_random.Next(1, 10));

                var syncLog = new B2SyncLog
                {
                    Operation = operation,
                    SourceStorageProvider = "S3",
                    SourceKey = image.S3Key ?? image.StorageKey,
                    SourceBucket = image.S3Bucket ?? "yendorcats-gallery",
                    DestinationStorageProvider = "B2",
                    DestinationKey = image.StorageKey,
                    DestinationBucket = "yendorcats-b2-gallery",
                    DestinationFileId = status == "Completed" ? $"4_{Guid.NewGuid():N}" : null,
                    FileSize = image.FileSize,
                    ContentType = image.ContentType,
                    Status = status,
                    StartTime = startTime,
                    EndTime = endTime,
                    AttemptCount = status == "Failed" ? _random.Next(1, 4) : 1,
                    MaxRetries = 3,
                    ErrorMessage = status == "Failed" ? GenerateErrorMessage() : null,
                    CreatedAt = startTime,
                    UpdatedAt = endTime
                };

                syncLogs.Add(syncLog);
            }

            _context.B2SyncLogs.AddRange(syncLogs);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Generated {Count} B2 sync logs", syncLogs.Count);
            return syncLogs;
        }

        /// <summary>
        /// Clear existing sample data
        /// </summary>
        private async Task ClearExistingDataAsync()
        {
            _logger.LogInformation("Clearing existing sample data");

            await _context.Database.ExecuteSqlRawAsync("DELETE FROM B2SyncLogs");
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM CatGalleryImages");
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM CatProfiles");
            
            // Reset identity counters
            await _context.Database.ExecuteSqlRawAsync("DELETE FROM sqlite_sequence WHERE name IN ('CatProfiles', 'CatGalleryImages', 'B2SyncLogs')");
        }

        // Helper methods for generating realistic data
        private string DetermineCategory(CatProfile profile)
        {
            if (profile.IsRetired) return "retired";
            if (profile.Gender == "M" && profile.IsBreeding) return "studs";
            if (profile.Gender == "F" && profile.IsBreeding) return "queens";
            if (profile.Age < 1) return "kittens";
            if (profile.BreedingStatus == "Champion") return "champions";
            return "gallery";
        }

        private string GetContentType(string format)
        {
            return format.ToLower() switch
            {
                "jpg" or "jpeg" => "image/jpeg",
                "png" => "image/png",
                "webp" => "image/webp",
                _ => "image/jpeg"
            };
        }

        private string GetImageTitle(string category, int imageNumber)
        {
            return category switch
            {
                "studs" => $"Stud Portrait #{imageNumber}",
                "queens" => $"Queen Portrait #{imageNumber}",
                "kittens" => $"Kitten Photo #{imageNumber}",
                "champions" => $"Champion Show Photo #{imageNumber}",
                "retired" => $"Retirement Portrait #{imageNumber}",
                _ => $"Gallery Photo #{imageNumber}"
            };
        }

        private string GenerateImageDescription(CatProfile profile, string category, int imageNumber)
        {
            var descriptions = new[]
            {
                $"Beautiful {profile.Color} Maine Coon showcasing the breed's magnificent coat.",
                $"Stunning portrait of {profile.Name} displaying excellent breed type.",
                $"Professional photo capturing {profile.Name}'s natural beauty and personality.",
                $"High-quality image of {profile.Name} from our {profile.Bloodline} bloodline.",
                $"Excellent example of Maine Coon breed standards in this portrait of {profile.Name}."
            };

            return descriptions[_random.Next(descriptions.Length)];
        }

        private string GenerateImageTags(CatProfile profile, string category)
        {
            var tags = new List<string>
            {
                profile.Name.ToLower(),
                "maine-coon",
                profile.Gender == "M" ? "male" : "female",
                profile.Color.ToLower().Replace(" ", "-"),
                category
            };

            if (profile.IsBreeding) tags.Add("breeding");
            if (profile.IsRetired) tags.Add("retired");
            if (profile.BreedingStatus == "Champion") tags.Add("champion");

            return string.Join(",", tags);
        }

        private string GenerateExifData()
        {
            var exif = new
            {
                Camera = "Canon EOS R5",
                Lens = "Canon RF 85mm f/1.2L",
                FocalLength = "85mm",
                Aperture = "f/2.8",
                ShutterSpeed = "1/200s",
                ISO = _random.Next(100, 1600),
                FlashUsed = _random.Next(2) == 0,
                WhiteBalance = "Auto",
                ColorSpace = "sRGB",
                ImageOrientation = "Horizontal",
                GPS = false
            };

            return System.Text.Json.JsonSerializer.Serialize(exif);
        }

        private string CalculateAgeAtPhoto(DateTime? birthDate, DateTime photoDate)
        {
            if (birthDate == null) return "Unknown";
            
            var age = photoDate - birthDate.Value;
            var totalDays = (int)age.TotalDays;
            
            if (totalDays < 30) return $"{totalDays} days";
            if (totalDays < 365) return $"{totalDays / 30} months";
            
            var years = totalDays / 365;
            var months = (totalDays % 365) / 30;
            
            return months > 0 ? $"{years} years {months} months" : $"{years} years";
        }

        private string GeneratePedigreeData(string name, string gender)
        {
            return $"Sire: GCH YendorCats {(gender == "M" ? "Thunder" : "Storm")} of Elite\n" +
                   $"Dam: CH YendorCats {(gender == "M" ? "Luna" : "Bella")} Royal\n" +
                   $"Bloodline: European Champion Line\n" +
                   $"Registration: Complete pedigree on file";
        }

        private string GenerateMicrochipNumber()
        {
            return $"982{_random.Next(100000000, 999999999):D9}{_random.Next(10000, 99999):D5}";
        }

        private string GenerateAwards(bool isBreeding, int age)
        {
            if (!isBreeding || age < 2) return "No awards yet";
            
            var awards = new List<string>();
            if (_random.Next(100) < 30) awards.Add("Best in Show 2023");
            if (_random.Next(100) < 40) awards.Add("Best of Breed");
            if (_random.Next(100) < 25) awards.Add("Regional Winner");
            if (_random.Next(100) < 20) awards.Add("Grand Champion");
            
            return awards.Any() ? string.Join(", ", awards) : "No major awards";
        }

        private string GenerateHealthRecords(int age)
        {
            var records = new List<string>
            {
                "Vaccinations: Up to date",
                "HCM Screening: Normal",
                "PKD Screening: Normal"
            };
            
            if (age > 3) records.Add("Annual Health Check: Pass");
            if (age > 5) records.Add("Cardiac Ultrasound: Normal");
            
            return string.Join(", ", records);
        }

        private string GenerateDescription(string name, string gender, int age, bool isBreeding)
        {
            var descriptions = new[]
            {
                $"{name} is a stunning {(gender == "M" ? "male" : "female")} Maine Coon with exceptional breed type and temperament.",
                $"Beautiful {(gender == "M" ? "boy" : "girl")} with excellent conformation and a loving personality.",
                $"Magnificent Maine Coon with outstanding coat quality and gentle nature.",
                $"Exceptional {(gender == "M" ? "male" : "female")} from champion bloodlines with proven genetics."
            };

            var baseDescription = descriptions[_random.Next(descriptions.Length)];
            
            if (isBreeding)
            {
                baseDescription += gender == "M" ? 
                    " Available for approved breeding and stud services." :
                    " Proven producer with excellent maternal instincts.";
            }
            
            return baseDescription;
        }

        private string GetWeightedRandomStatus(string[] statuses, int[] weights)
        {
            var totalWeight = weights.Sum();
            var randomValue = _random.Next(totalWeight);
            var currentWeight = 0;
            
            for (int i = 0; i < statuses.Length; i++)
            {
                currentWeight += weights[i];
                if (randomValue < currentWeight)
                    return statuses[i];
            }
            
            return statuses[0]; // Fallback
        }

        private string GenerateErrorMessage()
        {
            var errors = new[]
            {
                "Connection timeout to B2 service",
                "Insufficient permissions for B2 bucket",
                "File size exceeds maximum limit",
                "Invalid content type for upload",
                "B2 service temporarily unavailable",
                "Authentication failed for B2 service",
                "Network error during upload",
                "File corruption detected during transfer"
            };
            
            return errors[_random.Next(errors.Length)];
        }
    }

    /// <summary>
    /// Statistics for sample data generation
    /// </summary>
    public class SampleDataStats
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int ProfilesGenerated { get; set; }
        public int ImagesGenerated { get; set; }
        public int SyncLogsGenerated { get; set; }
        
        public TimeSpan Duration => EndTime - StartTime;
        public int TotalRecords => ProfilesGenerated + ImagesGenerated + SyncLogsGenerated;
    }
}