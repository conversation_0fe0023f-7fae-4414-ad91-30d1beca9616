using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace YendorCats.API.Data.Migrations
{
    /// <summary>
    /// Migration to add hybrid storage support with dual S3/B2 storage providers
    /// Includes new entities for cat profiles and B2 sync logging
    /// </summary>
    public partial class AddHybridStorageSupport : Migration
    {
        /// <summary>
        /// Apply the migration - add hybrid storage support
        /// </summary>
        /// <param name="migrationBuilder">The migration builder</param>
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create CatProfiles table for breeding program management
            migrationBuilder.CreateTable(
                name: "CatProfiles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CatId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CatName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    RegisteredName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Breed = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Bloodline = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Gender = table.Column<string>(type: "TEXT", maxLength: 1, nullable: false),
                    Color = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Pattern = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    EyeColor = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BirthDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    SireId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    SireName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DamId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    DamName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    RegistrationNumber = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    RegistrationBody = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Microchip = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Tattoo = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BreedingStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    AvailabilityStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Titles = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Awards = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    HealthStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    HealthNotes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    VetInfo = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    LastHealthCheck = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CurrentLocation = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    OwnerInfo = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ProfileImage = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    Personality = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    IsPublic = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    IsFeatured = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CatProfiles", x => x.Id);
                });

            // Create B2SyncLogs table for audit trails
            migrationBuilder.CreateTable(
                name: "B2SyncLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    Operation = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    S3Key = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    S3Bucket = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    B2Key = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    B2Bucket = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    B2FileId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: true),
                    ContentType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Checksum = table.Column<string>(type: "TEXT", maxLength: 64, nullable: true),
                    ProcessingTimeMs = table.Column<long>(type: "INTEGER", nullable: true),
                    RetryCount = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 0),
                    ErrorMessage = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    ErrorDetails = table.Column<string>(type: "TEXT", maxLength: 4000, nullable: true),
                    BatchId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    UserId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    SessionId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    NextRetryAt = table.Column<DateTime>(type: "TEXT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_B2SyncLogs", x => x.Id);
                });

            // Add new columns to CatGalleryImages for dual storage support
            migrationBuilder.AddColumn<string>(
                name: "StorageProvider",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "S3");

            migrationBuilder.AddColumn<string>(
                name: "S3Key",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "S3Bucket",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "S3Url",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2Key",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2Bucket",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2Url",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CatId",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Filename",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Alt",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "FileSize",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "MimeType",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Width",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Height",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Format",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ExifData",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsFeatured",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsPublic",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ViewCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "LikeCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DownloadCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "LastViewedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: true);

            // Create performance indexes for CatProfiles
            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CatId",
                table: "CatProfiles",
                column: "CatId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CatName",
                table: "CatProfiles",
                column: "CatName");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Breed",
                table: "CatProfiles",
                column: "Breed");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Bloodline",
                table: "CatProfiles",
                column: "Bloodline");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Gender",
                table: "CatProfiles",
                column: "Gender");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_BreedingStatus",
                table: "CatProfiles",
                column: "BreedingStatus");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_AvailabilityStatus",
                table: "CatProfiles",
                column: "AvailabilityStatus");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_SireId",
                table: "CatProfiles",
                column: "SireId");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_DamId",
                table: "CatProfiles",
                column: "DamId");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_BirthDate",
                table: "CatProfiles",
                column: "BirthDate");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsActive",
                table: "CatProfiles",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsPublic",
                table: "CatProfiles",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsFeatured",
                table: "CatProfiles",
                column: "IsFeatured");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_DisplayOrder",
                table: "CatProfiles",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CreatedAt",
                table: "CatProfiles",
                column: "CreatedAt");

            // Create composite indexes for CatProfiles
            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Gender_BreedingStatus_IsActive",
                table: "CatProfiles",
                columns: new[] { "Gender", "BreedingStatus", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Breed_IsActive_IsPublic",
                table: "CatProfiles",
                columns: new[] { "Breed", "IsActive", "IsPublic" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsFeatured_DisplayOrder",
                table: "CatProfiles",
                columns: new[] { "IsFeatured", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_AvailabilityStatus_Gender_IsActive",
                table: "CatProfiles",
                columns: new[] { "AvailabilityStatus", "Gender", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_SireId_DamId",
                table: "CatProfiles",
                columns: new[] { "SireId", "DamId" });

            // Create performance indexes for B2SyncLogs
            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_FileName",
                table: "B2SyncLogs",
                column: "FileName");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Operation",
                table: "B2SyncLogs",
                column: "Operation");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Status",
                table: "B2SyncLogs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_S3Key",
                table: "B2SyncLogs",
                column: "S3Key");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_B2Key",
                table: "B2SyncLogs",
                column: "B2Key");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_B2FileId",
                table: "B2SyncLogs",
                column: "B2FileId");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_BatchId",
                table: "B2SyncLogs",
                column: "BatchId");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_CreatedAt",
                table: "B2SyncLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_CompletedAt",
                table: "B2SyncLogs",
                column: "CompletedAt");

            // Create composite indexes for B2SyncLogs
            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Operation_Status",
                table: "B2SyncLogs",
                columns: new[] { "Operation", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Status_CreatedAt",
                table: "B2SyncLogs",
                columns: new[] { "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_BatchId_Status",
                table: "B2SyncLogs",
                columns: new[] { "BatchId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Operation_Status_CreatedAt",
                table: "B2SyncLogs",
                columns: new[] { "Operation", "Status", "CreatedAt" });

            // Create performance indexes for CatGalleryImages dual storage
            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_Filename",
                table: "CatGalleryImages",
                column: "Filename",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CatId",
                table: "CatGalleryImages",
                column: "CatId");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_StorageProvider",
                table: "CatGalleryImages",
                column: "StorageProvider");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_S3Key",
                table: "CatGalleryImages",
                column: "S3Key");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_B2Key",
                table: "CatGalleryImages",
                column: "B2Key");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsActive",
                table: "CatGalleryImages",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsPublic",
                table: "CatGalleryImages",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsFeatured",
                table: "CatGalleryImages",
                column: "IsFeatured");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_DisplayOrder",
                table: "CatGalleryImages",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CreatedAt",
                table: "CatGalleryImages",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_ViewCount",
                table: "CatGalleryImages",
                column: "ViewCount");

            // Create composite indexes for CatGalleryImages dual storage
            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CatId_IsActive_IsPublic",
                table: "CatGalleryImages",
                columns: new[] { "CatId", "IsActive", "IsPublic" });

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_StorageProvider_IsActive",
                table: "CatGalleryImages",
                columns: new[] { "StorageProvider", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsFeatured_DisplayOrder",
                table: "CatGalleryImages",
                columns: new[] { "IsFeatured", "DisplayOrder" });

            // Data migration: populate new fields for existing records
            migrationBuilder.Sql(@"
                UPDATE CatGalleryImages 
                SET 
                    Filename = CASE 
                        WHEN ImageUrl IS NOT NULL THEN 
                            CASE 
                                WHEN ImageUrl LIKE '%.jpg' THEN substr(ImageUrl, instr(ImageUrl, '/') + 1)
                                WHEN ImageUrl LIKE '%.jpeg' THEN substr(ImageUrl, instr(ImageUrl, '/') + 1)
                                WHEN ImageUrl LIKE '%.png' THEN substr(ImageUrl, instr(ImageUrl, '/') + 1)
                                ELSE substr(ImageUrl, instr(ImageUrl, '/') + 1) || '.jpg'
                            END
                        ELSE 'unknown_' || Id || '.jpg'
                    END,
                    CatId = CASE 
                        WHEN CatName IS NOT NULL THEN LOWER(REPLACE(CatName, ' ', '_'))
                        ELSE 'cat_' || Id
                    END,
                    S3Key = ImageUrl,
                    S3Url = ImageUrl,
                    MimeType = CASE 
                        WHEN ImageUrl LIKE '%.jpg' THEN 'image/jpeg'
                        WHEN ImageUrl LIKE '%.jpeg' THEN 'image/jpeg'
                        WHEN ImageUrl LIKE '%.png' THEN 'image/png'
                        ELSE 'image/jpeg'
                    END,
                    Format = CASE 
                        WHEN ImageUrl LIKE '%.png' THEN 'PNG'
                        ELSE 'JPEG'
                    END,
                    CreatedAt = COALESCE(CreatedAt, datetime('now')),
                    UpdatedAt = COALESCE(UpdatedAt, datetime('now'))
                WHERE Filename IS NULL OR Filename = '';
            ");
        }

        /// <summary>
        /// Revert the migration - remove hybrid storage support
        /// </summary>
        /// <param name="migrationBuilder">The migration builder</param>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop new tables
            migrationBuilder.DropTable(name: "CatProfiles");
            migrationBuilder.DropTable(name: "B2SyncLogs");

            // Drop new indexes for CatGalleryImages
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_Filename", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_CatId", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_StorageProvider", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_S3Key", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_B2Key", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_IsActive", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_IsPublic", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_IsFeatured", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_DisplayOrder", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_CreatedAt", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_ViewCount", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_CatId_IsActive_IsPublic", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_StorageProvider_IsActive", table: "CatGalleryImages");
            migrationBuilder.DropIndex(name: "IX_CatGalleryImages_IsFeatured_DisplayOrder", table: "CatGalleryImages");

            // Remove new columns from CatGalleryImages
            migrationBuilder.DropColumn(name: "StorageProvider", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "S3Key", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "S3Bucket", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "S3Url", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "B2Key", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "B2Bucket", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "B2Url", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "CatId", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "Filename", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "Title", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "Alt", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "FileSize", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "MimeType", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "Width", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "Height", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "Format", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "ExifData", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "IsFeatured", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "IsActive", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "IsPublic", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "DisplayOrder", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "ViewCount", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "LikeCount", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "DownloadCount", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "CreatedAt", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "UpdatedAt", table: "CatGalleryImages");
            migrationBuilder.DropColumn(name: "LastViewedAt", table: "CatGalleryImages");
        }
    }
}