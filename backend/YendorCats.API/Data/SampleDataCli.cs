using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace YendorCats.API.Data
{
    /// <summary>
    /// CLI tool for generating sample data for testing and development
    /// Usage: dotnet run --project YendorCats.API -- generate-sample-data [options]
    /// </summary>
    public class SampleDataCli
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SampleDataCli> _logger;

        public SampleDataCli(IServiceProvider serviceProvider, ILogger<SampleDataCli> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// Generate sample data with specified parameters
        /// </summary>
        /// <param name="profileCount">Number of cat profiles to generate</param>
        /// <param name="imagesPerProfile">Average number of images per profile</param>
        /// <param name="enableB2Sync">Whether to generate B2 sync data</param>
        /// <param name="recreateDatabase">Whether to recreate the database</param>
        /// <returns>Generation statistics</returns>
        public async Task<SampleDataStats> GenerateAsync(
            int profileCount = 25,
            int imagesPerProfile = 3,
            bool enableB2Sync = true,
            bool recreateDatabase = false)
        {
            _logger.LogInformation("Starting sample data generation CLI");
            _logger.LogInformation("Parameters: Profiles={ProfileCount}, Images/Profile={ImagesPerProfile}, B2Sync={EnableB2Sync}, RecreateDb={RecreateDatabase}",
                profileCount, imagesPerProfile, enableB2Sync, recreateDatabase);

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var generator = scope.ServiceProvider.GetRequiredService<SampleDataGenerator>();

                // Optionally recreate database
                if (recreateDatabase)
                {
                    _logger.LogInformation("Recreating database...");
                    await context.Database.EnsureDeletedAsync();
                    await context.Database.EnsureCreatedAsync();
                    _logger.LogInformation("Database recreated successfully");
                }
                else
                {
                    // Ensure database exists
                    await context.Database.EnsureCreatedAsync();
                }

                // Generate sample data
                var stats = await generator.GenerateCompleteDatasetAsync(
                    profileCount, imagesPerProfile, enableB2Sync);

                // Log summary
                LogGenerationSummary(stats);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sample data generation failed");
                throw;
            }
        }

        /// <summary>
        /// Generate minimal sample data for quick testing
        /// </summary>
        public async Task<SampleDataStats> GenerateQuickTestDataAsync()
        {
            _logger.LogInformation("Generating quick test data (10 profiles, 2 images each)");
            return await GenerateAsync(
                profileCount: 10,
                imagesPerProfile: 2,
                enableB2Sync: true,
                recreateDatabase: true);
        }

        /// <summary>
        /// Generate comprehensive sample data for full testing
        /// </summary>
        public async Task<SampleDataStats> GenerateFullTestDataAsync()
        {
            _logger.LogInformation("Generating full test data (50 profiles, 4 images each)");
            return await GenerateAsync(
                profileCount: 50,
                imagesPerProfile: 4,
                enableB2Sync: true,
                recreateDatabase: true);
        }

        /// <summary>
        /// Generate performance testing data
        /// </summary>
        public async Task<SampleDataStats> GeneratePerformanceTestDataAsync()
        {
            _logger.LogInformation("Generating performance test data (100 profiles, 5 images each)");
            return await GenerateAsync(
                profileCount: 100,
                imagesPerProfile: 5,
                enableB2Sync: true,
                recreateDatabase: true);
        }

        /// <summary>
        /// Clear all sample data
        /// </summary>
        public async Task ClearSampleDataAsync()
        {
            _logger.LogInformation("Clearing all sample data");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                await context.Database.ExecuteSqlRawAsync("DELETE FROM B2SyncLogs");
                await context.Database.ExecuteSqlRawAsync("DELETE FROM CatGalleryImages");
                await context.Database.ExecuteSqlRawAsync("DELETE FROM CatProfiles");
                await context.Database.ExecuteSqlRawAsync("DELETE FROM sqlite_sequence WHERE name IN ('CatProfiles', 'CatGalleryImages', 'B2SyncLogs')");

                _logger.LogInformation("Sample data cleared successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear sample data");
                throw;
            }
        }

        /// <summary>
        /// Get current database statistics
        /// </summary>
        public async Task<DatabaseStats> GetDatabaseStatsAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var stats = new DatabaseStats
            {
                ProfileCount = await context.CatProfiles.CountAsync(),
                ImageCount = await context.CatGalleryImages.CountAsync(),
                SyncLogCount = await context.B2SyncLogs.CountAsync(),
                ActiveProfiles = await context.CatProfiles.CountAsync(p => p.IsActive),
                PublicProfiles = await context.CatProfiles.CountAsync(p => p.IsPublic),
                BreedingProfiles = await context.CatProfiles.CountAsync(p => p.IsBreeding),
                ActiveImages = await context.CatGalleryImages.CountAsync(i => i.IsActive),
                PublicImages = await context.CatGalleryImages.CountAsync(i => i.IsPublic),
                FeaturedImages = await context.CatGalleryImages.CountAsync(i => i.IsFeatured),
                S3Images = await context.CatGalleryImages.CountAsync(i => i.StorageProvider == "S3"),
                B2Images = await context.CatGalleryImages.CountAsync(i => i.StorageProvider == "B2"),
                CompletedSyncLogs = await context.B2SyncLogs.CountAsync(l => l.Status == "Completed"),
                FailedSyncLogs = await context.B2SyncLogs.CountAsync(l => l.Status == "Failed")
            };

            return stats;
        }

        /// <summary>
        /// Validate sample data integrity
        /// </summary>
        public async Task<ValidationResult> ValidateSampleDataAsync()
        {
            _logger.LogInformation("Validating sample data integrity");

            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                var result = new ValidationResult();

                // Check for orphaned images
                var orphanedImages = await context.CatGalleryImages
                    .Where(i => !context.CatProfiles.Any(p => p.CatId == i.CatId))
                    .CountAsync();

                if (orphanedImages > 0)
                {
                    result.Issues.Add($"Found {orphanedImages} orphaned images without matching profiles");
                }

                // Check for missing storage data
                var s3ImagesWithoutKeys = await context.CatGalleryImages
                    .Where(i => i.StorageProvider == "S3" && (i.S3Key == null || i.S3Bucket == null))
                    .CountAsync();

                if (s3ImagesWithoutKeys > 0)
                {
                    result.Issues.Add($"Found {s3ImagesWithoutKeys} S3 images without proper storage keys");
                }

                var b2ImagesWithoutKeys = await context.CatGalleryImages
                    .Where(i => i.StorageProvider == "B2" && (i.B2Key == null || i.B2Bucket == null))
                    .CountAsync();

                if (b2ImagesWithoutKeys > 0)
                {
                    result.Issues.Add($"Found {b2ImagesWithoutKeys} B2 images without proper storage keys");
                }

                // Check for data consistency
                var profilesWithoutImages = await context.CatProfiles
                    .Where(p => !context.CatGalleryImages.Any(i => i.CatId == p.CatId))
                    .CountAsync();

                if (profilesWithoutImages > 0)
                {
                    result.Warnings.Add($"Found {profilesWithoutImages} profiles without images");
                }

                result.IsValid = result.Issues.Count == 0;
                
                if (result.IsValid)
                {
                    _logger.LogInformation("Sample data validation passed");
                }
                else
                {
                    _logger.LogWarning("Sample data validation found {IssueCount} issues", result.Issues.Count);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Sample data validation failed");
                throw;
            }
        }

        private void LogGenerationSummary(SampleDataStats stats)
        {
            _logger.LogInformation("=== Sample Data Generation Summary ===");
            _logger.LogInformation("Status: {Status}", stats.Success ? "SUCCESS" : "FAILED");
            _logger.LogInformation("Duration: {Duration}ms", stats.Duration.TotalMilliseconds);
            _logger.LogInformation("Profiles Generated: {Count}", stats.ProfilesGenerated);
            _logger.LogInformation("Images Generated: {Count}", stats.ImagesGenerated);
            _logger.LogInformation("Sync Logs Generated: {Count}", stats.SyncLogsGenerated);
            _logger.LogInformation("Total Records: {Count}", stats.TotalRecords);
            
            if (!stats.Success)
            {
                _logger.LogError("Error: {Error}", stats.ErrorMessage);
            }
            
            _logger.LogInformation("=====================================");
        }
    }

    /// <summary>
    /// Database statistics for monitoring
    /// </summary>
    public class DatabaseStats
    {
        public int ProfileCount { get; set; }
        public int ImageCount { get; set; }
        public int SyncLogCount { get; set; }
        public int ActiveProfiles { get; set; }
        public int PublicProfiles { get; set; }
        public int BreedingProfiles { get; set; }
        public int ActiveImages { get; set; }
        public int PublicImages { get; set; }
        public int FeaturedImages { get; set; }
        public int S3Images { get; set; }
        public int B2Images { get; set; }
        public int CompletedSyncLogs { get; set; }
        public int FailedSyncLogs { get; set; }

        public override string ToString()
        {
            return $"Profiles: {ProfileCount} ({ActiveProfiles} active, {PublicProfiles} public, {BreedingProfiles} breeding)\n" +
                   $"Images: {ImageCount} ({ActiveImages} active, {PublicImages} public, {FeaturedImages} featured)\n" +
                   $"Storage: {S3Images} S3, {B2Images} B2\n" +
                   $"Sync Logs: {SyncLogCount} ({CompletedSyncLogs} completed, {FailedSyncLogs} failed)";
        }
    }

    /// <summary>
    /// Validation result for sample data
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();

        public override string ToString()
        {
            var result = $"Validation: {(IsValid ? "PASSED" : "FAILED")}\n";
            
            if (Issues.Any())
            {
                result += "Issues:\n" + string.Join("\n", Issues.Select(i => $"  - {i}")) + "\n";
            }
            
            if (Warnings.Any())
            {
                result += "Warnings:\n" + string.Join("\n", Warnings.Select(w => $"  - {w}")) + "\n";
            }
            
            return result;
        }
    }
}