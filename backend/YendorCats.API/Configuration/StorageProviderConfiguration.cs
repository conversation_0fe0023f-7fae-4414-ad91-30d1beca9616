using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Configuration
{
    /// <summary>
    /// Configuration settings for hybrid storage providers (S3 + B2)
    /// </summary>
    public class StorageProviderConfiguration
    {
        /// <summary>
        /// Primary storage provider (S3, B2, or Hybrid)
        /// </summary>
        [Required]
        public string PrimaryProvider { get; set; } = "S3";

        /// <summary>
        /// Secondary storage provider for dual storage
        /// </summary>
        public string? SecondaryProvider { get; set; }

        /// <summary>
        /// Enable automatic B2 synchronization
        /// </summary>
        public bool EnableB2Sync { get; set; } = true;

        /// <summary>
        /// Enable automatic migration from S3-only to hybrid storage
        /// </summary>
        public bool EnableAutoMigration { get; set; } = false;

        /// <summary>
        /// Batch size for bulk operations
        /// </summary>
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// Maximum retry attempts for failed operations
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts (in seconds)
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 30;

        /// <summary>
        /// Enable performance monitoring
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// Enable detailed logging for storage operations
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = false;

        /// <summary>
        /// S3 storage configuration
        /// </summary>
        public S3StorageConfig S3Config { get; set; } = new();

        /// <summary>
        /// B2 storage configuration
        /// </summary>
        public B2StorageConfig B2Config { get; set; } = new();

        /// <summary>
        /// Cache configuration for performance optimization
        /// </summary>
        public CacheConfig CacheConfig { get; set; } = new();

        /// <summary>
        /// Convenience property for S3 bucket name (maps to S3Config.BucketName)
        /// </summary>
        public string S3BucketName => S3Config.BucketName;

        /// <summary>
        /// Convenience property for B2 bucket name (maps to B2Config.BucketName)
        /// </summary>
        public string B2BucketName => B2Config.BucketName;
    }

    /// <summary>
    /// S3 storage provider configuration
    /// </summary>
    public class S3StorageConfig
    {
        /// <summary>
        /// S3 bucket name
        /// </summary>
        [Required]
        public string BucketName { get; set; } = string.Empty;

        /// <summary>
        /// S3 service URL (for MinIO compatibility)
        /// </summary>
        public string? ServiceUrl { get; set; }

        /// <summary>
        /// AWS region
        /// </summary>
        public string Region { get; set; } = "us-east-1";

        /// <summary>
        /// Enable path-style addressing (required for MinIO)
        /// </summary>
        public bool ForcePathStyle { get; set; } = true;

        /// <summary>
        /// Enable server-side encryption
        /// </summary>
        public bool EnableServerSideEncryption { get; set; } = false;

        /// <summary>
        /// Default storage class
        /// </summary>
        public string StorageClass { get; set; } = "STANDARD";

        /// <summary>
        /// Enable versioning
        /// </summary>
        public bool EnableVersioning { get; set; } = false;
    }

    /// <summary>
    /// B2 storage provider configuration
    /// </summary>
    public class B2StorageConfig
    {
        /// <summary>
        /// B2 bucket name
        /// </summary>
        [Required]
        public string BucketName { get; set; } = string.Empty;

        /// <summary>
        /// B2 application key ID
        /// </summary>
        [Required]
        public string ApplicationKeyId { get; set; } = string.Empty;

        /// <summary>
        /// B2 application key
        /// </summary>
        [Required]
        public string ApplicationKey { get; set; } = string.Empty;

        /// <summary>
        /// B2 bucket ID
        /// </summary>
        [Required]
        public string BucketId { get; set; } = string.Empty;

        /// <summary>
        /// B2 download URL base
        /// </summary>
        public string DownloadUrlBase { get; set; } = "https://f002.backblazeb2.com/file";

        /// <summary>
        /// Enable lifecycle rules
        /// </summary>
        public bool EnableLifecycleRules { get; set; } = false;

        /// <summary>
        /// Default file info metadata
        /// </summary>
        public Dictionary<string, string> DefaultFileInfo { get; set; } = new();
    }

    /// <summary>
    /// Cache configuration for performance optimization
    /// </summary>
    public class CacheConfig
    {
        /// <summary>
        /// Enable in-memory caching
        /// </summary>
        public bool EnableMemoryCache { get; set; } = true;

        /// <summary>
        /// Cache expiration time in minutes
        /// </summary>
        public int CacheExpirationMinutes { get; set; } = 30;

        /// <summary>
        /// Maximum cache size in MB
        /// </summary>
        public int MaxCacheSizeMB { get; set; } = 100;

        /// <summary>
        /// Enable cache compression
        /// </summary>
        public bool EnableCacheCompression { get; set; } = true;

        /// <summary>
        /// Cache cleanup interval in minutes
        /// </summary>
        public int CleanupIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// Enable cache warming on startup
        /// </summary>
        public bool EnableCacheWarming { get; set; } = false;
    }
}