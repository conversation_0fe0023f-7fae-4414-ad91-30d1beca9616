namespace YendorCats.API.Configuration
{
    /// <summary>
    /// Configuration options for database seeding
    /// </summary>
    public class SeedingConfiguration
    {
        public const string SectionName = "Seeding";

        /// <summary>
        /// Enable sample data seeding in development environment
        /// </summary>
        public bool EnableSampleData { get; set; } = false;

        /// <summary>
        /// Force reseed even if sample data already exists
        /// </summary>
        public bool ForceReseed { get; set; } = false;

        /// <summary>
        /// Number of cat profiles to generate
        /// </summary>
        public int ProfileCount { get; set; } = 25;

        /// <summary>
        /// Average number of images per profile
        /// </summary>
        public int ImagesPerProfile { get; set; } = 3;

        /// <summary>
        /// Enable B2 sync log generation
        /// </summary>
        public bool EnableB2Sync { get; set; } = true;

        /// <summary>
        /// Validate configuration values
        /// </summary>
        public void Validate()
        {
            if (ProfileCount < 0)
                throw new ArgumentException("ProfileCount must be non-negative", nameof(ProfileCount));

            if (ImagesPerProfile < 0)
                throw new ArgumentException("ImagesPerProfile must be non-negative", nameof(ImagesPerProfile));

            if (ProfileCount > 1000)
                throw new ArgumentException("ProfileCount should not exceed 1000 for performance reasons", nameof(ProfileCount));

            if (ImagesPerProfile > 20)
                throw new ArgumentException("ImagesPerProfile should not exceed 20 for performance reasons", nameof(ImagesPerProfile));
        }

        /// <summary>
        /// Convert to SeedingOptions for the seeding service
        /// </summary>
        public Services.SeedingOptions ToSeedingOptions()
        {
            return new Services.SeedingOptions
            {
                ProfileCount = ProfileCount,
                ImagesPerProfile = ImagesPerProfile,
                EnableB2Sync = EnableB2Sync,
                ForceReseed = ForceReseed
            };
        }
    }
}
