namespace YendorCats.API.Configuration
{
    /// <summary>
    /// Configuration for performance optimization settings
    /// Centralizes all performance-related configuration for the gallery system
    /// </summary>
    public class PerformanceConfiguration
    {
        public const string SectionName = "Performance";

        /// <summary>
        /// Cache configuration settings
        /// </summary>
        public CacheConfiguration Cache { get; set; } = new();

        /// <summary>
        /// Database performance settings
        /// </summary>
        public DatabasePerformanceConfiguration Database { get; set; } = new();

        /// <summary>
        /// API performance settings
        /// </summary>
        public ApiPerformanceConfiguration Api { get; set; } = new();

        /// <summary>
        /// Image processing performance settings
        /// </summary>
        public ImageProcessingConfiguration ImageProcessing { get; set; } = new();

        /// <summary>
        /// Monitoring and metrics configuration
        /// </summary>
        public MonitoringConfiguration Monitoring { get; set; } = new();

        /// <summary>
        /// Performance targets and SLA settings
        /// </summary>
        public PerformanceTargets Targets { get; set; } = new();
    }

    /// <summary>
    /// Cache configuration settings
    /// </summary>
    public class CacheConfiguration
    {
        /// <summary>
        /// Memory cache settings
        /// </summary>
        public MemoryCacheSettings MemoryCache { get; set; } = new();

        /// <summary>
        /// Distributed cache settings
        /// </summary>
        public DistributedCacheSettings DistributedCache { get; set; } = new();

        /// <summary>
        /// Response caching settings
        /// </summary>
        public ResponseCacheSettings ResponseCache { get; set; } = new();

        /// <summary>
        /// Cache warmup settings
        /// </summary>
        public CacheWarmupSettings Warmup { get; set; } = new();
    }

    /// <summary>
    /// Memory cache configuration
    /// </summary>
    public class MemoryCacheSettings
    {
        /// <summary>
        /// Maximum memory cache size in MB
        /// </summary>
        public int SizeLimitMB { get; set; } = 512;

        /// <summary>
        /// Default expiration time for gallery images
        /// </summary>
        public TimeSpan GalleryImageExpiry { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Default expiration time for category lists
        /// </summary>
        public TimeSpan CategoryListExpiry { get; set; } = TimeSpan.FromMinutes(3);

        /// <summary>
        /// Default expiration time for search results
        /// </summary>
        public TimeSpan SearchResultExpiry { get; set; } = TimeSpan.FromMinutes(2);

        /// <summary>
        /// Default expiration time for statistics
        /// </summary>
        public TimeSpan StatisticsExpiry { get; set; } = TimeSpan.FromMinutes(15);

        /// <summary>
        /// Compaction percentage (when to start evicting items)
        /// </summary>
        public double CompactionPercentage { get; set; } = 0.25;
    }

    /// <summary>
    /// Distributed cache configuration
    /// </summary>
    public class DistributedCacheSettings
    {
        /// <summary>
        /// Whether distributed cache is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Redis connection string (if using Redis)
        /// </summary>
        public string? ConnectionString { get; set; }

        /// <summary>
        /// Default expiration time for distributed cache
        /// </summary>
        public TimeSpan DefaultExpiry { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// Sliding expiration time
        /// </summary>
        public TimeSpan? SlidingExpiry { get; set; } = TimeSpan.FromMinutes(10);

        /// <summary>
        /// Key prefix for cache entries
        /// </summary>
        public string KeyPrefix { get; set; } = "yendorcats:";

        /// <summary>
        /// Serialization options
        /// </summary>
        public CacheSerializationOptions Serialization { get; set; } = new();
    }

    /// <summary>
    /// Cache serialization options
    /// </summary>
    public class CacheSerializationOptions
    {
        /// <summary>
        /// Whether to compress cached data
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// Compression threshold in bytes
        /// </summary>
        public int CompressionThreshold { get; set; } = 1024;

        /// <summary>
        /// JSON serialization options
        /// </summary>
        public bool IgnoreNullValues { get; set; } = true;

        /// <summary>
        /// Whether to use camel case property names
        /// </summary>
        public bool UseCamelCase { get; set; } = true;
    }

    /// <summary>
    /// Response cache settings
    /// </summary>
    public class ResponseCacheSettings
    {
        /// <summary>
        /// Whether response caching is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Default cache duration for gallery endpoints
        /// </summary>
        public TimeSpan GalleryDuration { get; set; } = TimeSpan.FromMinutes(10);

        /// <summary>
        /// Cache duration for search results
        /// </summary>
        public TimeSpan SearchDuration { get; set; } = TimeSpan.FromMinutes(3);

        /// <summary>
        /// Cache duration for statistics
        /// </summary>
        public TimeSpan StatsDuration { get; set; } = TimeSpan.FromMinutes(15);

        /// <summary>
        /// Cache duration for popular content
        /// </summary>
        public TimeSpan PopularContentDuration { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// Whether to vary by query parameters
        /// </summary>
        public bool VaryByQueryKeys { get; set; } = true;

        /// <summary>
        /// Whether to vary by user
        /// </summary>
        public bool VaryByUser { get; set; } = false;
    }

    /// <summary>
    /// Cache warmup settings
    /// </summary>
    public class CacheWarmupSettings
    {
        /// <summary>
        /// Whether cache warmup is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Warmup schedule (cron expression)
        /// </summary>
        public string Schedule { get; set; } = "0 */30 * * * *"; // Every 30 minutes

        /// <summary>
        /// Number of items to warm up per category
        /// </summary>
        public int ItemsPerCategory { get; set; } = 50;

        /// <summary>
        /// Categories to warm up
        /// </summary>
        public List<string> Categories { get; set; } = new() { "studs", "queens", "kittens", "gallery" };

        /// <summary>
        /// Whether to warm up popular content
        /// </summary>
        public bool WarmupPopularContent { get; set; } = true;

        /// <summary>
        /// Whether to warm up recent content
        /// </summary>
        public bool WarmupRecentContent { get; set; } = true;
    }

    /// <summary>
    /// Database performance configuration
    /// </summary>
    public class DatabasePerformanceConfiguration
    {
        /// <summary>
        /// Connection pool settings
        /// </summary>
        public ConnectionPoolSettings ConnectionPool { get; set; } = new();

        /// <summary>
        /// Query optimization settings
        /// </summary>
        public QueryOptimizationSettings QueryOptimization { get; set; } = new();

        /// <summary>
        /// Batch operation settings
        /// </summary>
        public BatchOperationSettings BatchOperations { get; set; } = new();
    }

    /// <summary>
    /// Connection pool settings
    /// </summary>
    public class ConnectionPoolSettings
    {
        /// <summary>
        /// Maximum number of connections in pool
        /// </summary>
        public int MaxPoolSize { get; set; } = 100;

        /// <summary>
        /// Minimum number of connections in pool
        /// </summary>
        public int MinPoolSize { get; set; } = 5;

        /// <summary>
        /// Connection timeout in seconds
        /// </summary>
        public int ConnectionTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Command timeout in seconds
        /// </summary>
        public int CommandTimeoutSeconds { get; set; } = 30;
    }

    /// <summary>
    /// Query optimization settings
    /// </summary>
    public class QueryOptimizationSettings
    {
        /// <summary>
        /// Whether to enable query result caching
        /// </summary>
        public bool EnableQueryResultCaching { get; set; } = true;

        /// <summary>
        /// Default page size for paginated queries
        /// </summary>
        public int DefaultPageSize { get; set; } = 20;

        /// <summary>
        /// Maximum page size allowed
        /// </summary>
        public int MaxPageSize { get; set; } = 100;

        /// <summary>
        /// Whether to use compiled queries
        /// </summary>
        public bool UseCompiledQueries { get; set; } = true;

        /// <summary>
        /// Whether to track query performance
        /// </summary>
        public bool TrackQueryPerformance { get; set; } = true;
    }

    /// <summary>
    /// Batch operation settings
    /// </summary>
    public class BatchOperationSettings
    {
        /// <summary>
        /// Default batch size for bulk operations
        /// </summary>
        public int DefaultBatchSize { get; set; } = 100;

        /// <summary>
        /// Maximum batch size allowed
        /// </summary>
        public int MaxBatchSize { get; set; } = 1000;

        /// <summary>
        /// Timeout for batch operations in seconds
        /// </summary>
        public int BatchTimeoutSeconds { get; set; } = 300;
    }

    /// <summary>
    /// API performance configuration
    /// </summary>
    public class ApiPerformanceConfiguration
    {
        /// <summary>
        /// Rate limiting settings
        /// </summary>
        public RateLimitingSettings RateLimiting { get; set; } = new();

        /// <summary>
        /// Compression settings
        /// </summary>
        public CompressionSettings Compression { get; set; } = new();

        /// <summary>
        /// Request/response optimization
        /// </summary>
        public RequestOptimizationSettings RequestOptimization { get; set; } = new();
    }

    /// <summary>
    /// Rate limiting settings
    /// </summary>
    public class RateLimitingSettings
    {
        /// <summary>
        /// Whether rate limiting is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Requests per minute for anonymous users
        /// </summary>
        public int AnonymousRequestsPerMinute { get; set; } = 100;

        /// <summary>
        /// Requests per minute for authenticated users
        /// </summary>
        public int AuthenticatedRequestsPerMinute { get; set; } = 500;

        /// <summary>
        /// Requests per minute for admin users
        /// </summary>
        public int AdminRequestsPerMinute { get; set; } = 1000;
    }

    /// <summary>
    /// Compression settings
    /// </summary>
    public class CompressionSettings
    {
        /// <summary>
        /// Whether response compression is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Compression providers (gzip, brotli, etc.)
        /// </summary>
        public List<string> Providers { get; set; } = new() { "br", "gzip", "deflate" };

        /// <summary>
        /// Minimum response size to compress (bytes)
        /// </summary>
        public int MinimumSizeBytes { get; set; } = 1024;

        /// <summary>
        /// MIME types to compress
        /// </summary>
        public List<string> MimeTypes { get; set; } = new()
        {
            "application/json",
            "application/xml",
            "text/plain",
            "text/html",
            "text/css",
            "application/javascript"
        };
    }

    /// <summary>
    /// Request optimization settings
    /// </summary>
    public class RequestOptimizationSettings
    {
        /// <summary>
        /// Maximum request body size in MB
        /// </summary>
        public int MaxRequestBodySizeMB { get; set; } = 10;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int RequestTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Whether to enable request buffering
        /// </summary>
        public bool EnableRequestBuffering { get; set; } = false;

        /// <summary>
        /// Whether to enable response buffering
        /// </summary>
        public bool EnableResponseBuffering { get; set; } = false;
    }

    /// <summary>
    /// Image processing configuration
    /// </summary>
    public class ImageProcessingConfiguration
    {
        /// <summary>
        /// Thumbnail generation settings
        /// </summary>
        public ThumbnailSettings Thumbnails { get; set; } = new();

        /// <summary>
        /// Image optimization settings
        /// </summary>
        public ImageOptimizationSettings Optimization { get; set; } = new();
    }

    /// <summary>
    /// Thumbnail settings
    /// </summary>
    public class ThumbnailSettings
    {
        /// <summary>
        /// Whether thumbnail generation is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Thumbnail sizes to generate
        /// </summary>
        public Dictionary<string, int> Sizes { get; set; } = new()
        {
            { "small", 150 },
            { "medium", 300 },
            { "large", 600 }
        };

        /// <summary>
        /// JPEG quality for thumbnails (1-100)
        /// </summary>
        public int JpegQuality { get; set; } = 85;

        /// <summary>
        /// Whether to generate thumbnails asynchronously
        /// </summary>
        public bool AsyncGeneration { get; set; } = true;
    }

    /// <summary>
    /// Image optimization settings
    /// </summary>
    public class ImageOptimizationSettings
    {
        /// <summary>
        /// Whether image optimization is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Maximum image dimensions
        /// </summary>
        public int MaxWidth { get; set; } = 2048;
        public int MaxHeight { get; set; } = 2048;

        /// <summary>
        /// JPEG quality for optimized images (1-100)
        /// </summary>
        public int JpegQuality { get; set; } = 90;

        /// <summary>
        /// Whether to strip metadata from images
        /// </summary>
        public bool StripMetadata { get; set; } = false;
    }

    /// <summary>
    /// Monitoring configuration
    /// </summary>
    public class MonitoringConfiguration
    {
        /// <summary>
        /// Whether performance monitoring is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Metrics collection interval
        /// </summary>
        public TimeSpan MetricsInterval { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// How long to retain performance data
        /// </summary>
        public TimeSpan DataRetentionPeriod { get; set; } = TimeSpan.FromDays(30);

        /// <summary>
        /// Whether to enable detailed logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = false;

        /// <summary>
        /// Slow request threshold
        /// </summary>
        public TimeSpan SlowRequestThreshold { get; set; } = TimeSpan.FromMilliseconds(1000);
    }

    /// <summary>
    /// Performance targets and SLA settings
    /// </summary>
    public class PerformanceTargets
    {
        /// <summary>
        /// Target API response time (95th percentile)
        /// </summary>
        public TimeSpan ApiResponseTimeP95 { get; set; } = TimeSpan.FromMilliseconds(500);

        /// <summary>
        /// Target cache hit rate
        /// </summary>
        public double CacheHitRateTarget { get; set; } = 0.80; // 80%

        /// <summary>
        /// Target error rate
        /// </summary>
        public double ErrorRateTarget { get; set; } = 0.01; // 1%

        /// <summary>
        /// Target throughput (requests per second)
        /// </summary>
        public double ThroughputTarget { get; set; } = 100;

        /// <summary>
        /// Performance improvement target over baseline
        /// </summary>
        public double ImprovementTarget { get; set; } = 0.85; // 85% improvement
    }
}
