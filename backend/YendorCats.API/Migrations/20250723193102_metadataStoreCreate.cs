﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace YendorCats.API.Migrations
{
    /// <inheritdoc />
    public partial class metadataStoreCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_Category",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_CatName",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "Age",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "ImageUrl",
                table: "CatGalleryImages");

            migrationBuilder.RenameColumn(
                name: "OrderNumber",
                table: "CatGalleryImages",
                newName: "SortOrder");

            migrationBuilder.RenameColumn(
                name: "Mother",
                table: "CatGalleryImages",
                newName: "StorageFileId");

            migrationBuilder.RenameColumn(
                name: "Father",
                table: "CatGalleryImages",
                newName: "S3Bucket");

            migrationBuilder.AlterColumn<int>(
                name: "Width",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "Height",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "FileFormat",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 10,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 20);

            migrationBuilder.AlterColumn<DateTime>(
                name: "DateTaken",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "CatName",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Breed",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<long>(
                name: "Id",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "TEXT")
                .Annotation("Sqlite:Autoincrement", true);

            migrationBuilder.AddColumn<int>(
                name: "AccessCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "AgeAtPhoto",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Alt",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<float>(
                name: "AspectRatio",
                table: "CatGalleryImages",
                type: "REAL",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2Bucket",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2FileId",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2Key",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "B2Url",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CatId",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ContentType",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateModified",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DownloadCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ExifData",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Filename",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Format",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsFeatured",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsPublic",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastAccessedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastViewedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "LikeCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "MimeType",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ModifiedBy",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OriginalFileName",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "S3Key",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "S3Url",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StorageBucketName",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "StorageKey",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "StorageProvider",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ThumbnailStorageKey",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "ViewCount",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "CatProfiles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CatId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CatName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Breed = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Bloodline = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Gender = table.Column<string>(type: "TEXT", maxLength: 1, nullable: false),
                    BirthDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    BreedingStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    AvailabilityStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    FatherId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    MotherId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ChampionTitles = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    RegistrationNumber = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    RegisteredName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    RegistrationBody = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Color = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Pattern = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Markings = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    EyeColor = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Weight = table.Column<decimal>(type: "TEXT", nullable: true),
                    Length = table.Column<decimal>(type: "TEXT", nullable: true),
                    Height = table.Column<decimal>(type: "TEXT", nullable: true),
                    HealthRecords = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    GeneticTesting = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Vaccinations = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    LastHealthCheck = table.Column<DateTime>(type: "TEXT", nullable: true),
                    VeterinarianContact = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    PersonalityDescription = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    BehaviorTraits = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    SpecialNeeds = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    TotalLitters = table.Column<int>(type: "INTEGER", nullable: true),
                    FirstBreedingDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastBreedingDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    BreedingNotes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    CurrentLocation = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    CaregiverName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    CaregiverContact = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ProfileImageStorageKey = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ProfileImageStorageProvider = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    PurchasePrice = table.Column<decimal>(type: "TEXT", nullable: true),
                    CurrentValue = table.Column<decimal>(type: "TEXT", nullable: true),
                    StudFee = table.Column<decimal>(type: "TEXT", nullable: true),
                    KittenPrice = table.Column<decimal>(type: "TEXT", nullable: true),
                    PricingNotes = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    IsPublic = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
                    IsFeatured = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: false),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    ModifiedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    BloodlineType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Pedigree = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    MicrochipNumber = table.Column<string>(type: "TEXT", maxLength: 15, nullable: true),
                    Awards = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    IsBreeding = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsStudService = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsRetired = table.Column<bool>(type: "INTEGER", nullable: false),
                    ViewCount = table.Column<int>(type: "INTEGER", nullable: false),
                    LikeCount = table.Column<int>(type: "INTEGER", nullable: false),
                    OffspringCount = table.Column<int>(type: "INTEGER", nullable: false),
                    SireId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    SireName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DamId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    DamName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Microchip = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Tattoo = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Titles = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    HealthStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    HealthNotes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    VetInfo = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    OwnerInfo = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ProfileImage = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Personality = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    CatProfileId = table.Column<int>(type: "INTEGER", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CatProfiles", x => x.Id);
                    table.UniqueConstraint("AK_CatProfiles_CatId", x => x.CatId);
                    table.ForeignKey(
                        name: "FK_CatProfiles_CatProfiles_CatProfileId",
                        column: x => x.CatProfileId,
                        principalTable: "CatProfiles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "B2SyncLogs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    StorageKey = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Operation = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    SourceProvider = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    DestinationProvider = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    SourceBucket = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DestinationBucket = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    SourceKey = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    DestinationKey = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    OriginalFileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: true),
                    FileSize = table.Column<long>(type: "INTEGER", nullable: true),
                    ContentType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FileHash = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MetadataJson = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    MetadataFieldCount = table.Column<int>(type: "INTEGER", nullable: true),
                    SyncedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    StartedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    CompletedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    DurationMs = table.Column<int>(type: "INTEGER", nullable: true),
                    RetryCount = table.Column<int>(type: "INTEGER", nullable: false, defaultValue: 0),
                    MaxRetries = table.Column<int>(type: "INTEGER", nullable: false),
                    ErrorMessage = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    ErrorCode = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    NextRetryAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    UserId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    SessionId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    UserAgent = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    IpAddress = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BatchId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MigrationId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    BatchSequence = table.Column<int>(type: "INTEGER", nullable: true),
                    TotalBatchItems = table.Column<int>(type: "INTEGER", nullable: true),
                    CatGalleryImageId = table.Column<long>(type: "INTEGER", nullable: true),
                    CatProfileId = table.Column<int>(type: "INTEGER", nullable: true),
                    CatId = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Category = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    IsVerified = table.Column<bool>(type: "INTEGER", nullable: false),
                    VerifiedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    VerifiedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    RequiresManualReview = table.Column<bool>(type: "INTEGER", nullable: false),
                    ReviewNotes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Context = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    DestinationFileId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    S3Key = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    S3Bucket = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    B2Bucket = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    B2FileId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Checksum = table.Column<string>(type: "TEXT", maxLength: 64, nullable: true),
                    ProcessingTimeMs = table.Column<int>(type: "INTEGER", nullable: true),
                    ErrorDetails = table.Column<string>(type: "TEXT", maxLength: 4000, nullable: true),
                    B2Key = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_B2SyncLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_B2SyncLogs_CatGalleryImages_CatGalleryImageId",
                        column: x => x.CatGalleryImageId,
                        principalTable: "CatGalleryImages",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_B2SyncLogs_CatProfiles_CatProfileId",
                        column: x => x.CatProfileId,
                        principalTable: "CatProfiles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_B2Key",
                table: "CatGalleryImages",
                column: "B2Key");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CatId",
                table: "CatGalleryImages",
                column: "CatId");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CatId_IsActive_IsPublic",
                table: "CatGalleryImages",
                columns: new[] { "CatId", "IsActive", "IsPublic" });

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CreatedAt",
                table: "CatGalleryImages",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_DisplayOrder",
                table: "CatGalleryImages",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_Filename",
                table: "CatGalleryImages",
                column: "Filename",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsActive",
                table: "CatGalleryImages",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsFeatured",
                table: "CatGalleryImages",
                column: "IsFeatured");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsFeatured_DisplayOrder",
                table: "CatGalleryImages",
                columns: new[] { "IsFeatured", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_IsPublic",
                table: "CatGalleryImages",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_S3Key",
                table: "CatGalleryImages",
                column: "S3Key");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_StorageProvider",
                table: "CatGalleryImages",
                column: "StorageProvider");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_StorageProvider_IsActive",
                table: "CatGalleryImages",
                columns: new[] { "StorageProvider", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_ViewCount",
                table: "CatGalleryImages",
                column: "ViewCount");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_B2FileId",
                table: "B2SyncLogs",
                column: "B2FileId");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_B2Key",
                table: "B2SyncLogs",
                column: "B2Key");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_BatchId",
                table: "B2SyncLogs",
                column: "BatchId");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_BatchId_Status",
                table: "B2SyncLogs",
                columns: new[] { "BatchId", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_CatGalleryImageId",
                table: "B2SyncLogs",
                column: "CatGalleryImageId");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_CatProfileId",
                table: "B2SyncLogs",
                column: "CatProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_CompletedAt",
                table: "B2SyncLogs",
                column: "CompletedAt");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_CreatedAt",
                table: "B2SyncLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_FileName",
                table: "B2SyncLogs",
                column: "FileName");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Operation",
                table: "B2SyncLogs",
                column: "Operation");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Operation_Status",
                table: "B2SyncLogs",
                columns: new[] { "Operation", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Operation_Status_CreatedAt",
                table: "B2SyncLogs",
                columns: new[] { "Operation", "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_S3Key",
                table: "B2SyncLogs",
                column: "S3Key");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Status",
                table: "B2SyncLogs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_B2SyncLogs_Status_CreatedAt",
                table: "B2SyncLogs",
                columns: new[] { "Status", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_AvailabilityStatus",
                table: "CatProfiles",
                column: "AvailabilityStatus");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_AvailabilityStatus_Gender_IsActive",
                table: "CatProfiles",
                columns: new[] { "AvailabilityStatus", "Gender", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_BirthDate",
                table: "CatProfiles",
                column: "BirthDate");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Bloodline",
                table: "CatProfiles",
                column: "Bloodline");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Breed",
                table: "CatProfiles",
                column: "Breed");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Breed_IsActive_IsPublic",
                table: "CatProfiles",
                columns: new[] { "Breed", "IsActive", "IsPublic" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_BreedingStatus",
                table: "CatProfiles",
                column: "BreedingStatus");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CatId",
                table: "CatProfiles",
                column: "CatId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CatName",
                table: "CatProfiles",
                column: "CatName");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CatProfileId",
                table: "CatProfiles",
                column: "CatProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_CreatedAt",
                table: "CatProfiles",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_DamId",
                table: "CatProfiles",
                column: "DamId");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_DisplayOrder",
                table: "CatProfiles",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Gender",
                table: "CatProfiles",
                column: "Gender");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_Gender_BreedingStatus_IsActive",
                table: "CatProfiles",
                columns: new[] { "Gender", "BreedingStatus", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsActive",
                table: "CatProfiles",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsFeatured",
                table: "CatProfiles",
                column: "IsFeatured");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsFeatured_DisplayOrder",
                table: "CatProfiles",
                columns: new[] { "IsFeatured", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_IsPublic",
                table: "CatProfiles",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_SireId",
                table: "CatProfiles",
                column: "SireId");

            migrationBuilder.CreateIndex(
                name: "IX_CatProfiles_SireId_DamId",
                table: "CatProfiles",
                columns: new[] { "SireId", "DamId" });

            migrationBuilder.AddForeignKey(
                name: "FK_CatGalleryImages_CatProfiles_CatId",
                table: "CatGalleryImages",
                column: "CatId",
                principalTable: "CatProfiles",
                principalColumn: "CatId",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CatGalleryImages_CatProfiles_CatId",
                table: "CatGalleryImages");

            migrationBuilder.DropTable(
                name: "B2SyncLogs");

            migrationBuilder.DropTable(
                name: "CatProfiles");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_B2Key",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_CatId",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_CatId_IsActive_IsPublic",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_CreatedAt",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_DisplayOrder",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_Filename",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_IsActive",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_IsFeatured",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_IsFeatured_DisplayOrder",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_IsPublic",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_S3Key",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_StorageProvider",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_StorageProvider_IsActive",
                table: "CatGalleryImages");

            migrationBuilder.DropIndex(
                name: "IX_CatGalleryImages_ViewCount",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "AccessCount",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "AgeAtPhoto",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "Alt",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "AspectRatio",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "B2Bucket",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "B2FileId",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "B2Key",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "B2Url",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "CatId",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "ContentType",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "DateModified",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "DownloadCount",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "ExifData",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "Filename",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "Format",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "IsFeatured",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "IsPublic",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "LastAccessedAt",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "LastViewedAt",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "LikeCount",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "MimeType",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "ModifiedBy",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "OriginalFileName",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "S3Key",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "S3Url",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "StorageBucketName",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "StorageKey",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "StorageProvider",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "ThumbnailStorageKey",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "CatGalleryImages");

            migrationBuilder.DropColumn(
                name: "ViewCount",
                table: "CatGalleryImages");

            migrationBuilder.RenameColumn(
                name: "StorageFileId",
                table: "CatGalleryImages",
                newName: "Mother");

            migrationBuilder.RenameColumn(
                name: "SortOrder",
                table: "CatGalleryImages",
                newName: "OrderNumber");

            migrationBuilder.RenameColumn(
                name: "S3Bucket",
                table: "CatGalleryImages",
                newName: "Father");

            migrationBuilder.AlterColumn<int>(
                name: "Width",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<int>(
                name: "Height",
                table: "CatGalleryImages",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AlterColumn<string>(
                name: "FileFormat",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 10,
                oldNullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "DateTaken",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "CatName",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Breed",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "Id",
                table: "CatGalleryImages",
                type: "TEXT",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "INTEGER")
                .OldAnnotation("Sqlite:Autoincrement", true);

            migrationBuilder.AddColumn<float>(
                name: "Age",
                table: "CatGalleryImages",
                type: "REAL",
                nullable: false,
                defaultValue: 0f);

            migrationBuilder.AddColumn<string>(
                name: "ImageUrl",
                table: "CatGalleryImages",
                type: "TEXT",
                maxLength: 1000,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_Category",
                table: "CatGalleryImages",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_CatGalleryImages_CatName",
                table: "CatGalleryImages",
                column: "CatName");
        }
    }
}
