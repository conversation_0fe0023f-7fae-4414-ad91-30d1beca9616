using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Client model for managing clients
    /// </summary>
    public class Client
    {
        /// <summary>
        /// Unique identifier for the client
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// First name of the client
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the client
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Email address of the client
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Phone number of the client
        /// </summary>
        [StringLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Address of the client
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// City of the client
        /// </summary>
        [StringLength(50)]
        public string? City { get; set; }

        /// <summary>
        /// State/Province of the client
        /// </summary>
        [StringLength(50)]
        public string? State { get; set; }

        /// <summary>
        /// Postal/Zip code of the client
        /// </summary>
        [StringLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// Country of the client
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }

        /// <summary>
        /// Notes about the client
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// When the client was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the client was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Whether the client is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Navigation property for client's appointments
        /// </summary>
        public virtual ICollection<Appointment>? Appointments { get; set; }
    }

    /// <summary>
    /// Client creation request model
    /// </summary>
    public class CreateClientRequest
    {
        /// <summary>
        /// First name of the client
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the client
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Email address of the client
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Phone number of the client
        /// </summary>
        [StringLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Address of the client
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// City of the client
        /// </summary>
        [StringLength(50)]
        public string? City { get; set; }

        /// <summary>
        /// State/Province of the client
        /// </summary>
        [StringLength(50)]
        public string? State { get; set; }

        /// <summary>
        /// Postal/Zip code of the client
        /// </summary>
        [StringLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// Country of the client
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }

        /// <summary>
        /// Notes about the client
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Client update request model
    /// </summary>
    public class UpdateClientRequest
    {
        /// <summary>
        /// First name of the client
        /// </summary>
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the client
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Email address of the client
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Phone number of the client
        /// </summary>
        [StringLength(20)]
        public string? Phone { get; set; }

        /// <summary>
        /// Address of the client
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// City of the client
        /// </summary>
        [StringLength(50)]
        public string? City { get; set; }

        /// <summary>
        /// State/Province of the client
        /// </summary>
        [StringLength(50)]
        public string? State { get; set; }

        /// <summary>
        /// Postal/Zip code of the client
        /// </summary>
        [StringLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// Country of the client
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }

        /// <summary>
        /// Notes about the client
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether the client is active
        /// </summary>
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// Client response model
    /// </summary>
    public class ClientResponse
    {
        /// <summary>
        /// Unique identifier for the client
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// First name of the client
        /// </summary>
        public string FirstName { get; set; } = string.Empty;

        /// <summary>
        /// Last name of the client
        /// </summary>
        public string LastName { get; set; } = string.Empty;

        /// <summary>
        /// Full name of the client
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// Email address of the client
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Phone number of the client
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// Address of the client
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// City of the client
        /// </summary>
        public string? City { get; set; }

        /// <summary>
        /// State/Province of the client
        /// </summary>
        public string? State { get; set; }

        /// <summary>
        /// Postal/Zip code of the client
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// Country of the client
        /// </summary>
        public string? Country { get; set; }

        /// <summary>
        /// Notes about the client
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// When the client was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the client was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// Whether the client is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Number of appointments for this client
        /// </summary>
        public int AppointmentCount { get; set; }
    }
}
