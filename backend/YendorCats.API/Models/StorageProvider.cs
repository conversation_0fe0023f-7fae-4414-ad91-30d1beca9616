using System.ComponentModel;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Enumeration of supported storage providers for the hybrid storage architecture
    /// </summary>
    public enum StorageProvider
    {
        /// <summary>
        /// Amazon S3 storage provider (existing/legacy)
        /// </summary>
        [Description("Amazon S3")]
        S3 = 1,
        
        /// <summary>
        /// Backblaze B2 storage provider (new/primary)
        /// </summary>
        [Description("Backblaze B2")]
        B2 = 2,
        
        /// <summary>
        /// Local file system storage (for development/testing)
        /// </summary>
        [Description("Local Storage")]
        Local = 3,
        
        /// <summary>
        /// Azure Blob Storage (future expansion)
        /// </summary>
        [Description("Azure Blob Storage")]
        Azure = 4
    }
    
    /// <summary>
    /// Storage provider configuration for dual storage support
    /// </summary>
    public class StorageProviderConfig
    {
        /// <summary>
        /// Primary storage provider for new uploads
        /// </summary>
        public StorageProvider Primary { get; set; } = StorageProvider.B2;
        
        /// <summary>
        /// Secondary storage provider for backup/redundancy
        /// </summary>
        public StorageProvider? Secondary { get; set; } = StorageProvider.S3;
        
        /// <summary>
        /// Fallback storage provider if primary fails
        /// </summary>
        public StorageProvider Fallback { get; set; } = StorageProvider.S3;
        
        /// <summary>
        /// Whether to enable automatic failover to fallback storage
        /// </summary>
        public bool EnableFailover { get; set; } = true;
        
        /// <summary>
        /// Whether to enable cross-provider synchronization
        /// </summary>
        public bool EnableCrossProviderSync { get; set; } = false;
        
        /// <summary>
        /// Whether to maintain dual storage (save to both providers)
        /// </summary>
        public bool EnableDualStorage { get; set; } = false;
        
        /// <summary>
        /// Maximum retry attempts for failed operations
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;
        
        /// <summary>
        /// Delay between retry attempts in seconds
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 5;
        
        /// <summary>
        /// Whether to enable performance monitoring
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;
        
        /// <summary>
        /// Whether to enable audit logging
        /// </summary>
        public bool EnableAuditLogging { get; set; } = true;
        
        /// <summary>
        /// Get the display name for a storage provider
        /// </summary>
        public static string GetDisplayName(StorageProvider provider)
        {
            var field = provider.GetType().GetField(provider.ToString());
            if (field != null)
            {
                var attribute = (DescriptionAttribute?)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
                return attribute?.Description ?? provider.ToString();
            }
            return provider.ToString();
        }
        
        /// <summary>
        /// Get the string representation of a storage provider
        /// </summary>
        public static string GetProviderString(StorageProvider provider)
        {
            return provider switch
            {
                StorageProvider.S3 => "S3",
                StorageProvider.B2 => "B2",
                StorageProvider.Local => "LOCAL",
                StorageProvider.Azure => "AZURE",
                _ => provider.ToString().ToUpperInvariant()
            };
        }
        
        /// <summary>
        /// Parse a storage provider from string
        /// </summary>
        public static StorageProvider ParseProvider(string providerString)
        {
            return providerString?.ToUpperInvariant() switch
            {
                "S3" => StorageProvider.S3,
                "B2" => StorageProvider.B2,
                "LOCAL" => StorageProvider.Local,
                "AZURE" => StorageProvider.Azure,
                _ => StorageProvider.S3 // Default fallback
            };
        }
        
        /// <summary>
        /// Validate the storage provider configuration
        /// </summary>
        public bool IsValid()
        {
            // Primary provider must be set
            if (Primary == default)
                return false;
            
            // If dual storage is enabled, secondary must be set and different from primary
            if (EnableDualStorage && (Secondary == null || Secondary == Primary))
                return false;
            
            // Fallback should be different from primary for meaningful failover
            if (EnableFailover && Fallback == Primary)
                return false;
            
            // Retry settings should be reasonable
            if (MaxRetryAttempts < 0 || MaxRetryAttempts > 10)
                return false;
            
            if (RetryDelaySeconds < 0 || RetryDelaySeconds > 300)
                return false;
            
            return true;
        }
        
        /// <summary>
        /// Get the effective storage provider for read operations
        /// </summary>
        public StorageProvider GetReadProvider()
        {
            return Primary;
        }
        
        /// <summary>
        /// Get the effective storage provider for write operations
        /// </summary>
        public StorageProvider GetWriteProvider()
        {
            return Primary;
        }
        
        /// <summary>
        /// Get all configured storage providers
        /// </summary>
        public List<StorageProvider> GetAllProviders()
        {
            var providers = new List<StorageProvider> { Primary };
            
            if (Secondary.HasValue && Secondary.Value != Primary)
                providers.Add(Secondary.Value);
            
            if (Fallback != Primary && !providers.Contains(Fallback))
                providers.Add(Fallback);
            
            return providers;
        }
    }
    
    /// <summary>
    /// Storage provider capabilities and features
    /// </summary>
    public class StorageProviderCapabilities
    {
        public StorageProvider Provider { get; set; }
        public bool SupportsMetadata { get; set; }
        public bool SupportsVersioning { get; set; }
        public bool SupportsEncryption { get; set; }
        public bool SupportsCompression { get; set; }
        public bool SupportsCDN { get; set; }
        public bool SupportsDirectAccess { get; set; }
        public bool SupportsSignedUrls { get; set; }
        public bool SupportsLargeFiles { get; set; }
        public long MaxFileSize { get; set; }
        public int MaxMetadataSize { get; set; }
        public string[] SupportedRegions { get; set; } = Array.Empty<string>();
        public decimal CostPerGB { get; set; }
        public decimal CostPerRequest { get; set; }
        
        /// <summary>
        /// Get capabilities for a specific storage provider
        /// </summary>
        public static StorageProviderCapabilities GetCapabilities(StorageProvider provider)
        {
            return provider switch
            {
                StorageProvider.S3 => new StorageProviderCapabilities
                {
                    Provider = StorageProvider.S3,
                    SupportsMetadata = true,
                    SupportsVersioning = true,
                    SupportsEncryption = true,
                    SupportsCompression = false,
                    SupportsCDN = true,
                    SupportsDirectAccess = true,
                    SupportsSignedUrls = true,
                    SupportsLargeFiles = true,
                    MaxFileSize = 5L * 1024 * 1024 * 1024 * 1024, // 5TB
                    MaxMetadataSize = 2048,
                    SupportedRegions = new[] { "us-east-1", "us-west-2", "eu-west-1", "ap-southeast-2" },
                    CostPerGB = 0.023m,
                    CostPerRequest = 0.0004m
                },
                StorageProvider.B2 => new StorageProviderCapabilities
                {
                    Provider = StorageProvider.B2,
                    SupportsMetadata = true,
                    SupportsVersioning = true,
                    SupportsEncryption = true,
                    SupportsCompression = false,
                    SupportsCDN = true,
                    SupportsDirectAccess = true,
                    SupportsSignedUrls = true,
                    SupportsLargeFiles = true,
                    MaxFileSize = 10L * 1024 * 1024 * 1024 * 1024, // 10TB
                    MaxMetadataSize = 7168,
                    SupportedRegions = new[] { "us-west-002", "eu-central-003" },
                    CostPerGB = 0.005m,
                    CostPerRequest = 0.0004m
                },
                StorageProvider.Local => new StorageProviderCapabilities
                {
                    Provider = StorageProvider.Local,
                    SupportsMetadata = false,
                    SupportsVersioning = false,
                    SupportsEncryption = false,
                    SupportsCompression = false,
                    SupportsCDN = false,
                    SupportsDirectAccess = true,
                    SupportsSignedUrls = false,
                    SupportsLargeFiles = true,
                    MaxFileSize = long.MaxValue,
                    MaxMetadataSize = 0,
                    SupportedRegions = new[] { "local" },
                    CostPerGB = 0m,
                    CostPerRequest = 0m
                },
                StorageProvider.Azure => new StorageProviderCapabilities
                {
                    Provider = StorageProvider.Azure,
                    SupportsMetadata = true,
                    SupportsVersioning = true,
                    SupportsEncryption = true,
                    SupportsCompression = false,
                    SupportsCDN = true,
                    SupportsDirectAccess = true,
                    SupportsSignedUrls = true,
                    SupportsLargeFiles = true,
                    MaxFileSize = 4L * 1024 * 1024 * 1024 * 1024, // 4TB
                    MaxMetadataSize = 8192,
                    SupportedRegions = new[] { "eastus", "westus", "westeurope", "southeastasia" },
                    CostPerGB = 0.0184m,
                    CostPerRequest = 0.0004m
                },
                _ => new StorageProviderCapabilities
                {
                    Provider = provider,
                    SupportsMetadata = false,
                    SupportsVersioning = false,
                    SupportsEncryption = false,
                    SupportsCompression = false,
                    SupportsCDN = false,
                    SupportsDirectAccess = false,
                    SupportsSignedUrls = false,
                    SupportsLargeFiles = false,
                    MaxFileSize = 0,
                    MaxMetadataSize = 0,
                    SupportedRegions = Array.Empty<string>(),
                    CostPerGB = 0,
                    CostPerRequest = 0
                }
            };
        }
    }
}