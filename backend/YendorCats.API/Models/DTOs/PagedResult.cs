using System;
using System.Collections.Generic;
using System.Linq;

namespace YendorCats.API.Models.DTOs
{
    /// <summary>
    /// Generic paged result wrapper for API responses
    /// Provides comprehensive pagination metadata and performance tracking
    /// </summary>
    public class PagedResult<T>
    {
        /// <summary>
        /// The actual data items for the current page
        /// </summary>
        public IEnumerable<T> Items { get; set; } = new List<T>();
        
        /// <summary>
        /// Total number of items across all pages
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// Current page number (1-based)
        /// </summary>
        public int Page { get; set; }
        
        /// <summary>
        /// Number of items per page
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// Total number of pages
        /// </summary>
        public int TotalPages { get; set; }
        
        /// <summary>
        /// Whether there is a next page
        /// </summary>
        public bool HasNext { get; set; }
        
        /// <summary>
        /// Whether there is a previous page
        /// </summary>
        public bool HasPrevious { get; set; }

        /// <summary>
        /// Alias for HasNext - for backward compatibility
        /// </summary>
        public bool HasNextPage
        {
            get => HasNext;
            set => HasNext = value;
        }

        /// <summary>
        /// Alias for HasPrevious - for backward compatibility
        /// </summary>
        public bool HasPreviousPage
        {
            get => HasPrevious;
            set => HasPrevious = value;
        }
        
        /// <summary>
        /// Number of items in the current page
        /// </summary>
        public int ItemCount => Items?.Count() ?? 0;
        
        /// <summary>
        /// Starting item number for the current page (1-based)
        /// </summary>
        public int StartItem => Page > 0 ? ((Page - 1) * PageSize) + 1 : 0;
        
        /// <summary>
        /// Ending item number for the current page (1-based)
        /// </summary>
        public int EndItem => StartItem + ItemCount - 1;
        
        /// <summary>
        /// Whether this is the first page
        /// </summary>
        public bool IsFirstPage => Page == 1;
        
        /// <summary>
        /// Whether this is the last page
        /// </summary>
        public bool IsLastPage => Page == TotalPages;
        
        /// <summary>
        /// Whether there are any items
        /// </summary>
        public bool HasItems => ItemCount > 0;
        
        /// <summary>
        /// Whether the result set is empty
        /// </summary>
        public bool IsEmpty => ItemCount == 0;
        
        // Performance metrics
        /// <summary>
        /// Cache source for performance tracking
        /// </summary>
        public string? CacheSource { get; set; } // "memory", "distributed", "database"
        
        /// <summary>
        /// Query execution time
        /// </summary>
        public TimeSpan? QueryTime { get; set; }
        
        /// <summary>
        /// When the result was generated
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Database query execution time
        /// </summary>
        public TimeSpan? DatabaseQueryTime { get; set; }
        
        /// <summary>
        /// Serialization time
        /// </summary>
        public TimeSpan? SerializationTime { get; set; }
        
        /// <summary>
        /// Total processing time
        /// </summary>
        public TimeSpan? TotalProcessingTime { get; set; }
        
        // Additional metadata
        /// <summary>
        /// Sort criteria applied
        /// </summary>
        public string? SortBy { get; set; }
        
        /// <summary>
        /// Sort direction
        /// </summary>
        public string? SortDirection { get; set; }
        
        /// <summary>
        /// Applied filters
        /// </summary>
        public Dictionary<string, object>? Filters { get; set; }
        
        /// <summary>
        /// Search term if applicable
        /// </summary>
        public string? SearchTerm { get; set; }
        
        /// <summary>
        /// Category filter if applicable
        /// </summary>
        public string? Category { get; set; }
        
        /// <summary>
        /// Request ID for tracking
        /// </summary>
        public string? RequestId { get; set; }
        
        /// <summary>
        /// Create a paged result from a full dataset
        /// </summary>
        public static PagedResult<T> Create(IEnumerable<T> allItems, int page, int pageSize, 
            string? sortBy = null, string? sortDirection = null, string? searchTerm = null)
        {
            var totalCount = allItems.Count();
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            var items = allItems.Skip((page - 1) * pageSize).Take(pageSize);
            
            return new PagedResult<T>
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = totalPages,
                HasNext = page < totalPages,
                HasPrevious = page > 1,
                SortBy = sortBy,
                SortDirection = sortDirection,
                SearchTerm = searchTerm
            };
        }
        
        /// <summary>
        /// Create a paged result with pre-calculated totals (for database queries)
        /// </summary>
        public static PagedResult<T> CreateWithTotals(IEnumerable<T> pageItems, int totalCount, 
            int page, int pageSize, string? sortBy = null, string? sortDirection = null)
        {
            var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            
            return new PagedResult<T>
            {
                Items = pageItems,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = totalPages,
                HasNext = page < totalPages,
                HasPrevious = page > 1,
                SortBy = sortBy,
                SortDirection = sortDirection
            };
        }
        
        /// <summary>
        /// Create an empty paged result
        /// </summary>
        public static PagedResult<T> Empty(int page = 1, int pageSize = 20)
        {
            return new PagedResult<T>
            {
                Items = new List<T>(),
                TotalCount = 0,
                Page = page,
                PageSize = pageSize,
                TotalPages = 0,
                HasNext = false,
                HasPrevious = false
            };
        }
        
        /// <summary>
        /// Transform the items to a different type
        /// </summary>
        public PagedResult<TResult> Transform<TResult>(Func<T, TResult> transformer)
        {
            return new PagedResult<TResult>
            {
                Items = Items.Select(transformer),
                TotalCount = TotalCount,
                Page = Page,
                PageSize = PageSize,
                TotalPages = TotalPages,
                HasNext = HasNext,
                HasPrevious = HasPrevious,
                CacheSource = CacheSource,
                QueryTime = QueryTime,
                GeneratedAt = GeneratedAt,
                DatabaseQueryTime = DatabaseQueryTime,
                SerializationTime = SerializationTime,
                TotalProcessingTime = TotalProcessingTime,
                SortBy = SortBy,
                SortDirection = SortDirection,
                Filters = Filters,
                SearchTerm = SearchTerm,
                Category = Category,
                RequestId = RequestId
            };
        }
        
        /// <summary>
        /// Add performance tracking information
        /// </summary>
        public PagedResult<T> WithPerformanceTracking(TimeSpan? queryTime = null, 
            string? cacheSource = null, TimeSpan? dbQueryTime = null, TimeSpan? serializationTime = null)
        {
            QueryTime = queryTime;
            CacheSource = cacheSource;
            DatabaseQueryTime = dbQueryTime;
            SerializationTime = serializationTime;
            
            // Calculate total processing time
            if (DatabaseQueryTime.HasValue || SerializationTime.HasValue)
            {
                var total = TimeSpan.Zero;
                if (DatabaseQueryTime.HasValue) total += DatabaseQueryTime.Value;
                if (SerializationTime.HasValue) total += SerializationTime.Value;
                TotalProcessingTime = total;
            }
            
            return this;
        }
        
        /// <summary>
        /// Add filter information
        /// </summary>
        public PagedResult<T> WithFilters(Dictionary<string, object>? filters = null, 
            string? searchTerm = null, string? category = null)
        {
            Filters = filters;
            SearchTerm = searchTerm;
            Category = category;
            return this;
        }
        
        /// <summary>
        /// Add request tracking
        /// </summary>
        public PagedResult<T> WithRequestId(string requestId)
        {
            RequestId = requestId;
            return this;
        }
        
        /// <summary>
        /// Get pagination summary text
        /// </summary>
        public string GetPaginationSummary()
        {
            if (IsEmpty)
                return "No items found";
            
            if (TotalCount == 1)
                return "1 item";
            
            if (TotalPages == 1)
                return $"{TotalCount} items";
            
            return $"Showing {StartItem}-{EndItem} of {TotalCount} items (Page {Page} of {TotalPages})";
        }
        
        /// <summary>
        /// Get page navigation info
        /// </summary>
        public PageNavigationInfo GetNavigationInfo()
        {
            var pages = new List<int>();
            
            // Calculate which page numbers to show
            var startPage = Math.Max(1, Page - 2);
            var endPage = Math.Min(TotalPages, Page + 2);
            
            for (int i = startPage; i <= endPage; i++)
            {
                pages.Add(i);
            }
            
            return new PageNavigationInfo
            {
                CurrentPage = Page,
                TotalPages = TotalPages,
                PreviousPage = HasPrevious ? Page - 1 : null,
                NextPage = HasNext ? Page + 1 : null,
                FirstPage = 1,
                LastPage = TotalPages,
                VisiblePages = pages,
                HasPrevious = HasPrevious,
                HasNext = HasNext,
                ShowFirstPage = startPage > 1,
                ShowLastPage = endPage < TotalPages
            };
        }
        
        /// <summary>
        /// Validate pagination parameters
        /// </summary>
        public static (bool IsValid, string ErrorMessage) ValidatePagination(int page, int pageSize, 
            int maxPageSize = 100)
        {
            if (page < 1)
                return (false, "Page number must be greater than 0");
            
            if (pageSize < 1)
                return (false, "Page size must be greater than 0");
            
            if (pageSize > maxPageSize)
                return (false, $"Page size cannot exceed {maxPageSize}");
            
            return (true, string.Empty);
        }
        
        /// <summary>
        /// Get performance summary
        /// </summary>
        public string GetPerformanceSummary()
        {
            var parts = new List<string>();
            
            if (QueryTime.HasValue)
                parts.Add($"Query: {QueryTime.Value.TotalMilliseconds:F0}ms");
            
            if (DatabaseQueryTime.HasValue)
                parts.Add($"DB: {DatabaseQueryTime.Value.TotalMilliseconds:F0}ms");
            
            if (SerializationTime.HasValue)
                parts.Add($"Serialization: {SerializationTime.Value.TotalMilliseconds:F0}ms");
            
            if (!string.IsNullOrEmpty(CacheSource))
                parts.Add($"Cache: {CacheSource}");
            
            return string.Join(" | ", parts);
        }
    }
    
    /// <summary>
    /// Page navigation helper information
    /// </summary>
    public class PageNavigationInfo
    {
        public int CurrentPage { get; set; }
        public int TotalPages { get; set; }
        public int? PreviousPage { get; set; }
        public int? NextPage { get; set; }
        public int FirstPage { get; set; }
        public int LastPage { get; set; }
        public List<int> VisiblePages { get; set; } = new();
        public bool HasPrevious { get; set; }
        public bool HasNext { get; set; }
        public bool ShowFirstPage { get; set; }
        public bool ShowLastPage { get; set; }
    }
}