using System;

namespace YendorCats.API.Models.DTOs
{
    /// <summary>
    /// DTO for gallery image API responses with optimized data structure
    /// Designed for high-performance API responses with minimal data transfer
    /// </summary>
    public class GalleryImageDto
    {
        public long Id { get; set; }
        
        /// <summary>
        /// Storage key for the image (S3 key or B2 key)
        /// </summary>
        public string StorageKey { get; set; } = string.Empty;
        
        /// <summary>
        /// Public URL for direct image access
        /// </summary>
        public string ImageUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Thumbnail URL for optimized loading
        /// </summary>
        public string? ThumbnailUrl { get; set; }
        
        /// <summary>
        /// Original filename for download purposes
        /// </summary>
        public string OriginalFileName { get; set; } = string.Empty;
        
        // Cat information
        public string? CatName { get; set; }
        public string? CatId { get; set; }
        public string Category { get; set; } = string.Empty;
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? AgeAtPhoto { get; set; }
        public string Breed { get; set; } = string.Empty;
        public string? Bloodline { get; set; }
        public string? Gender { get; set; }
        
        // Dates
        public DateTime? DateTaken { get; set; }
        public DateTime DateUploaded { get; set; }
        
        // Image properties
        public int? Width { get; set; }
        public int? Height { get; set; }
        public float? AspectRatio { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; } = string.Empty;
        
        // Metadata
        public string? Tags { get; set; }
        public int AccessCount { get; set; }
        public DateTime? LastAccessedAt { get; set; }
        
        // Storage information
        public string StorageProvider { get; set; } = string.Empty;
        public string StorageBucketName { get; set; } = string.Empty;
        
        // Status
        public bool IsActive { get; set; }
        public bool IsPublic { get; set; }
        public int SortOrder { get; set; }
        
        // Performance tracking
        public string? CacheSource { get; set; } // "memory", "distributed", "database"
        public TimeSpan? QueryTime { get; set; }
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        
        // Helper properties
        public string DisplayName => !string.IsNullOrEmpty(Title) ? Title : CatName ?? "Unknown Cat";
        public string FormattedFileSize => FormatFileSize(FileSize);
        public string FormattedDimensions => Width.HasValue && Height.HasValue ? $"{Width}x{Height}" : "Unknown";
        public bool HasThumbnail => !string.IsNullOrEmpty(ThumbnailUrl);
        public bool IsLandscape => AspectRatio.HasValue && AspectRatio.Value > 1.0f;
        public bool IsPortrait => AspectRatio.HasValue && AspectRatio.Value < 1.0f;
        public bool IsSquare => AspectRatio.HasValue && Math.Abs(AspectRatio.Value - 1.0f) < 0.1f;
        
        /// <summary>
        /// Create DTO from entity
        /// </summary>
        public static GalleryImageDto FromEntity(CatGalleryImage entity)
        {
            return new GalleryImageDto
            {
                Id = entity.Id,
                StorageKey = entity.StorageKey,
                ImageUrl = entity.PublicUrl,
                ThumbnailUrl = GetThumbnailUrl(entity),
                OriginalFileName = entity.OriginalFileName,
                CatName = entity.CatName,
                CatId = entity.CatId,
                Category = entity.Category,
                Title = entity.Title,
                Description = entity.Description,
                AgeAtPhoto = entity.AgeAtPhoto,
                Breed = entity.Breed,
                Bloodline = entity.Bloodline,
                Gender = entity.Gender,
                DateTaken = entity.DateTaken,
                DateUploaded = entity.DateUploaded,
                Width = entity.Width,
                Height = entity.Height,
                AspectRatio = entity.AspectRatio,
                FileSize = entity.FileSize,
                ContentType = entity.ContentType,
                Tags = entity.Tags,
                AccessCount = entity.AccessCount,
                LastAccessedAt = entity.LastAccessedAt,
                StorageProvider = entity.StorageProvider,
                StorageBucketName = entity.StorageBucketName,
                IsActive = entity.IsActive,
                IsPublic = entity.IsPublic,
                SortOrder = entity.SortOrder
            };
        }
        
        /// <summary>
        /// Create lightweight DTO with minimal data for list views
        /// </summary>
        public static GalleryImageDto CreateLightweight(CatGalleryImage entity)
        {
            return new GalleryImageDto
            {
                Id = entity.Id,
                StorageKey = entity.StorageKey,
                ImageUrl = entity.PublicUrl,
                ThumbnailUrl = GetThumbnailUrl(entity),
                CatName = entity.CatName,
                Category = entity.Category,
                Title = entity.Title,
                DateTaken = entity.DateTaken,
                Width = entity.Width,
                Height = entity.Height,
                AspectRatio = entity.AspectRatio,
                FileSize = entity.FileSize,
                IsActive = entity.IsActive,
                IsPublic = entity.IsPublic,
                SortOrder = entity.SortOrder
            };
        }
        
        /// <summary>
        /// Get thumbnail URL based on storage provider
        /// </summary>
        private static string? GetThumbnailUrl(CatGalleryImage entity)
        {
            if (string.IsNullOrEmpty(entity.ThumbnailStorageKey))
                return null;
            
            return entity.StorageProvider switch
            {
                "B2" => $"https://f002.backblazeb2.com/file/{entity.StorageBucketName}/{entity.ThumbnailStorageKey}",
                "S3" => $"https://{entity.StorageBucketName}.s3.amazonaws.com/{entity.ThumbnailStorageKey}",
                _ => null
            };
        }
        
        /// <summary>
        /// Format file size for display
        /// </summary>
        private static string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 bytes";
            
            string[] sizes = { "bytes", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:0.##} {sizes[order]}";
        }
        
        /// <summary>
        /// Get age display text
        /// </summary>
        public string GetAgeDisplay()
        {
            if (string.IsNullOrEmpty(AgeAtPhoto))
                return "Unknown age";
            
            return AgeAtPhoto.Contains("month") || AgeAtPhoto.Contains("year") 
                ? AgeAtPhoto 
                : $"{AgeAtPhoto} years";
        }
        
        /// <summary>
        /// Get formatted upload date
        /// </summary>
        public string GetFormattedUploadDate()
        {
            return DateUploaded.ToString("MMMM dd, yyyy");
        }
        
        /// <summary>
        /// Get formatted taken date
        /// </summary>
        public string GetFormattedTakenDate()
        {
            return DateTaken?.ToString("MMMM dd, yyyy") ?? "Unknown date";
        }
        
        /// <summary>
        /// Get tags as list
        /// </summary>
        public List<string> GetTagsList()
        {
            if (string.IsNullOrEmpty(Tags))
                return new List<string>();
            
            return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(tag => tag.Trim())
                      .Where(tag => !string.IsNullOrEmpty(tag))
                      .ToList();
        }
        
        /// <summary>
        /// Check if image matches search criteria
        /// </summary>
        public bool MatchesSearch(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return true;
            
            var term = searchTerm.ToLowerInvariant();
            
            return (CatName?.ToLowerInvariant().Contains(term) == true) ||
                   (Title?.ToLowerInvariant().Contains(term) == true) ||
                   (Description?.ToLowerInvariant().Contains(term) == true) ||
                   (Breed?.ToLowerInvariant().Contains(term) == true) ||
                   (Bloodline?.ToLowerInvariant().Contains(term) == true) ||
                   (Tags?.ToLowerInvariant().Contains(term) == true);
        }
        
        /// <summary>
        /// Get responsive image URL based on requested size
        /// </summary>
        public string GetResponsiveImageUrl(int? maxWidth = null, int? maxHeight = null)
        {
            // For now, return the main image URL
            // In the future, this could generate different sizes
            if (maxWidth.HasValue && maxWidth.Value <= 300 && HasThumbnail)
                return ThumbnailUrl!;
            
            return ImageUrl;
        }
    }
}