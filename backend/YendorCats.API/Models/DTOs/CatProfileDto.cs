using System;
using System.Collections.Generic;
using System.Linq;

namespace YendorCats.API.Models.DTOs
{
    /// <summary>
    /// DTO for cat profile API responses with comprehensive breeding information
    /// Optimized for both detailed views and list views
    /// </summary>
    public class CatProfileDto
    {
        public int Id { get; set; }
        public string CatId { get; set; } = string.Empty;
        public string CatName { get; set; } = string.Empty;
        public string Breed { get; set; } = string.Empty;
        public string? Bloodline { get; set; }
        public string? Gender { get; set; }
        public DateTime? BirthDate { get; set; }
        
        // Status information
        public string? BreedingStatus { get; set; }
        public string? AvailabilityStatus { get; set; }
        public bool IsActive { get; set; }
        public bool IsPublic { get; set; }
        public bool IsFeatured { get; set; }
        public int DisplayOrder { get; set; }
        
        // Pedigree information
        public string? FatherId { get; set; }
        public string? MotherId { get; set; }
        public string? FatherName { get; set; }
        public string? MotherName { get; set; }
        
        // Registration information
        public string? ChampionTitles { get; set; }
        public string? RegistrationNumber { get; set; }
        public string? RegisteredName { get; set; }
        public string? RegistrationBody { get; set; }
        
        // Physical characteristics
        public string? Color { get; set; }
        public string? Pattern { get; set; }
        public string? Markings { get; set; }
        public string? EyeColor { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Height { get; set; }
        
        // Health information
        public string? HealthRecords { get; set; }
        public string? GeneticTesting { get; set; }
        public string? Vaccinations { get; set; }
        public DateTime? LastHealthCheck { get; set; }
        public string? VeterinarianContact { get; set; }
        
        // Personality and behavior
        public string? PersonalityDescription { get; set; }
        public string? BehaviorTraits { get; set; }
        public string? SpecialNeeds { get; set; }
        
        // Breeding information
        public int? TotalLitters { get; set; }
        public DateTime? FirstBreedingDate { get; set; }
        public DateTime? LastBreedingDate { get; set; }
        public string? BreedingNotes { get; set; }
        
        // Location and care
        public string? CurrentLocation { get; set; }
        public string? CaregiverName { get; set; }
        public string? CaregiverContact { get; set; }
        
        // Profile image
        public string? ProfileImageUrl { get; set; }
        public string? ProfileImageStorageKey { get; set; }
        public string? ProfileImageStorageProvider { get; set; }
        
        // Financial information
        public decimal? PurchasePrice { get; set; }
        public decimal? CurrentValue { get; set; }
        public decimal? StudFee { get; set; }
        public decimal? KittenPrice { get; set; }
        public string? PricingNotes { get; set; }
        
        // Metadata
        public string? Notes { get; set; }
        public string? Tags { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ModifiedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? ModifiedBy { get; set; }
        
        // Gallery information
        public int GalleryImageCount { get; set; }
        public List<GalleryImageDto>? GalleryImages { get; set; }
        public string? FeaturedImageUrl { get; set; }
        
        // Computed properties
        public string CurrentAge { get; set; } = string.Empty;
        public bool IsKitten { get; set; }
        public bool IsBreedingAge { get; set; }
        public string BreedingStatusSummary { get; set; } = string.Empty;
        public decimal? EstimatedValue { get; set; }
        public string DisplayName => !string.IsNullOrEmpty(RegisteredName) ? RegisteredName : CatName;
        public string FormattedWeight => Weight.HasValue ? $"{Weight:F1} kg" : "Unknown";
        public string FormattedAge => !string.IsNullOrEmpty(CurrentAge) ? CurrentAge : "Unknown";
        public List<string> TagsList => GetTagsList();
        public bool HasProfileImage => !string.IsNullOrEmpty(ProfileImageUrl);
        public bool HasGalleryImages => GalleryImageCount > 0;
        public bool HasChampionTitles => !string.IsNullOrEmpty(ChampionTitles);
        public bool HasPedigree => !string.IsNullOrEmpty(FatherId) || !string.IsNullOrEmpty(MotherId);
        
        /// <summary>
        /// Create DTO from entity
        /// </summary>
        public static CatProfileDto FromEntity(CatProfile entity)
        {
            return new CatProfileDto
            {
                Id = entity.Id,
                CatId = entity.CatId,
                CatName = entity.CatName,
                Breed = entity.Breed,
                Bloodline = entity.Bloodline,
                Gender = entity.Gender,
                BirthDate = entity.BirthDate,
                BreedingStatus = entity.BreedingStatus,
                AvailabilityStatus = entity.AvailabilityStatus,
                IsActive = entity.IsActive,
                IsPublic = entity.IsPublic,
                IsFeatured = entity.IsFeatured,
                DisplayOrder = entity.DisplayOrder,
                FatherId = entity.FatherId,
                MotherId = entity.MotherId,
                FatherName = entity.Father?.CatName,
                MotherName = entity.Mother?.CatName,
                ChampionTitles = entity.ChampionTitles,
                RegistrationNumber = entity.RegistrationNumber,
                RegisteredName = entity.RegisteredName,
                RegistrationBody = entity.RegistrationBody,
                Color = entity.Color,
                Pattern = entity.Pattern,
                Markings = entity.Markings,
                EyeColor = entity.EyeColor,
                Weight = entity.Weight,
                Length = entity.Length,
                Height = entity.Height,
                HealthRecords = entity.HealthRecords,
                GeneticTesting = entity.GeneticTesting,
                Vaccinations = entity.Vaccinations,
                LastHealthCheck = entity.LastHealthCheck,
                VeterinarianContact = entity.VeterinarianContact,
                PersonalityDescription = entity.PersonalityDescription,
                BehaviorTraits = entity.BehaviorTraits,
                SpecialNeeds = entity.SpecialNeeds,
                TotalLitters = entity.TotalLitters,
                FirstBreedingDate = entity.FirstBreedingDate,
                LastBreedingDate = entity.LastBreedingDate,
                BreedingNotes = entity.BreedingNotes,
                CurrentLocation = entity.CurrentLocation,
                CaregiverName = entity.CaregiverName,
                CaregiverContact = entity.CaregiverContact,
                ProfileImageUrl = entity.ProfileImageUrl,
                ProfileImageStorageKey = entity.ProfileImageStorageKey,
                ProfileImageStorageProvider = entity.ProfileImageStorageProvider,
                PurchasePrice = entity.PurchasePrice,
                CurrentValue = entity.CurrentValue,
                StudFee = entity.StudFee,
                KittenPrice = entity.KittenPrice,
                PricingNotes = entity.PricingNotes,
                Notes = entity.Notes,
                Tags = entity.Tags,
                CreatedAt = entity.CreatedAt,
                ModifiedAt = entity.ModifiedAt,
                CreatedBy = entity.CreatedBy,
                ModifiedBy = entity.ModifiedBy,
                GalleryImageCount = entity.GalleryImages?.Count ?? 0,
                GalleryImages = entity.GalleryImages?.Select(GalleryImageDto.FromEntity).ToList(),
                FeaturedImageUrl = entity.GalleryImages?.FirstOrDefault()?.PublicUrl,
                CurrentAge = entity.CurrentAge,
                IsKitten = entity.IsKitten(),
                IsBreedingAge = entity.IsBreedingAge(),
                BreedingStatusSummary = entity.GetBreedingStatusSummary(),
                EstimatedValue = entity.GetEstimatedValue()
            };
        }
        
        /// <summary>
        /// Create lightweight DTO for list views
        /// </summary>
        public static CatProfileDto CreateLightweight(CatProfile entity)
        {
            return new CatProfileDto
            {
                Id = entity.Id,
                CatId = entity.CatId,
                CatName = entity.CatName,
                Breed = entity.Breed,
                Bloodline = entity.Bloodline,
                Gender = entity.Gender,
                BirthDate = entity.BirthDate,
                BreedingStatus = entity.BreedingStatus,
                AvailabilityStatus = entity.AvailabilityStatus,
                IsActive = entity.IsActive,
                IsPublic = entity.IsPublic,
                IsFeatured = entity.IsFeatured,
                DisplayOrder = entity.DisplayOrder,
                RegisteredName = entity.RegisteredName,
                ChampionTitles = entity.ChampionTitles,
                Color = entity.Color,
                ProfileImageUrl = entity.ProfileImageUrl,
                GalleryImageCount = entity.GalleryImages?.Count ?? 0,
                FeaturedImageUrl = entity.GalleryImages?.FirstOrDefault()?.PublicUrl,
                CurrentAge = entity.CurrentAge,
                IsKitten = entity.IsKitten(),
                IsBreedingAge = entity.IsBreedingAge(),
                BreedingStatusSummary = entity.GetBreedingStatusSummary(),
                EstimatedValue = entity.GetEstimatedValue()
            };
        }
        
        /// <summary>
        /// Create summary DTO for card views
        /// </summary>
        public static CatProfileDto CreateSummary(CatProfile entity)
        {
            return new CatProfileDto
            {
                Id = entity.Id,
                CatId = entity.CatId,
                CatName = entity.CatName,
                Breed = entity.Breed,
                Gender = entity.Gender,
                BirthDate = entity.BirthDate,
                BreedingStatus = entity.BreedingStatus,
                AvailabilityStatus = entity.AvailabilityStatus,
                RegisteredName = entity.RegisteredName,
                Color = entity.Color,
                ProfileImageUrl = entity.ProfileImageUrl,
                FeaturedImageUrl = entity.GalleryImages?.FirstOrDefault()?.PublicUrl,
                CurrentAge = entity.CurrentAge,
                IsKitten = entity.IsKitten(),
                IsBreedingAge = entity.IsBreedingAge(),
                BreedingStatusSummary = entity.GetBreedingStatusSummary(),
                IsFeatured = entity.IsFeatured
            };
        }
        
        /// <summary>
        /// Get tags as a list
        /// </summary>
        public List<string> GetTagsList()
        {
            if (string.IsNullOrEmpty(Tags))
                return new List<string>();
            
            return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(tag => tag.Trim())
                      .Where(tag => !string.IsNullOrEmpty(tag))
                      .ToList();
        }
        
        /// <summary>
        /// Get formatted birth date
        /// </summary>
        public string GetFormattedBirthDate()
        {
            return BirthDate?.ToString("MMMM dd, yyyy") ?? "Unknown";
        }
        
        /// <summary>
        /// Get next breeding date estimate
        /// </summary>
        public DateTime? GetNextBreedingEstimate()
        {
            if (Gender != "F" || BreedingStatus != "Available")
                return null;
            
            // Estimate next breeding opportunity (every 6 months for queens)
            var lastBreeding = LastBreedingDate ?? FirstBreedingDate;
            if (lastBreeding.HasValue)
            {
                return lastBreeding.Value.AddMonths(6);
            }
            
            return null;
        }
        
        /// <summary>
        /// Get breeding availability status
        /// </summary>
        public string GetBreedingAvailability()
        {
            if (!IsBreedingAge)
                return "Not yet breeding age";
            
            if (BreedingStatus == "Retired")
                return "Retired from breeding";
            
            if (BreedingStatus == "Breeding")
                return "Currently breeding";
            
            if (AvailabilityStatus == "Not-For-Sale")
                return "Not available for breeding";
            
            var nextBreeding = GetNextBreedingEstimate();
            if (nextBreeding.HasValue)
            {
                var monthsUntil = (int)((nextBreeding.Value - DateTime.Now).TotalDays / 30.44);
                if (monthsUntil > 0)
                    return $"Next breeding availability: {monthsUntil} months";
            }
            
            return "Available for breeding";
        }
        
        /// <summary>
        /// Get health status summary
        /// </summary>
        public string GetHealthStatusSummary()
        {
            if (LastHealthCheck.HasValue)
            {
                var daysSinceCheck = (DateTime.Now - LastHealthCheck.Value).TotalDays;
                if (daysSinceCheck < 90)
                    return "Recent health check";
                else if (daysSinceCheck < 365)
                    return "Health check due soon";
                else
                    return "Health check overdue";
            }
            
            return "No health records";
        }
        
        /// <summary>
        /// Get pricing information
        /// </summary>
        public string GetPricingInfo()
        {
            if (AvailabilityStatus == "Not-For-Sale")
                return "Not for sale";
            
            if (BreedingStatus == "Available" && Gender == "M" && StudFee.HasValue)
                return $"Stud fee: ${StudFee:F0}";
            
            if (IsKitten && KittenPrice.HasValue)
                return $"Kitten price: ${KittenPrice:F0}";
            
            if (CurrentValue.HasValue)
                return $"Estimated value: ${CurrentValue:F0}";
            
            if (EstimatedValue.HasValue)
                return $"Estimated value: ${EstimatedValue:F0}";
            
            return "Price on inquiry";
        }
        
        /// <summary>
        /// Get pedigree summary
        /// </summary>
        public string GetPedigreeSummary()
        {
            var parts = new List<string>();
            
            if (!string.IsNullOrEmpty(FatherName))
                parts.Add($"Sire: {FatherName}");
            
            if (!string.IsNullOrEmpty(MotherName))
                parts.Add($"Dam: {MotherName}");
            
            if (!string.IsNullOrEmpty(Bloodline))
                parts.Add($"Line: {Bloodline}");
            
            return parts.Any() ? string.Join(" | ", parts) : "Pedigree information not available";
        }
        
        /// <summary>
        /// Get physical description
        /// </summary>
        public string GetPhysicalDescription()
        {
            var parts = new List<string>();
            
            if (!string.IsNullOrEmpty(Color))
                parts.Add(Color);
            
            if (!string.IsNullOrEmpty(Pattern))
                parts.Add(Pattern);
            
            if (!string.IsNullOrEmpty(EyeColor))
                parts.Add($"{EyeColor} eyes");
            
            if (Weight.HasValue)
                parts.Add($"{Weight:F1} kg");
            
            return parts.Any() ? string.Join(", ", parts) : "Physical details not recorded";
        }
        
        /// <summary>
        /// Check if profile matches search criteria
        /// </summary>
        public bool MatchesSearch(string searchTerm)
        {
            if (string.IsNullOrEmpty(searchTerm))
                return true;
            
            var term = searchTerm.ToLowerInvariant();
            
            return (CatName?.ToLowerInvariant().Contains(term) == true) ||
                   (RegisteredName?.ToLowerInvariant().Contains(term) == true) ||
                   (Breed?.ToLowerInvariant().Contains(term) == true) ||
                   (Bloodline?.ToLowerInvariant().Contains(term) == true) ||
                   (Color?.ToLowerInvariant().Contains(term) == true) ||
                   (ChampionTitles?.ToLowerInvariant().Contains(term) == true) ||
                   (Tags?.ToLowerInvariant().Contains(term) == true) ||
                   (CatId?.ToLowerInvariant().Contains(term) == true);
        }
        
        /// <summary>
        /// Get status badge information
        /// </summary>
        public StatusBadgeInfo GetStatusBadge()
        {
            if (IsFeatured)
                return new StatusBadgeInfo("Featured", "primary");
            
            if (AvailabilityStatus == "Available")
                return new StatusBadgeInfo("Available", "success");
            
            if (AvailabilityStatus == "Reserved")
                return new StatusBadgeInfo("Reserved", "warning");
            
            if (AvailabilityStatus == "Sold")
                return new StatusBadgeInfo("Sold", "secondary");
            
            if (BreedingStatus == "Breeding")
                return new StatusBadgeInfo("Breeding", "info");
            
            if (BreedingStatus == "Retired")
                return new StatusBadgeInfo("Retired", "secondary");
            
            if (IsKitten)
                return new StatusBadgeInfo("Kitten", "info");
            
            return new StatusBadgeInfo("Available", "success");
        }
    }
    
    /// <summary>
    /// Status badge information for UI display
    /// </summary>
    public class StatusBadgeInfo
    {
        public string Text { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // primary, secondary, success, warning, info, danger
        
        public StatusBadgeInfo(string text, string type)
        {
            Text = text;
            Type = type;
        }
    }
}