using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace YendorCats.API.Models
{
    /// <summary>
    /// B2 synchronization log for tracking all storage operations and migrations
    /// Critical for audit trails and data integrity verification
    /// </summary>
    [Table("B2SyncLogs")]
    public class B2SyncLog
    {
        [Key]
        public long Id { get; set; }
        
        // Operation identification
        [Required]
        [MaxLength(500)]
        public string StorageKey { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(50)]
        public string Operation { get; set; } = string.Empty; // INSERT, UPDATE, DELETE, VERIFY, MIGRATE
        
        [Required]
        [MaxLength(50)]
        public string Status { get; set; } = string.Empty; // SUCCESS, FAILED, PENDING, RETRY
        
        // Source and destination information
        [MaxLength(10)]
        public string? SourceProvider { get; set; } // S3, B2, DATABASE
        
        [MaxLength(10)]
        public string? DestinationProvider { get; set; } // S3, B2, DATABASE
        
        [MaxLength(100)]
        public string? SourceBucket { get; set; }
        
        [MaxLength(100)]
        public string? DestinationBucket { get; set; }
        
        [MaxLength(500)]
        public string? SourceKey { get; set; }
        
        [MaxLength(500)]
        public string? DestinationKey { get; set; }
        
        // File information
        [MaxLength(255)]
        public string? OriginalFileName { get; set; }
        
        public long? FileSize { get; set; }
        
        [MaxLength(100)]
        public string? ContentType { get; set; }
        
        [MaxLength(100)]
        public string? FileHash { get; set; } // MD5 or SHA256 hash for integrity
        
        // Metadata information
        [MaxLength(1000)]
        public string? MetadataJson { get; set; } // JSON serialized metadata
        
        public int? MetadataFieldCount { get; set; }
        
        // Timing and performance
        public DateTime SyncedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        public int? DurationMs { get; set; }
        
        // Retry and error handling
        public int RetryCount { get; set; } = 0;
        
        public int MaxRetries { get; set; } = 3;
        
        [MaxLength(2000)]
        public string? ErrorMessage { get; set; }
        
        [MaxLength(500)]
        public string? ErrorCode { get; set; }
        
        public DateTime? NextRetryAt { get; set; }
        
        // User and session tracking
        [MaxLength(100)]
        public string? UserId { get; set; }
        
        [MaxLength(100)]
        public string? SessionId { get; set; }
        
        [MaxLength(200)]
        public string? UserAgent { get; set; }
        
        [MaxLength(50)]
        public string? IpAddress { get; set; }
        
        // Batch and migration tracking
        [MaxLength(100)]
        public string? BatchId { get; set; }
        
        [MaxLength(100)]
        public string? MigrationId { get; set; }
        
        public int? BatchSequence { get; set; }
        
        public int? TotalBatchItems { get; set; }
        
        // Related entity information
        public long? CatGalleryImageId { get; set; }
        
        public int? CatProfileId { get; set; }
        
        [MaxLength(50)]
        public string? CatId { get; set; }
        
        [MaxLength(50)]
        public string? Category { get; set; }
        
        // Verification and validation
        public bool IsVerified { get; set; } = false;
        
        public DateTime? VerifiedAt { get; set; }
        
        [MaxLength(100)]
        public string? VerifiedBy { get; set; }
        
        public bool RequiresManualReview { get; set; } = false;
        
        [MaxLength(1000)]
        public string? ReviewNotes { get; set; }
        
        // Additional context
        [MaxLength(500)]
        public string? Context { get; set; } // Additional context about the operation
        
        [MaxLength(1000)]
        public string? Notes { get; set; }
        
        // Computed properties
        [NotMapped]
        public bool IsCompleted => Status == "SUCCESS" || Status == "FAILED";
        
        [NotMapped]
        public bool NeedsRetry => Status == "FAILED" && RetryCount < MaxRetries && 
                                 (NextRetryAt == null || NextRetryAt <= DateTime.UtcNow);
        
        [NotMapped]
        public TimeSpan? Duration => StartedAt.HasValue && CompletedAt.HasValue 
            ? CompletedAt - StartedAt : null;
        
        [NotMapped]
        public string OperationSummary => $"{Operation} {StorageKey} from {SourceProvider} to {DestinationProvider}";
        
        // Navigation properties
        [ForeignKey(nameof(CatGalleryImageId))]
        public virtual CatGalleryImage? CatGalleryImage { get; set; }

        [ForeignKey(nameof(CatProfileId))]
        public virtual CatProfile? CatProfile { get; set; }

        // Additional Properties for Compatibility
        /// <summary>
        /// B2 file ID for destination files
        /// </summary>
        [MaxLength(100)]
        public string? DestinationFileId { get; set; }

        /// <summary>
        /// Created timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Updated timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Additional Properties Expected by AppDbContext
        /// <summary>
        /// File name - alias for OriginalFileName
        /// </summary>
        [NotMapped]
        public string? FileName
        {
            get => OriginalFileName;
            set => OriginalFileName = value;
        }

        /// <summary>
        /// S3 key - alias for SourceKey when source is S3
        /// </summary>
        [NotMapped]
        public string? S3Key
        {
            get => SourceProvider == "S3" ? SourceKey : null;
            set
            {
                if (SourceProvider == "S3" || string.IsNullOrEmpty(SourceProvider))
                {
                    SourceKey = value;
                    if (string.IsNullOrEmpty(SourceProvider))
                        SourceProvider = "S3";
                }
            }
        }

        /// <summary>
        /// S3 bucket - alias for SourceBucket when source is S3
        /// </summary>
        [NotMapped]
        public string? S3Bucket
        {
            get => SourceProvider == "S3" ? SourceBucket : null;
            set
            {
                if (SourceProvider == "S3" || string.IsNullOrEmpty(SourceProvider))
                {
                    SourceBucket = value;
                    if (string.IsNullOrEmpty(SourceProvider))
                        SourceProvider = "S3";
                }
            }
        }

        /// <summary>
        /// B2 bucket - alias for DestinationBucket when destination is B2
        /// </summary>
        [NotMapped]
        public string? B2Bucket
        {
            get => DestinationProvider == "B2" ? DestinationBucket : null;
            set
            {
                if (DestinationProvider == "B2" || string.IsNullOrEmpty(DestinationProvider))
                {
                    DestinationBucket = value;
                    if (string.IsNullOrEmpty(DestinationProvider))
                        DestinationProvider = "B2";
                }
            }
        }

        /// <summary>
        /// B2 file ID - alias for DestinationFileId
        /// </summary>
        [NotMapped]
        public string? B2FileId
        {
            get => DestinationFileId;
            set => DestinationFileId = value;
        }

        /// <summary>
        /// File checksum - alias for FileHash
        /// </summary>
        [NotMapped]
        public string? Checksum
        {
            get => FileHash;
            set => FileHash = value;
        }

        /// <summary>
        /// Processing time in milliseconds - alias for DurationMs
        /// </summary>
        [NotMapped]
        public int? ProcessingTimeMs
        {
            get => DurationMs;
            set => DurationMs = value;
        }

        /// <summary>
        /// Error details - alias for ErrorMessage
        /// </summary>
        [NotMapped]
        public string? ErrorDetails
        {
            get => ErrorMessage;
            set => ErrorMessage = value;
        }

        // Legacy/Compatibility Properties (for backward compatibility with existing code)
        /// <summary>
        /// Source storage provider - alias for SourceProvider
        /// </summary>
        [NotMapped]
        public string? SourceStorageProvider
        {
            get => SourceProvider;
            set => SourceProvider = value;
        }

        /// <summary>
        /// Destination storage provider - alias for DestinationProvider
        /// </summary>
        [NotMapped]
        public string? DestinationStorageProvider
        {
            get => DestinationProvider;
            set => DestinationProvider = value;
        }

        /// <summary>
        /// B2 key - alias for DestinationKey when destination is B2
        /// </summary>
        [NotMapped]
        public string? B2Key
        {
            get => DestinationProvider == "B2" ? DestinationKey : null;
            set
            {
                if (DestinationProvider == "B2" || string.IsNullOrEmpty(DestinationProvider))
                {
                    DestinationKey = value;
                    if (string.IsNullOrEmpty(DestinationProvider))
                        DestinationProvider = "B2";
                }
            }
        }

        /// <summary>
        /// Start time - alias for StartedAt
        /// </summary>
        [NotMapped]
        public DateTime? StartTime
        {
            get => StartedAt;
            set => StartedAt = value;
        }

        /// <summary>
        /// End time - alias for CompletedAt
        /// </summary>
        [NotMapped]
        public DateTime? EndTime
        {
            get => CompletedAt;
            set => CompletedAt = value;
        }

        /// <summary>
        /// Attempt count - alias for RetryCount + 1 (since RetryCount is 0-based)
        /// </summary>
        [NotMapped]
        public int AttemptCount
        {
            get => RetryCount + 1;
            set => RetryCount = Math.Max(0, value - 1);
        }
        
        // Factory methods for common operations
        public static B2SyncLog CreateMigrationLog(string storageKey, string sourceProvider, 
            string destinationProvider, string migrationId, string? batchId = null)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "MIGRATE",
                Status = "PENDING",
                SourceProvider = sourceProvider,
                DestinationProvider = destinationProvider,
                MigrationId = migrationId,
                BatchId = batchId,
                StartedAt = DateTime.UtcNow
            };
        }
        
        public static B2SyncLog CreateUploadLog(string storageKey, string destinationProvider, 
            string fileName, long fileSize, string contentType, string? userId = null)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "INSERT",
                Status = "PENDING",
                DestinationProvider = destinationProvider,
                OriginalFileName = fileName,
                FileSize = fileSize,
                ContentType = contentType,
                UserId = userId,
                StartedAt = DateTime.UtcNow
            };
        }
        
        public static B2SyncLog CreateDeleteLog(string storageKey, string sourceProvider, 
            string? userId = null)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "DELETE",
                Status = "PENDING",
                SourceProvider = sourceProvider,
                UserId = userId,
                StartedAt = DateTime.UtcNow
            };
        }
        
        public static B2SyncLog CreateVerificationLog(string storageKey, string provider)
        {
            return new B2SyncLog
            {
                StorageKey = storageKey,
                Operation = "VERIFY",
                Status = "PENDING",
                SourceProvider = provider,
                StartedAt = DateTime.UtcNow
            };
        }
        
        // Instance methods
        public void MarkAsStarted()
        {
            StartedAt = DateTime.UtcNow;
            Status = "PENDING";
        }
        
        public void MarkAsCompleted(bool success = true, string? errorMessage = null)
        {
            CompletedAt = DateTime.UtcNow;
            Status = success ? "SUCCESS" : "FAILED";
            
            if (StartedAt.HasValue)
            {
                DurationMs = (int)(CompletedAt.Value - StartedAt.Value).TotalMilliseconds;
            }
            
            if (!success && !string.IsNullOrEmpty(errorMessage))
            {
                ErrorMessage = errorMessage;
            }
        }
        
        public void MarkAsRetry(string? errorMessage = null, int? retryDelayMinutes = null)
        {
            RetryCount++;
            Status = "RETRY";
            
            if (!string.IsNullOrEmpty(errorMessage))
            {
                ErrorMessage = errorMessage;
            }
            
            // Calculate next retry time with exponential backoff
            var baseDelay = retryDelayMinutes ?? (int)Math.Pow(2, RetryCount); // 2, 4, 8 minutes
            NextRetryAt = DateTime.UtcNow.AddMinutes(baseDelay);
        }
        
        public void MarkAsVerified(string? verifiedBy = null)
        {
            IsVerified = true;
            VerifiedAt = DateTime.UtcNow;
            VerifiedBy = verifiedBy;
        }
        
        public void AddContext(string contextInfo)
        {
            if (string.IsNullOrEmpty(Context))
                Context = contextInfo;
            else
                Context += $" | {contextInfo}";
        }
        
        public void AddNote(string note)
        {
            if (string.IsNullOrEmpty(Notes))
                Notes = note;
            else
                Notes += $"\n{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}: {note}";
        }
        
        public void SetFileHash(string hash)
        {
            FileHash = hash;
        }
        
        public void SetMetadata(string metadataJson, int fieldCount)
        {
            MetadataJson = metadataJson;
            MetadataFieldCount = fieldCount;
        }
        
        public void SetBatchInfo(string batchId, int sequence, int totalItems)
        {
            BatchId = batchId;
            BatchSequence = sequence;
            TotalBatchItems = totalItems;
        }
        
        public void SetUserInfo(string? userId, string? sessionId, string? userAgent, string? ipAddress)
        {
            UserId = userId;
            SessionId = sessionId;
            UserAgent = userAgent;
            IpAddress = ipAddress;
        }
        
        public void LinkToGalleryImage(long galleryImageId)
        {
            CatGalleryImageId = galleryImageId;
        }
        
        public void LinkToCatProfile(int catProfileId)
        {
            CatProfileId = catProfileId;
        }
        
        // Query helper methods
        public bool IsOlderThan(TimeSpan timeSpan)
        {
            return SyncedAt < DateTime.UtcNow - timeSpan;
        }
        
        public bool IsInProgress()
        {
            return Status == "PENDING" && StartedAt.HasValue && !CompletedAt.HasValue;
        }
        
        public bool IsStuck()
        {
            // Consider a log entry stuck if it's been pending for more than 30 minutes
            return IsInProgress() && (DateTime.UtcNow - StartedAt.Value).TotalMinutes > 30;
        }
    }
}