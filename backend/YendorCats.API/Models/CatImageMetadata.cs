using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Represents metadata for a cat image stored in S3
    /// </summary>
    public class CatImageMetadata
    {
        #region File Metadata (System Generated)
        
        /// <summary>
        /// The date the image was uploaded (set automatically)
        /// </summary>
        [Required]
        public DateTime? DateUploaded { get; set; }
        
        /// <summary>
        /// The file format/extension
        /// </summary>
        [Required]
        public string FileFormat { get; set; } = string.Empty;
        
        /// <summary>
        /// The content type (MIME type) of the image
        /// </summary>
        [Required]
        public string ContentType { get; set; } = string.Empty;
        
        /// <summary>
        /// The size of the file in bytes
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// The width of the image in pixels
        /// </summary>
        public int? Width { get; set; }
        
        /// <summary>
        /// The height of the image in pixels
        /// </summary>
        public int? Height { get; set; }
        
        /// <summary>
        /// The IP address of the uploader
        /// </summary>
        public string? UploaderIp { get; set; }
        
        /// <summary>
        /// The user agent of the uploader
        /// </summary>
        public string? UploaderUserAgent { get; set; }
        
        /// <summary>
        /// A unique identifier for the upload session
        /// </summary>
        public string? UploadSessionId { get; set; }
        
        #endregion
        
        #region User Inputted Data
        
        /// <summary>
        /// The name of the cat in the picture
        /// </summary>
        [Required]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Description of the cat or image
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// The age of the cat (in years.months format, e.g., "2.5")
        /// </summary>
        public string? Age { get; set; }
        
        /// <summary>
        /// The gender of the cat (M/F)
        /// </summary>
        [Required]
        public string Gender { get; set; } = string.Empty;
        
        /// <summary>
        /// The bloodline of the cat
        /// </summary>
        public string? Bloodline { get; set; }
        
        /// <summary>
        /// The breed of the cat
        /// </summary>
        public string? Breed { get; set; }
        
        /// <summary>
        /// The hair/fur color of the cat
        /// </summary>
        public string? HairColor { get; set; }
        
        /// <summary>
        /// The date the photo was taken
        /// </summary>
        public DateTime? DateTaken { get; set; }
        
        /// <summary>
        /// Description of the cat's personality
        /// </summary>
        public string? Personality { get; set; }
        
        /// <summary>
        /// The category the cat belongs to (e.g., "studs", "queens", "kittens")
        /// </summary>
        public string? Category { get; set; }
        
        /// <summary>
        /// Tags for filtering and searching
        /// </summary>
        public string? Tags { get; set; }
        
        /// <summary>
        /// The parent/mother of the cat
        /// </summary>
        public string? Mother { get; set; }
        
        /// <summary>
        /// The parent/father of the cat
        /// </summary>
        public string? Father { get; set; }
        
        #endregion
        
        #region Database Relationship Fields
        
        /// <summary>
        /// Database Cat ID for bidirectional linking
        /// </summary>
        public int? CatId { get; set; }
        
        /// <summary>
        /// Database ID of the mother cat
        /// </summary>
        public int? MotherCatId { get; set; }
        
        /// <summary>
        /// Database ID of the father cat
        /// </summary>
        public int? FatherCatId { get; set; }
        
        /// <summary>
        /// Official registered name from pedigree
        /// </summary>
        public string? RegisteredName { get; set; }
        
        /// <summary>
        /// Registration number from pedigree papers
        /// </summary>
        public string? RegistrationNumber { get; set; }
        
        /// <summary>
        /// Breeding status (available-kitten, breeding-queen, stud, retired)
        /// </summary>
        public string? BreedingStatus { get; set; }
        
        /// <summary>
        /// Availability status (available, reserved, sold, not-for-sale)
        /// </summary>
        public string? AvailabilityStatus { get; set; }
        
        /// <summary>
        /// Type of photo (profile, action, family, breeding, growth)
        /// </summary>
        public string? PhotoType { get; set; }
        
        /// <summary>
        /// Age when photo was taken
        /// </summary>
        public string? AgeAtPhoto { get; set; }
        
        /// <summary>
        /// Champion titles and achievements
        /// </summary>
        public string? ChampionTitles { get; set; }
        
        /// <summary>
        /// Generation level in pedigree (1=parent, 2=grandparent, etc.)
        /// </summary>
        public string? GenerationLevel { get; set; }
        
        #endregion
        
        /// <summary>
        /// Additional metadata as key-value pairs
        /// </summary>
        public Dictionary<string, string> AdditionalMetadata { get; set; } = new();
        
        /// <summary>
        /// Convert the metadata to a dictionary for S3 storage
        /// </summary>
        /// <returns>Dictionary containing all metadata fields</returns>
        public Dictionary<string, string> ToS3Metadata()
        {
            var metadata = new Dictionary<string, string>
            {
                // Required fields - using hyphenated format for consistency
                ["cat-name"] = Name ?? "",
                ["gender"] = Gender ?? "",
                ["date-uploaded"] = DateUploaded?.ToString("yyyy-MM-ddTHH:mm:ssZ") ?? "",
                ["file-format"] = FileFormat ?? "",
                ["content-type"] = ContentType ?? ""
            };
            
            // Optional fields - always add as empty string if null for consistency
            metadata["description"] = Description ?? "";
            metadata["cat-age"] = Age ?? "";
            metadata["bloodline"] = Bloodline ?? "";
            metadata["breed"] = Breed ?? "";
            metadata["hair-color"] = HairColor ?? "";
            metadata["date-taken"] = DateTaken?.ToString("yyyy-MM-dd") ?? "";
            metadata["personality"] = Personality ?? "";
            metadata["category"] = Category ?? "";
            metadata["tags"] = Tags ?? "";
            metadata["mother"] = Mother ?? "";
            metadata["father"] = Father ?? "";
            
            // Database relationship fields
            metadata["cat-id"] = CatId?.ToString() ?? "";
            metadata["mother-cat-id"] = MotherCatId?.ToString() ?? "";
            metadata["father-cat-id"] = FatherCatId?.ToString() ?? "";
            metadata["registered-name"] = RegisteredName ?? "";
            metadata["registration-number"] = RegistrationNumber ?? "";
            metadata["breeding-status"] = BreedingStatus ?? "";
            metadata["availability-status"] = AvailabilityStatus ?? "";
            metadata["photo-type"] = PhotoType ?? "";
            metadata["age-at-photo"] = AgeAtPhoto ?? "";
            metadata["champion-titles"] = ChampionTitles ?? "";
            metadata["generation-level"] = GenerationLevel ?? "";
            
            // System metadata
            metadata["file-size"] = FileSize > 0 ? FileSize.ToString() : "";
            metadata["width"] = Width?.ToString() ?? "";
            metadata["height"] = Height?.ToString() ?? "";
            metadata["uploader-ip"] = UploaderIp ?? "";
            metadata["uploader-user-agent"] = UploaderUserAgent ?? "";
            metadata["upload-session-id"] = UploadSessionId ?? "";
            
            // Add any additional metadata
            foreach (var kvp in AdditionalMetadata)
            {
                if (!metadata.ContainsKey(kvp.Key))
                {
                    metadata[kvp.Key] = kvp.Value;
                }
            }
            
            return metadata;
        }
        
        /// <summary>
        /// Create a CatImageMetadata object from S3 metadata
        /// </summary>
        /// <param name="metadata">The S3 object metadata</param>
        /// <returns>A CatImageMetadata object</returns>
        public static CatImageMetadata FromS3Metadata(Dictionary<string, string> metadata)
        {
            var result = new CatImageMetadata();
            
            // Handle null dictionary
            if (metadata == null)
            {
                // For null input, set expected defaults
                result.Name = "";
                result.Age = "";
                result.Gender = "";
                result.Description = "";
                return result;
            }
            
            // Extract metadata fields using hyphenated format
            // Always set to empty string if missing or empty, to match test expectations
            result.Name = metadata.TryGetValue("cat-name", out var name) && !string.IsNullOrEmpty(name) ? name : "";
            result.Description = metadata.TryGetValue("description", out var description) && !string.IsNullOrEmpty(description) ? description : "";
            result.Age = metadata.TryGetValue("cat-age", out var age) && !string.IsNullOrEmpty(age) ? age : "";
            result.Gender = metadata.TryGetValue("gender", out var gender) && !string.IsNullOrEmpty(gender) ? gender : "";
            result.Bloodline = metadata.TryGetValue("bloodline", out var bloodline) && !string.IsNullOrEmpty(bloodline) ? bloodline : null;
            result.Breed = metadata.TryGetValue("breed", out var breed) && !string.IsNullOrEmpty(breed) ? breed : null;
            result.HairColor = metadata.TryGetValue("hair-color", out var hairColor) && !string.IsNullOrEmpty(hairColor) ? hairColor : null;
            result.Personality = metadata.TryGetValue("personality", out var personality) && !string.IsNullOrEmpty(personality) ? personality : null;
            result.Category = metadata.TryGetValue("category", out var category) && !string.IsNullOrEmpty(category) ? category : null;
            result.Tags = metadata.TryGetValue("tags", out var tags) && !string.IsNullOrEmpty(tags) ? tags : null;
            result.Mother = metadata.TryGetValue("mother", out var mother) && !string.IsNullOrEmpty(mother) ? mother : null;
            result.Father = metadata.TryGetValue("father", out var father) && !string.IsNullOrEmpty(father) ? father : null;
            
            // Parse database relationship fields
            if (metadata.TryGetValue("cat-id", out var catIdStr) &&
                !string.IsNullOrEmpty(catIdStr) && int.TryParse(catIdStr, out var catId))
            {
                result.CatId = catId;
            }
            
            if (metadata.TryGetValue("mother-cat-id", out var motherCatIdStr) &&
                !string.IsNullOrEmpty(motherCatIdStr) && int.TryParse(motherCatIdStr, out var motherCatId))
            {
                result.MotherCatId = motherCatId;
            }
            
            if (metadata.TryGetValue("father-cat-id", out var fatherCatIdStr) &&
                !string.IsNullOrEmpty(fatherCatIdStr) && int.TryParse(fatherCatIdStr, out var fatherCatId))
            {
                result.FatherCatId = fatherCatId;
            }
            
            if (metadata.TryGetValue("registered-name", out var registeredName) && !string.IsNullOrEmpty(registeredName)) result.RegisteredName = registeredName;
            if (metadata.TryGetValue("registration-number", out var registrationNumber) && !string.IsNullOrEmpty(registrationNumber)) result.RegistrationNumber = registrationNumber;
            if (metadata.TryGetValue("breeding-status", out var breedingStatus) && !string.IsNullOrEmpty(breedingStatus)) result.BreedingStatus = breedingStatus;
            if (metadata.TryGetValue("availability-status", out var availabilityStatus) && !string.IsNullOrEmpty(availabilityStatus)) result.AvailabilityStatus = availabilityStatus;
            if (metadata.TryGetValue("photo-type", out var photoType) && !string.IsNullOrEmpty(photoType)) result.PhotoType = photoType;
            if (metadata.TryGetValue("age-at-photo", out var ageAtPhoto) && !string.IsNullOrEmpty(ageAtPhoto)) result.AgeAtPhoto = ageAtPhoto;
            if (metadata.TryGetValue("champion-titles", out var championTitles) && !string.IsNullOrEmpty(championTitles)) result.ChampionTitles = championTitles;
            if (metadata.TryGetValue("generation-level", out var generationLevel) && !string.IsNullOrEmpty(generationLevel)) result.GenerationLevel = generationLevel;
            
            // Parse date fields - be strict about parsing to avoid invalid dates
            if (metadata.TryGetValue("date-uploaded", out var dateUploadedStr) &&
                !string.IsNullOrEmpty(dateUploadedStr))
            {
                // Try to parse the date, but only if it's a valid format
                if (DateTime.TryParseExact(dateUploadedStr, "yyyy-MM-ddTHH:mm:ssZ", null, System.Globalization.DateTimeStyles.AssumeUniversal, out var dateUploaded))
                {
                    result.DateUploaded = dateUploaded.ToUniversalTime();
                }
                else if (DateTime.TryParse(dateUploadedStr, out var fallbackDate))
                {
                    result.DateUploaded = fallbackDate.ToUniversalTime();
                }
                // If it fails to parse, leave as null
            }
            
            if (metadata.TryGetValue("date-taken", out var dateTakenStr) &&
                !string.IsNullOrEmpty(dateTakenStr))
            {
                // Try to parse the date, but only if it's a valid format
                if (DateTime.TryParseExact(dateTakenStr, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out var dateTaken))
                {
                    result.DateTaken = dateTaken;
                }
                else if (DateTime.TryParse(dateTakenStr, out var fallbackDate))
                {
                    result.DateTaken = fallbackDate;
                }
                // If it fails to parse, leave as null
            }
            
            // Parse numeric fields
            if (metadata.TryGetValue("file-size", out var fileSizeStr) &&
                !string.IsNullOrEmpty(fileSizeStr) && long.TryParse(fileSizeStr, out var fileSize))
            {
                result.FileSize = fileSize;
            }
            
            if (metadata.TryGetValue("width", out var widthStr) &&
                !string.IsNullOrEmpty(widthStr) && int.TryParse(widthStr, out var width))
            {
                result.Width = width;
            }
            
            if (metadata.TryGetValue("height", out var heightStr) &&
                !string.IsNullOrEmpty(heightStr) && int.TryParse(heightStr, out var height))
            {
                result.Height = height;
            }
            
            // System fields
            if (metadata.TryGetValue("file-format", out var fileFormat) && !string.IsNullOrEmpty(fileFormat)) result.FileFormat = fileFormat;
            if (metadata.TryGetValue("content-type", out var contentType) && !string.IsNullOrEmpty(contentType)) result.ContentType = contentType;
            if (metadata.TryGetValue("uploader-ip", out var uploaderIp) && !string.IsNullOrEmpty(uploaderIp)) result.UploaderIp = uploaderIp;
            if (metadata.TryGetValue("uploader-user-agent", out var userAgent) && !string.IsNullOrEmpty(userAgent)) result.UploaderUserAgent = userAgent;
            if (metadata.TryGetValue("upload-session-id", out var sessionId) && !string.IsNullOrEmpty(sessionId)) result.UploadSessionId = sessionId;
            
            // Add any remaining metadata to the additional metadata dictionary
            foreach (var kvp in metadata)
            {
                string key = kvp.Key.ToLowerInvariant();
                if (key != "cat-name" && key != "description" && key != "cat-age" &&
                    key != "gender" && key != "bloodline" && key != "breed" &&
                    key != "hair-color" && key != "date-taken" && key != "personality" &&
                    key != "category" && key != "tags" && key != "mother" &&
                    key != "father" && key != "date-uploaded" && key != "file-format" &&
                    key != "content-type" && key != "file-size" && key != "width" &&
                    key != "height" && key != "uploader-ip" && key != "uploader-user-agent" &&
                    key != "upload-session-id" && key != "cat-id" && key != "mother-cat-id" &&
                    key != "father-cat-id" && key != "registered-name" && key != "registration-number" &&
                    key != "breeding-status" && key != "availability-status" && key != "photo-type" &&
                    key != "age-at-photo" && key != "champion-titles" && key != "generation-level")
                {
                    result.AdditionalMetadata[kvp.Key] = kvp.Value;
                }
            }
            
            return result;
        }
    }
}
