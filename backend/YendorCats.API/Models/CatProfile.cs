using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Cat profile entity for comprehensive cat management and pedigree tracking
    /// Supports the full breeding program with lineage, registration, and show data
    /// </summary>
    [Table("CatProfiles")]
    public class CatProfile
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [MaxLength(50)]
        public string CatId { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string CatName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Breed { get; set; } = "Maine Coon";
        
        [MaxLength(100)]
        public string? Bloodline { get; set; }
        
        [MaxLength(1)]
        public string? Gender { get; set; } // M/F
        
        public DateTime? BirthDate { get; set; }
        
        // Computed property for current age
        [NotMapped]
        public string CurrentAge
        {
            get
            {
                if (!BirthDate.HasValue)
                    return "Unknown";
                
                var age = DateTime.Now - BirthDate.Value;
                var years = (int)(age.TotalDays / 365.25);
                var months = (int)((age.TotalDays % 365.25) / 30.44);
                
                if (years == 0)
                    return months == 1 ? "1 month" : $"{months} months";
                else if (months == 0)
                    return years == 1 ? "1 year" : $"{years} years";
                else
                    return $"{years} years, {months} months";
            }
        }
        
        [MaxLength(50)]
        public string? BreedingStatus { get; set; } // Available, Breeding, Retired, Kitten
        
        [MaxLength(50)]
        public string? AvailabilityStatus { get; set; } // Available, Reserved, Sold, Not-For-Sale
        
        // Pedigree relationships
        [MaxLength(50)]
        public string? FatherId { get; set; }
        
        [MaxLength(50)]
        public string? MotherId { get; set; }
        
        // Show and registration information
        [MaxLength(500)]
        public string? ChampionTitles { get; set; }
        
        [MaxLength(100)]
        public string? RegistrationNumber { get; set; }
        
        [MaxLength(200)]
        public string? RegisteredName { get; set; }
        
        [MaxLength(100)]
        public string? RegistrationBody { get; set; } // TICA, CFA, etc.
        
        // Physical characteristics
        [MaxLength(100)]
        public string? Color { get; set; }
        
        [MaxLength(100)]
        public string? Pattern { get; set; }
        
        [MaxLength(200)]
        public string? Markings { get; set; }
        
        [MaxLength(50)]
        public string? EyeColor { get; set; }
        
        public decimal? Weight { get; set; } // in kg
        
        public decimal? Length { get; set; } // in cm
        
        public decimal? Height { get; set; } // in cm
        
        // Health and genetics
        [MaxLength(1000)]
        public string? HealthRecords { get; set; }
        
        [MaxLength(500)]
        public string? GeneticTesting { get; set; }
        
        [MaxLength(500)]
        public string? Vaccinations { get; set; }
        
        public DateTime? LastHealthCheck { get; set; }
        
        [MaxLength(200)]
        public string? VeterinarianContact { get; set; }
        
        // Personality and behavior
        [MaxLength(1000)]
        public string? PersonalityDescription { get; set; }
        
        [MaxLength(500)]
        public string? BehaviorTraits { get; set; }
        
        [MaxLength(500)]
        public string? SpecialNeeds { get; set; }
        
        // Breeding information
        public int? TotalLitters { get; set; }
        
        public DateTime? FirstBreedingDate { get; set; }
        
        public DateTime? LastBreedingDate { get; set; }
        
        [MaxLength(1000)]
        public string? BreedingNotes { get; set; }
        
        // Location and care
        [MaxLength(200)]
        public string? CurrentLocation { get; set; }
        
        [MaxLength(200)]
        public string? CaregiverName { get; set; }
        
        [MaxLength(200)]
        public string? CaregiverContact { get; set; }
        
        // Profile image
        [MaxLength(500)]
        public string? ProfileImageStorageKey { get; set; }
        
        [MaxLength(10)]
        public string? ProfileImageStorageProvider { get; set; } // S3 or B2
        
        // Computed property for profile image URL
        [NotMapped]
        public string? ProfileImageUrl
        {
            get
            {
                if (string.IsNullOrEmpty(ProfileImageStorageKey))
                    return null;
                
                return ProfileImageStorageProvider switch
                {
                    "B2" => $"https://f002.backblazeb2.com/file/yendor-profiles/{ProfileImageStorageKey}",
                    "S3" => $"https://yendor-profiles.s3.amazonaws.com/{ProfileImageStorageKey}",
                    _ => null
                };
            }
        }
        
        // Financial information
        public decimal? PurchasePrice { get; set; }
        
        public decimal? CurrentValue { get; set; }
        
        public decimal? StudFee { get; set; }
        
        public decimal? KittenPrice { get; set; }
        
        [MaxLength(500)]
        public string? PricingNotes { get; set; }
        
        // Status and visibility
        public bool IsActive { get; set; } = true;
        
        public bool IsPublic { get; set; } = true;
        
        public bool IsFeatured { get; set; } = false;
        
        public int DisplayOrder { get; set; } = 0;
        
        // Timestamps
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;
        
        // Audit fields
        [MaxLength(100)]
        public string? CreatedBy { get; set; }
        
        [MaxLength(100)]
        public string? ModifiedBy { get; set; }
        
        // Additional metadata
        [MaxLength(1000)]
        public string? Notes { get; set; }
        
        [MaxLength(500)]
        public string? Tags { get; set; } // Comma-separated tags

        // Legacy/Compatibility Properties (for backward compatibility with existing code)
        /// <summary>
        /// Alias for CatName - for backward compatibility
        /// </summary>
        [NotMapped]
        public string Name
        {
            get => CatName;
            set => CatName = value;
        }

        /// <summary>
        /// Alias for BirthDate - for backward compatibility
        /// </summary>
        [NotMapped]
        public DateTime? DateOfBirth
        {
            get => BirthDate;
            set => BirthDate = value;
        }

        /// <summary>
        /// Alias for ModifiedAt - for backward compatibility
        /// </summary>
        [NotMapped]
        public DateTime UpdatedAt
        {
            get => ModifiedAt;
            set => ModifiedAt = value;
        }

        /// <summary>
        /// General status field - maps to BreedingStatus for compatibility
        /// </summary>
        [NotMapped]
        public string? Status
        {
            get => BreedingStatus;
            set => BreedingStatus = value;
        }

        /// <summary>
        /// Bloodline type classification
        /// </summary>
        [MaxLength(50)]
        public string? BloodlineType { get; set; }

        /// <summary>
        /// Pedigree information
        /// </summary>
        [MaxLength(2000)]
        public string? Pedigree { get; set; }

        /// <summary>
        /// Microchip identification number
        /// </summary>
        [MaxLength(15)]
        public string? MicrochipNumber { get; set; }

        /// <summary>
        /// Awards and achievements
        /// </summary>
        [MaxLength(1000)]
        public string? Awards { get; set; }

        /// <summary>
        /// General description - maps to PersonalityDescription for compatibility
        /// </summary>
        [NotMapped]
        public string? Description
        {
            get => PersonalityDescription;
            set => PersonalityDescription = value;
        }

        /// <summary>
        /// Whether this cat is available for breeding
        /// </summary>
        public bool IsBreeding { get; set; } = false;

        /// <summary>
        /// Whether this cat is available for stud service
        /// </summary>
        public bool IsStudService { get; set; } = false;

        /// <summary>
        /// Whether this cat is retired from breeding
        /// </summary>
        public bool IsRetired { get; set; } = false;

        /// <summary>
        /// Number of times this profile has been viewed
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// Number of likes this profile has received
        /// </summary>
        public int LikeCount { get; set; } = 0;

        /// <summary>
        /// Number of offspring this cat has produced
        /// </summary>
        public int OffspringCount { get; set; } = 0;

        /// <summary>
        /// Age in years - computed from BirthDate
        /// </summary>
        [NotMapped]
        public float Age
        {
            get
            {
                if (!BirthDate.HasValue) return 0;
                var age = DateTime.Now - BirthDate.Value;
                return (float)(age.TotalDays / 365.25); // Account for leap years
            }
        }

        // Additional Properties Expected by AppDbContext
        /// <summary>
        /// Sire (father) ID - alias for FatherId for backward compatibility
        /// </summary>
        [NotMapped]
        public string? SireId
        {
            get => FatherId;
            set => FatherId = value;
        }

        /// <summary>
        /// Sire (father) name - computed from Father navigation property
        /// </summary>
        [NotMapped]
        public string? SireName
        {
            get => Father?.CatName;
            set
            {
                // This is a computed property, but we allow setting for compatibility
                // The actual name comes from the Father navigation property
            }
        }

        /// <summary>
        /// Dam (mother) ID - alias for MotherId for backward compatibility
        /// </summary>
        [NotMapped]
        public string? DamId
        {
            get => MotherId;
            set => MotherId = value;
        }

        /// <summary>
        /// Dam (mother) name - computed from Mother navigation property
        /// </summary>
        [NotMapped]
        public string? DamName
        {
            get => Mother?.CatName;
            set
            {
                // This is a computed property, but we allow setting for compatibility
                // The actual name comes from the Mother navigation property
            }
        }

        /// <summary>
        /// Microchip number - for identification
        /// </summary>
        [MaxLength(15)]
        public string? Microchip { get; set; }

        /// <summary>
        /// Tattoo identification
        /// </summary>
        [MaxLength(20)]
        public string? Tattoo { get; set; }

        /// <summary>
        /// Show titles and achievements - alias for ChampionTitles
        /// </summary>
        [NotMapped]
        public string? Titles
        {
            get => ChampionTitles;
            set => ChampionTitles = value;
        }

        /// <summary>
        /// Health status summary
        /// </summary>
        [MaxLength(100)]
        public string? HealthStatus { get; set; }

        /// <summary>
        /// Health notes and details - alias for HealthRecords
        /// </summary>
        [NotMapped]
        public string? HealthNotes
        {
            get => HealthRecords;
            set => HealthRecords = value;
        }

        /// <summary>
        /// Veterinarian information - alias for VeterinarianContact
        /// </summary>
        [NotMapped]
        public string? VetInfo
        {
            get => VeterinarianContact;
            set => VeterinarianContact = value;
        }

        /// <summary>
        /// Owner information
        /// </summary>
        [MaxLength(500)]
        public string? OwnerInfo { get; set; }

        /// <summary>
        /// Profile image URL or reference - computed from ProfileImageStorageKey
        /// </summary>
        [NotMapped]
        public string? ProfileImage
        {
            get
            {
                if (string.IsNullOrEmpty(ProfileImageStorageKey)) return null;

                // Generate URL based on storage provider
                return ProfileImageStorageProvider?.ToUpper() switch
                {
                    "B2" => $"https://f004.backblazeb2.com/file/yendor/{ProfileImageStorageKey}",
                    "S3" => $"https://f004.backblazeb2.com/file/yendor/{ProfileImageStorageKey}", // Using B2 as default
                    _ => $"https://f004.backblazeb2.com/file/yendor/{ProfileImageStorageKey}"
                };
            }
            set
            {
                // This is a computed property, but we allow setting for compatibility
                // The actual URL generation is based on ProfileImageStorageKey and ProfileImageStorageProvider
            }
        }

        /// <summary>
        /// Personality traits - alias for PersonalityDescription
        /// </summary>
        [NotMapped]
        public string? Personality
        {
            get => PersonalityDescription;
            set => PersonalityDescription = value;
        }

        // Navigation properties
        public virtual ICollection<CatGalleryImage> GalleryImages { get; set; } = new List<CatGalleryImage>();

        // Remove [NotMapped] to allow EF to configure these relationships properly
        public virtual CatProfile? Father { get; set; }
        public virtual CatProfile? Mother { get; set; }

        public virtual ICollection<CatProfile> Offspring { get; set; } = new List<CatProfile>();
        
        // Helper methods
        public void UpdateModifiedTimestamp(string? modifiedBy = null)
        {
            ModifiedAt = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(modifiedBy))
                ModifiedBy = modifiedBy;
        }
        
        public bool IsKitten()
        {
            return BirthDate.HasValue && (DateTime.Now - BirthDate.Value).TotalDays < 365;
        }
        
        public bool IsBreedingAge()
        {
            if (!BirthDate.HasValue) return false;
            
            var ageInMonths = (DateTime.Now - BirthDate.Value).TotalDays / 30.44;
            return Gender switch
            {
                "M" => ageInMonths >= 12, // Males can breed at 12 months
                "F" => ageInMonths >= 8,  // Females can breed at 8 months
                _ => false
            };
        }
        
        public string GetBreedingStatusSummary()
        {
            if (!IsBreedingAge())
                return "Too young for breeding";
            
            return BreedingStatus switch
            {
                "Available" => $"Available for breeding - {Gender switch { "M" => "Stud", "F" => "Queen", _ => "Cat" }}",
                "Breeding" => "Currently breeding",
                "Retired" => "Retired from breeding",
                "Kitten" => "Kitten - not yet breeding age",
                _ => "Status unknown"
            };
        }
        
        public List<string> GetTagsList()
        {
            if (string.IsNullOrEmpty(Tags))
                return new List<string>();
            
            return Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                      .Select(tag => tag.Trim())
                      .Where(tag => !string.IsNullOrEmpty(tag))
                      .ToList();
        }
        
        public void SetTags(IEnumerable<string> tags)
        {
            Tags = string.Join(", ", tags.Where(tag => !string.IsNullOrWhiteSpace(tag)));
        }
        
        // Pedigree helper methods
        public string GetPedigreeDisplay(int generations = 3)
        {
            var pedigree = new List<string>();
            
            if (!string.IsNullOrEmpty(RegisteredName))
                pedigree.Add($"Self: {RegisteredName}");
            
            if (Father != null && !string.IsNullOrEmpty(Father.RegisteredName))
                pedigree.Add($"Sire: {Father.RegisteredName}");
            
            if (Mother != null && !string.IsNullOrEmpty(Mother.RegisteredName))
                pedigree.Add($"Dam: {Mother.RegisteredName}");
            
            return string.Join(" | ", pedigree);
        }
        
        public decimal? GetEstimatedValue()
        {
            if (CurrentValue.HasValue)
                return CurrentValue;
            
            // Simple estimation based on breeding status and titles
            decimal baseValue = 1000; // Base value for Maine Coon
            
            if (!string.IsNullOrEmpty(ChampionTitles))
                baseValue *= 2; // Champions are worth more
            
            if (BreedingStatus == "Available" && IsBreedingAge())
                baseValue *= 1.5m; // Breeding cats are worth more
            
            return baseValue;
        }
    }
}