using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace YendorCats.API.Models
{
    /// <summary>
    /// Appointment model for tracking client appointments
    /// </summary>
    public class Appointment
    {
        /// <summary>
        /// Unique identifier for the appointment
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Title or subject of the appointment
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Description or details of the appointment
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// ID of the client associated with this appointment
        /// </summary>
        [Required]
        public int ClientId { get; set; }

        /// <summary>
        /// Navigation property for the client
        /// </summary>
        [ForeignKey("ClientId")]
        public virtual Client? Client { get; set; }

        /// <summary>
        /// Start date and time of the appointment
        /// </summary>
        [Required]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End date and time of the appointment
        /// </summary>
        [Required]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Location of the appointment
        /// </summary>
        [StringLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// Status of the appointment (Scheduled, Confirmed, Completed, Cancelled, etc.)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = AppointmentStatus.Scheduled;

        /// <summary>
        /// Notes about the appointment
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether reminder emails have been sent
        /// </summary>
        public bool ReminderSent { get; set; } = false;

        /// <summary>
        /// When reminder emails were sent
        /// </summary>
        public DateTime? ReminderSentAt { get; set; }

        /// <summary>
        /// ID of the admin user who created this appointment
        /// </summary>
        public int? CreatedById { get; set; }

        /// <summary>
        /// When the appointment was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the appointment was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// ID of the cat associated with this appointment (optional)
        /// </summary>
        public int? CatId { get; set; }

        /// <summary>
        /// Navigation property for the cat
        /// </summary>
        [ForeignKey("CatId")]
        public virtual Cat? Cat { get; set; }
    }

    /// <summary>
    /// Appointment status options
    /// </summary>
    public static class AppointmentStatus
    {
        public const string Scheduled = "Scheduled";
        public const string Confirmed = "Confirmed";
        public const string Completed = "Completed";
        public const string Cancelled = "Cancelled";
        public const string Rescheduled = "Rescheduled";
        public const string NoShow = "NoShow";
    }

    /// <summary>
    /// Appointment creation request model
    /// </summary>
    public class CreateAppointmentRequest
    {
        /// <summary>
        /// Title or subject of the appointment
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Description or details of the appointment
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// ID of the client associated with this appointment
        /// </summary>
        [Required]
        public int ClientId { get; set; }

        /// <summary>
        /// Start date and time of the appointment
        /// </summary>
        [Required]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End date and time of the appointment
        /// </summary>
        [Required]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Location of the appointment
        /// </summary>
        [StringLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// Notes about the appointment
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// ID of the cat associated with this appointment (optional)
        /// </summary>
        public int? CatId { get; set; }
    }

    /// <summary>
    /// Appointment update request model
    /// </summary>
    public class UpdateAppointmentRequest
    {
        /// <summary>
        /// Title or subject of the appointment
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Description or details of the appointment
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// ID of the client associated with this appointment
        /// </summary>
        [Required]
        public int ClientId { get; set; }

        /// <summary>
        /// Start date and time of the appointment
        /// </summary>
        [Required]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End date and time of the appointment
        /// </summary>
        [Required]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Location of the appointment
        /// </summary>
        [StringLength(200)]
        public string? Location { get; set; }

        /// <summary>
        /// Status of the appointment
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Status { get; set; } = AppointmentStatus.Scheduled;

        /// <summary>
        /// Notes about the appointment
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// ID of the cat associated with this appointment (optional)
        /// </summary>
        public int? CatId { get; set; }
    }

    /// <summary>
    /// Appointment response model
    /// </summary>
    public class AppointmentResponse
    {
        /// <summary>
        /// Unique identifier for the appointment
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Title or subject of the appointment
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Description or details of the appointment
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// ID of the client associated with this appointment
        /// </summary>
        public int ClientId { get; set; }

        /// <summary>
        /// Client information
        /// </summary>
        public ClientBasicInfo? Client { get; set; }

        /// <summary>
        /// Start date and time of the appointment
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// End date and time of the appointment
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Duration of the appointment in minutes
        /// </summary>
        public int DurationMinutes => (int)(EndTime - StartTime).TotalMinutes;

        /// <summary>
        /// Location of the appointment
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// Status of the appointment
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Notes about the appointment
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// Whether reminder emails have been sent
        /// </summary>
        public bool ReminderSent { get; set; }

        /// <summary>
        /// When reminder emails were sent
        /// </summary>
        public DateTime? ReminderSentAt { get; set; }

        /// <summary>
        /// When the appointment was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the appointment was last updated
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// ID of the cat associated with this appointment (optional)
        /// </summary>
        public int? CatId { get; set; }

        /// <summary>
        /// Basic cat information
        /// </summary>
        public CatBasicInfo? Cat { get; set; }
    }

    /// <summary>
    /// Basic client information for appointment responses
    /// </summary>
    public class ClientBasicInfo
    {
        /// <summary>
        /// Client ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Client full name
        /// </summary>
        public string FullName { get; set; } = string.Empty;

        /// <summary>
        /// Client email
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Client phone
        /// </summary>
        public string? Phone { get; set; }
    }

    /// <summary>
    /// Basic cat information for appointment responses
    /// </summary>
    public class CatBasicInfo
    {
        /// <summary>
        /// Cat ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Cat name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Cat breed
        /// </summary>
        public string Breed { get; set; } = string.Empty;
    }
}
