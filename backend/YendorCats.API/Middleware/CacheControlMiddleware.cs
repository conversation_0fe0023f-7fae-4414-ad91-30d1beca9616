using Microsoft.Extensions.Primitives;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Middleware for advanced cache control and optimization
    /// Provides intelligent caching headers based on content type and request patterns
    /// </summary>
    public class CacheControlMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<CacheControlMiddleware> _logger;
        private readonly CacheControlOptions _options;

        public CacheControlMiddleware(
            RequestDelegate next,
            ILogger<CacheControlMiddleware> logger,
            CacheControlOptions options)
        {
            _next = next;
            _logger = logger;
            _options = options;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Process request
            ProcessRequest(context);
            
            await _next(context);
            
            // Process response
            ProcessResponse(context);
        }

        /// <summary>
        /// Process incoming request for cache optimization
        /// </summary>
        private void ProcessRequest(HttpContext context)
        {
            try
            {
                var request = context.Request;
                
                // Handle conditional requests (If-None-Match, If-Modified-Since)
                HandleConditionalRequest(context);
                
                // Add cache-friendly request headers
                if (_options.EnableRequestOptimization)
                {
                    OptimizeRequestHeaders(context);
                }
                
                // Track cache-related request patterns
                TrackCachePatterns(context);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing cache request headers");
            }
        }

        /// <summary>
        /// Process outgoing response for cache optimization
        /// </summary>
        private void ProcessResponse(HttpContext context)
        {
            try
            {
                var response = context.Response;
                var request = context.Request;
                
                // Skip cache headers for certain status codes
                if (response.StatusCode >= 400)
                {
                    SetNoCacheHeaders(response);
                    return;
                }
                
                // Determine cache strategy based on endpoint
                var cacheStrategy = DetermineCacheStrategy(context);
                
                // Apply cache headers based on strategy
                ApplyCacheHeaders(context, cacheStrategy);
                
                // Add ETag for cache validation
                if (_options.EnableETags && cacheStrategy.EnableETag)
                {
                    AddETagHeader(context);
                }
                
                // Add Last-Modified header
                if (_options.EnableLastModified && cacheStrategy.EnableLastModified)
                {
                    AddLastModifiedHeader(context);
                }
                
                // Add Vary headers for content negotiation
                AddVaryHeaders(context, cacheStrategy);
                
                // Add custom cache optimization headers
                AddOptimizationHeaders(context);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing cache response headers");
            }
        }

        /// <summary>
        /// Handle conditional requests (304 Not Modified)
        /// </summary>
        private void HandleConditionalRequest(HttpContext context)
        {
            var request = context.Request;
            var response = context.Response;
            
            // Check If-None-Match header (ETag)
            if (request.Headers.TryGetValue("If-None-Match", out var ifNoneMatch))
            {
                // This would need to be implemented with actual ETag comparison
                // For now, we'll just log the request
                _logger.LogDebug("Conditional request with If-None-Match: {ETag}", ifNoneMatch);
            }
            
            // Check If-Modified-Since header
            if (request.Headers.TryGetValue("If-Modified-Since", out var ifModifiedSince))
            {
                if (DateTime.TryParse(ifModifiedSince, out var modifiedSince))
                {
                    _logger.LogDebug("Conditional request with If-Modified-Since: {Date}", modifiedSince);
                    // Implementation would check if content was modified since this date
                }
            }
        }

        /// <summary>
        /// Optimize request headers for better caching
        /// </summary>
        private void OptimizeRequestHeaders(HttpContext context)
        {
            var request = context.Request;
            
            // Add Accept-Encoding if not present (for compression)
            if (!request.Headers.ContainsKey("Accept-Encoding"))
            {
                request.Headers.Add("Accept-Encoding", "gzip, deflate, br");
            }
        }

        /// <summary>
        /// Track cache-related request patterns for optimization
        /// </summary>
        private void TrackCachePatterns(HttpContext context)
        {
            if (!_options.EnablePatternTracking)
                return;
            
            var request = context.Request;
            var userAgent = request.Headers["User-Agent"].FirstOrDefault();
            var acceptEncoding = request.Headers["Accept-Encoding"].FirstOrDefault();
            
            // Log patterns for analysis (in production, this would go to metrics service)
            _logger.LogDebug("Cache pattern: Path={Path}, UserAgent={UserAgent}, AcceptEncoding={AcceptEncoding}",
                request.Path, userAgent, acceptEncoding);
        }

        /// <summary>
        /// Determine cache strategy based on request context
        /// </summary>
        private CacheStrategy DetermineCacheStrategy(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower() ?? "";
            var method = context.Request.Method;
            
            // API endpoints
            if (path.StartsWith("/api/v2/gallery/"))
            {
                if (path.Contains("/search"))
                {
                    return _options.SearchCacheStrategy;
                }
                else if (path.Contains("/stats"))
                {
                    return _options.StatsCacheStrategy;
                }
                else if (path.Contains("/popular") || path.Contains("/recent"))
                {
                    return _options.PopularContentCacheStrategy;
                }
                else
                {
                    return _options.GalleryCacheStrategy;
                }
            }
            
            // Static content
            if (IsStaticContent(path))
            {
                return _options.StaticContentCacheStrategy;
            }
            
            // Admin endpoints
            if (path.StartsWith("/api/v2/admin/"))
            {
                return _options.AdminCacheStrategy;
            }
            
            // Default strategy
            return _options.DefaultCacheStrategy;
        }

        /// <summary>
        /// Apply cache headers based on strategy
        /// </summary>
        private void ApplyCacheHeaders(HttpContext context, CacheStrategy strategy)
        {
            var response = context.Response;
            
            if (strategy.MaxAge.HasValue)
            {
                var cacheControl = new List<string>();
                
                if (strategy.IsPublic)
                {
                    cacheControl.Add("public");
                }
                else
                {
                    cacheControl.Add("private");
                }
                
                cacheControl.Add($"max-age={strategy.MaxAge.Value.TotalSeconds:F0}");
                
                if (strategy.MustRevalidate)
                {
                    cacheControl.Add("must-revalidate");
                }
                
                if (strategy.NoStore)
                {
                    cacheControl.Add("no-store");
                }
                
                if (strategy.NoCache)
                {
                    cacheControl.Add("no-cache");
                }
                
                response.Headers.Add("Cache-Control", string.Join(", ", cacheControl));
            }
            
            // Set Expires header
            if (strategy.MaxAge.HasValue && !strategy.NoCache && !strategy.NoStore)
            {
                var expires = DateTime.UtcNow.Add(strategy.MaxAge.Value);
                response.Headers.Add("Expires", expires.ToString("R"));
            }
        }

        /// <summary>
        /// Add ETag header for cache validation
        /// </summary>
        private void AddETagHeader(HttpContext context)
        {
            var response = context.Response;
            
            // Generate ETag based on response content or other factors
            // This is a simplified implementation
            var etag = GenerateETag(context);
            if (!string.IsNullOrEmpty(etag))
            {
                response.Headers.Add("ETag", $"\"{etag}\"");
            }
        }

        /// <summary>
        /// Add Last-Modified header
        /// </summary>
        private void AddLastModifiedHeader(HttpContext context)
        {
            var response = context.Response;
            
            // This would typically come from the actual content modification time
            // For now, we'll use a default approach
            var lastModified = GetLastModifiedDate(context);
            if (lastModified.HasValue)
            {
                response.Headers.Add("Last-Modified", lastModified.Value.ToString("R"));
            }
        }

        /// <summary>
        /// Add Vary headers for content negotiation
        /// </summary>
        private void AddVaryHeaders(HttpContext context, CacheStrategy strategy)
        {
            var response = context.Response;
            var varyHeaders = new List<string>();
            
            if (strategy.VaryByAcceptEncoding)
            {
                varyHeaders.Add("Accept-Encoding");
            }
            
            if (strategy.VaryByUserAgent)
            {
                varyHeaders.Add("User-Agent");
            }
            
            if (strategy.VaryByAccept)
            {
                varyHeaders.Add("Accept");
            }
            
            if (varyHeaders.Any())
            {
                response.Headers.Add("Vary", string.Join(", ", varyHeaders));
            }
        }

        /// <summary>
        /// Add custom optimization headers
        /// </summary>
        private void AddOptimizationHeaders(HttpContext context)
        {
            var response = context.Response;
            
            // Add compression hint
            if (_options.EnableCompressionHints)
            {
                response.Headers.Add("X-Content-Compressed", "true");
            }
            
            // Add cache optimization hints
            if (_options.EnableCacheHints)
            {
                response.Headers.Add("X-Cache-Optimized", "true");
                response.Headers.Add("X-Cache-Strategy", "multi-level");
            }
        }

        /// <summary>
        /// Set no-cache headers for error responses
        /// </summary>
        private void SetNoCacheHeaders(HttpResponse response)
        {
            response.Headers.Add("Cache-Control", "no-cache, no-store, must-revalidate");
            response.Headers.Add("Pragma", "no-cache");
            response.Headers.Add("Expires", "0");
        }

        /// <summary>
        /// Check if path represents static content
        /// </summary>
        private bool IsStaticContent(string path)
        {
            var staticExtensions = new[] { ".css", ".js", ".png", ".jpg", ".jpeg", ".gif", ".ico", ".svg", ".woff", ".woff2" };
            return staticExtensions.Any(ext => path.EndsWith(ext));
        }

        /// <summary>
        /// Generate ETag for response
        /// </summary>
        private string GenerateETag(HttpContext context)
        {
            // This is a simplified ETag generation
            // In production, this would be based on actual content hash
            var path = context.Request.Path.Value ?? "";
            var query = context.Request.QueryString.Value ?? "";
            var combined = $"{path}{query}";
            
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(combined))
                .Replace("+", "-")
                .Replace("/", "_")
                .Replace("=", "")
                .Substring(0, Math.Min(16, combined.Length));
        }

        /// <summary>
        /// Get last modified date for content
        /// </summary>
        private DateTime? GetLastModifiedDate(HttpContext context)
        {
            // This would typically come from actual content metadata
            // For now, return a default value
            return DateTime.UtcNow.Date; // Today at midnight
        }
    }

    /// <summary>
    /// Cache strategy configuration
    /// </summary>
    public class CacheStrategy
    {
        public TimeSpan? MaxAge { get; set; }
        public bool IsPublic { get; set; } = true;
        public bool MustRevalidate { get; set; } = false;
        public bool NoStore { get; set; } = false;
        public bool NoCache { get; set; } = false;
        public bool EnableETag { get; set; } = true;
        public bool EnableLastModified { get; set; } = true;
        public bool VaryByAcceptEncoding { get; set; } = true;
        public bool VaryByUserAgent { get; set; } = false;
        public bool VaryByAccept { get; set; } = false;
    }

    /// <summary>
    /// Cache control middleware options
    /// </summary>
    public class CacheControlOptions
    {
        public bool EnableETags { get; set; } = true;
        public bool EnableLastModified { get; set; } = true;
        public bool EnableRequestOptimization { get; set; } = true;
        public bool EnablePatternTracking { get; set; } = false;
        public bool EnableCompressionHints { get; set; } = true;
        public bool EnableCacheHints { get; set; } = true;

        // Cache strategies for different content types
        public CacheStrategy DefaultCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromMinutes(5),
            IsPublic = true
        };

        public CacheStrategy GalleryCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromMinutes(10),
            IsPublic = true,
            EnableETag = true
        };

        public CacheStrategy SearchCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromMinutes(3),
            IsPublic = true
        };

        public CacheStrategy StatsCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromMinutes(15),
            IsPublic = true
        };

        public CacheStrategy PopularContentCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromMinutes(30),
            IsPublic = true
        };

        public CacheStrategy StaticContentCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromDays(30),
            IsPublic = true,
            EnableETag = true
        };

        public CacheStrategy AdminCacheStrategy { get; set; } = new()
        {
            MaxAge = TimeSpan.FromMinutes(1),
            IsPublic = false,
            MustRevalidate = true
        };
    }

    /// <summary>
    /// Extension methods for cache control middleware
    /// </summary>
    public static class CacheControlMiddlewareExtensions
    {
        public static IApplicationBuilder UseCacheControl(
            this IApplicationBuilder builder,
            CacheControlOptions? options = null)
        {
            options ??= new CacheControlOptions();
            return builder.UseMiddleware<CacheControlMiddleware>(options);
        }

        public static IServiceCollection AddCacheControl(
            this IServiceCollection services,
            Action<CacheControlOptions>? configureOptions = null)
        {
            var options = new CacheControlOptions();
            configureOptions?.Invoke(options);
            
            services.AddSingleton(options);
            return services;
        }
    }
}
