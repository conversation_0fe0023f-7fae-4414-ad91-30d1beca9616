using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using YendorCats.API.Data;
using YendorCats.API.Services;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Middleware to check if admin user needs to change their password
    /// </summary>
    public class PasswordChangeRequiredMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PasswordChangeRequiredMiddleware> _logger;

        /// <summary>
        /// Constructor for password change required middleware
        /// </summary>
        /// <param name="next">Next middleware in pipeline</param>
        /// <param name="logger">Logger</param>
        public PasswordChangeRequiredMiddleware(RequestDelegate next, ILogger<PasswordChangeRequiredMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        /// <summary>
        /// Invoke the middleware
        /// </summary>
        /// <param name="context">HTTP context</param>
        /// <param name="adminAuthService">Admin auth service</param>
        /// <param name="dbContext">Database context</param>
        /// <returns>Task</returns>
        public async Task InvokeAsync(HttpContext context, IAdminAuthService adminAuthService, AppDbContext dbContext)
        {
            // Skip middleware for non-API requests or for the password change endpoint itself
            if (!context.Request.Path.StartsWithSegments("/api") ||
                context.Request.Path.StartsWithSegments("/api/AdminUserManagement/change-password") ||
                context.Request.Path.StartsWithSegments("/api/AdminAuth/login") ||
                context.Request.Path.StartsWithSegments("/api/AdminAuth/logout"))
            {
                await _next(context);
                return;
            }

            // Get token from cookie or authorization header
            string? token = context.Request.Cookies["admin_token"] ?? 
                           context.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

            if (string.IsNullOrEmpty(token))
            {
                await _next(context);
                return;
            }

            try
            {
                var admin = await adminAuthService.ValidateTokenAsync(token);

                // If admin user is found and must change password
                if (admin != null && admin.MustChangePassword)
                {
                    _logger.LogInformation("Admin {Username} must change password", admin.Username);

                    // For API requests, return 403 with specific message
                    context.Response.StatusCode = StatusCodes.Status403Forbidden;
                    context.Response.ContentType = "application/json";

                    var response = new
                    {
                        error = "password_change_required",
                        message = "You must change your password before continuing",
                        changePasswordUrl = "/api/AdminUserManagement/change-password"
                    };

                    await context.Response.WriteAsync(JsonSerializer.Serialize(response));
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in password change required middleware");
            }

            await _next(context);
        }
    }

    /// <summary>
    /// Extension methods for the password change required middleware
    /// </summary>
    public static class PasswordChangeRequiredMiddlewareExtensions
    {
        /// <summary>
        /// Adds the password change required middleware to the application
        /// </summary>
        /// <param name="builder">Application builder</param>
        /// <returns>Application builder</returns>
        public static IApplicationBuilder UsePasswordChangeRequired(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<PasswordChangeRequiredMiddleware>();
        }
    }
}
