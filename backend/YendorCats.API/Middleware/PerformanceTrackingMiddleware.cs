using System.Diagnostics;
using System.Text.Json;
using YendorCats.API.Services.Performance;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Middleware for tracking API performance metrics
    /// Automatically records response times, success rates, and other performance data
    /// </summary>
    public class PerformanceTrackingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PerformanceTrackingMiddleware> _logger;
        private readonly PerformanceTrackingOptions _options;

        public PerformanceTrackingMiddleware(
            RequestDelegate next,
            ILogger<PerformanceTrackingMiddleware> logger,
            PerformanceTrackingOptions options)
        {
            _next = next;
            _logger = logger;
            _options = options;
        }

        public async Task InvokeAsync(HttpContext context, IPerformanceMetricsService? performanceMetrics = null)
        {
            // Skip tracking for certain paths
            if (ShouldSkipTracking(context.Request.Path))
            {
                await _next(context);
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            var requestId = context.TraceIdentifier;
            var endpoint = GetEndpointName(context);
            var method = context.Request.Method;
            
            // Add request ID to response headers
            context.Response.Headers.Add("X-Request-Id", requestId);
            
            Exception? exception = null;
            var originalBodyStream = context.Response.Body;
            
            try
            {
                // Capture response for analysis if needed
                using var responseBody = new MemoryStream();
                context.Response.Body = responseBody;
                
                await _next(context);
                
                // Copy response back to original stream
                responseBody.Seek(0, SeekOrigin.Begin);
                await responseBody.CopyToAsync(originalBodyStream);
            }
            catch (Exception ex)
            {
                exception = ex;
                throw;
            }
            finally
            {
                stopwatch.Stop();
                context.Response.Body = originalBodyStream;
                
                // Record performance metrics
                await RecordPerformanceMetrics(
                    context, 
                    performanceMetrics, 
                    endpoint, 
                    method, 
                    stopwatch.Elapsed, 
                    exception == null,
                    exception);
                
                // Add performance headers
                AddPerformanceHeaders(context, stopwatch.Elapsed, requestId);
                
                // Log performance data
                LogPerformanceData(context, endpoint, method, stopwatch.Elapsed, exception == null);
            }
        }

        /// <summary>
        /// Check if request should be skipped from tracking
        /// </summary>
        private bool ShouldSkipTracking(PathString path)
        {
            var pathValue = path.Value?.ToLower() ?? "";
            
            return _options.ExcludedPaths.Any(excludedPath => 
                pathValue.StartsWith(excludedPath.ToLower())) ||
                pathValue.Contains("/swagger") ||
                pathValue.Contains("/health") ||
                pathValue.Contains("/ping");
        }

        /// <summary>
        /// Get normalized endpoint name for metrics
        /// </summary>
        private string GetEndpointName(HttpContext context)
        {
            var path = context.Request.Path.Value ?? "";
            var method = context.Request.Method;
            
            // Normalize path by replacing IDs with placeholders
            var normalizedPath = NormalizePath(path);
            
            return $"{method} {normalizedPath}";
        }

        /// <summary>
        /// Normalize path by replacing dynamic segments with placeholders
        /// </summary>
        private string NormalizePath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return "/";
            
            var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var normalizedSegments = new List<string>();
            
            foreach (var segment in segments)
            {
                // Replace numeric IDs with placeholder
                if (long.TryParse(segment, out _))
                {
                    normalizedSegments.Add("{id}");
                }
                // Replace GUIDs with placeholder
                else if (Guid.TryParse(segment, out _))
                {
                    normalizedSegments.Add("{guid}");
                }
                // Keep other segments as-is
                else
                {
                    normalizedSegments.Add(segment);
                }
            }
            
            return "/" + string.Join("/", normalizedSegments);
        }

        /// <summary>
        /// Record performance metrics
        /// </summary>
        private async Task RecordPerformanceMetrics(
            HttpContext context,
            IPerformanceMetricsService? performanceMetrics,
            string endpoint,
            string method,
            TimeSpan duration,
            bool success,
            Exception? exception)
        {
            if (performanceMetrics == null)
                return;
            
            try
            {
                // Determine cache source from response headers
                var cacheSource = context.Response.Headers["X-Cache-Source"].FirstOrDefault();
                
                await performanceMetrics.RecordApiRequestAsync(
                    endpoint, 
                    method, 
                    duration, 
                    success, 
                    cacheSource);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to record performance metrics for {Endpoint}", endpoint);
            }
        }

        /// <summary>
        /// Add performance headers to response
        /// </summary>
        private void AddPerformanceHeaders(HttpContext context, TimeSpan duration, string requestId)
        {
            try
            {
                if (!context.Response.Headers.ContainsKey("X-Response-Time"))
                {
                    context.Response.Headers.Add("X-Response-Time", duration.TotalMilliseconds.ToString("F0"));
                }
                
                if (!context.Response.Headers.ContainsKey("X-Request-Id"))
                {
                    context.Response.Headers.Add("X-Request-Id", requestId);
                }
                
                context.Response.Headers.Add("X-Timestamp", DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString());
                
                // Add server identification
                context.Response.Headers.Add("X-Server", Environment.MachineName);
                
                // Add API version
                context.Response.Headers.Add("X-API-Version", "2.0");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to add performance headers");
            }
        }

        /// <summary>
        /// Log performance data
        /// </summary>
        private void LogPerformanceData(
            HttpContext context, 
            string endpoint, 
            string method, 
            TimeSpan duration, 
            bool success)
        {
            try
            {
                var statusCode = context.Response.StatusCode;
                var userAgent = context.Request.Headers["User-Agent"].FirstOrDefault();
                var cacheSource = context.Response.Headers["X-Cache-Source"].FirstOrDefault();
                
                if (_options.LogSlowRequests && duration > _options.SlowRequestThreshold)
                {
                    _logger.LogWarning(
                        "Slow request detected: {Method} {Endpoint} - {Duration}ms - Status: {StatusCode} - Cache: {CacheSource}",
                        method, endpoint, duration.TotalMilliseconds, statusCode, cacheSource ?? "none");
                }
                else if (_options.LogAllRequests)
                {
                    _logger.LogInformation(
                        "Request completed: {Method} {Endpoint} - {Duration}ms - Status: {StatusCode} - Cache: {CacheSource}",
                        method, endpoint, duration.TotalMilliseconds, statusCode, cacheSource ?? "none");
                }
                
                // Log errors
                if (!success || statusCode >= 400)
                {
                    _logger.LogWarning(
                        "Request failed: {Method} {Endpoint} - {Duration}ms - Status: {StatusCode} - UserAgent: {UserAgent}",
                        method, endpoint, duration.TotalMilliseconds, statusCode, userAgent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to log performance data");
            }
        }
    }

    /// <summary>
    /// Configuration options for performance tracking middleware
    /// </summary>
    public class PerformanceTrackingOptions
    {
        /// <summary>
        /// Paths to exclude from performance tracking
        /// </summary>
        public List<string> ExcludedPaths { get; set; } = new()
        {
            "/swagger",
            "/health",
            "/ping",
            "/favicon.ico",
            "/robots.txt"
        };

        /// <summary>
        /// Whether to log all requests
        /// </summary>
        public bool LogAllRequests { get; set; } = false;

        /// <summary>
        /// Whether to log slow requests
        /// </summary>
        public bool LogSlowRequests { get; set; } = true;

        /// <summary>
        /// Threshold for considering a request slow
        /// </summary>
        public TimeSpan SlowRequestThreshold { get; set; } = TimeSpan.FromMilliseconds(1000);

        /// <summary>
        /// Whether to include request/response bodies in metrics (for debugging)
        /// </summary>
        public bool IncludeRequestBodies { get; set; } = false;

        /// <summary>
        /// Maximum size of request body to capture (in bytes)
        /// </summary>
        public int MaxRequestBodySize { get; set; } = 1024 * 1024; // 1MB

        /// <summary>
        /// Whether to track user agents
        /// </summary>
        public bool TrackUserAgents { get; set; } = true;

        /// <summary>
        /// Whether to track IP addresses
        /// </summary>
        public bool TrackIpAddresses { get; set; } = false;
    }

    /// <summary>
    /// Extension methods for registering performance tracking middleware
    /// </summary>
    public static class PerformanceTrackingMiddlewareExtensions
    {
        /// <summary>
        /// Add performance tracking middleware to the pipeline
        /// </summary>
        public static IApplicationBuilder UsePerformanceTracking(
            this IApplicationBuilder builder, 
            PerformanceTrackingOptions? options = null)
        {
            options ??= new PerformanceTrackingOptions();
            
            return builder.UseMiddleware<PerformanceTrackingMiddleware>(options);
        }

        /// <summary>
        /// Add performance tracking services
        /// </summary>
        public static IServiceCollection AddPerformanceTracking(
            this IServiceCollection services,
            Action<PerformanceTrackingOptions>? configureOptions = null)
        {
            var options = new PerformanceTrackingOptions();
            configureOptions?.Invoke(options);
            
            services.AddSingleton(options);
            
            return services;
        }
    }
}
