using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using System.Net;

namespace YendorCats.API.Middleware
{
    /// <summary>
    /// Simple rate limiting middleware
    /// </summary>
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IMemoryCache _cache;
        private readonly ILogger<RateLimitingMiddleware> _logger;

        // Rate limiting configuration
        private readonly Dictionary<string, (int requests, TimeSpan window)> _rateLimits = new()
        {
            { "/api/auth/login", (5, TimeSpan.FromMinutes(15)) },
            { "/api/adminauth/login", (3, TimeSpan.FromMinutes(15)) },
            { "/api/auth/register", (3, TimeSpan.FromHours(1)) },
            { "/api/photoupload/upload", (10, TimeSpan.FromMinutes(5)) }
        };

        public RateLimitingMiddleware(RequestDelegate next, IMemoryCache cache, ILogger<RateLimitingMiddleware> logger)
        {
            _next = next;
            _cache = cache;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var endpoint = context.Request.Path.Value?.ToLower();
            var clientIp = GetClientIpAddress(context);

            if (endpoint != null && _rateLimits.ContainsKey(endpoint))
            {
                var (maxRequests, timeWindow) = _rateLimits[endpoint];
                var key = $"rate_limit_{clientIp}_{endpoint}";

                var requestCount = _cache.Get<int>(key);
                
                if (requestCount >= maxRequests)
                {
                    _logger.LogWarning("Rate limit exceeded for IP {ClientIp} on endpoint {Endpoint}", clientIp, endpoint);
                    
                    context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                    context.Response.Headers.Add("Retry-After", timeWindow.TotalSeconds.ToString());
                    
                    await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                    return;
                }

                _cache.Set(key, requestCount + 1, timeWindow);
            }

            await _next(context);
        }

        private string GetClientIpAddress(HttpContext context)
        {
            // Check for forwarded IP first (for load balancers/proxies)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }
    }
}
