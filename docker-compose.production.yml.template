services:
  # Frontend Service - Nginx serving static files with API proxy
  frontend:
    image: ${ECR_REGISTRY}/yendorcats/frontend:staging
    container_name: yendorcats-frontend-production
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    depends_on:
      - api
      - uploader
    networks:
      - yendorcats-production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Backend API Service - .NET Core API
  api:
    image: ${ECR_REGISTRY}/yendorcats/api:staging
    container_name: yendorcats-api-production
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - CONTAINERIZED_BUILD=true
      - ConnectionStrings__SqliteConnection=Data Source=/app/data/yendorcats.db
      
      # AWS/B2 Configuration
      - AWS__Region=${AWS_REGION}
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=${AWS_S3_ENDPOINT}
      - AWS__S3__PublicUrl=${AWS_S3_PUBLIC_URL}
      - AWS__S3__UseCdn=true
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      
      # Hybrid Storage Configuration
      - HybridStorage__DefaultProvider=S3
      - HybridStorage__EnableDualStorage=true
      - HybridStorage__S3__BucketName=${AWS_S3_BUCKET_NAME}
      - HybridStorage__S3__Region=${AWS_REGION}
      - HybridStorage__S3__ServiceUrl=${AWS_S3_ENDPOINT}
      - HybridStorage__S3__PublicUrl=${AWS_S3_PUBLIC_URL}
      - HybridStorage__S3__UseDirectUrls=true
      - HybridStorage__B2__BucketName=${B2_BUCKET_NAME}
      - HybridStorage__B2__ApplicationKeyId=${B2_APPLICATION_KEY_ID}
      - HybridStorage__B2__ApplicationKey=${B2_APPLICATION_KEY}
      - HybridStorage__B2__BucketId=${B2_BUCKET_ID}
      
      # JWT Configuration
      - JwtSettings__Secret=${YENDOR_JWT_SECRET}
      - JwtSettings__Issuer=YendorCatsApi
      - JwtSettings__Audience=YendorCatsClients
      - JwtSettings__ExpiryMinutes=60
      - JwtSettings__RefreshExpiryDays=7
      
      # Seeding Configuration
      - Seeding__EnableSampleData=false
      - Seeding__ForceReseed=false
      - Seeding__EnableB2Sync=true
      
      # Logging
      - Serilog__MinimumLevel__Default=Warning
      - Serilog__MinimumLevel__Override__Microsoft=Error
      - Serilog__MinimumLevel__Override__System=Error
    volumes:
      - api-data-production:/app/data
      - api-logs-production:/app/Logs
    networks:
      - yendorcats-production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # File Uploader Service - Node.js microservice
  uploader:
    image: ${ECR_REGISTRY}/yendorcats/uploader:staging
    container_name: yendorcats-uploader-production
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - NODE_ENV=production
      - PORT=80
      
      # AWS/B2 Configuration
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
      - AWS_S3_REGION=${AWS_REGION}
      - AWS_S3_ENDPOINT=${AWS_S3_ENDPOINT}
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      
      # API Configuration
      - API_BASE_URL=http://api:80
    depends_on:
      - api
    volumes:
      - uploader-temp-production:/app/uploads
    networks:
      - yendorcats-production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

networks:
  yendorcats-production:
    driver: bridge
    name: yendorcats-production-network

volumes:
  api-data-production:
    name: yendorcats-api-data-production
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/yendorcats/data
  api-logs-production:
    name: yendorcats-api-logs-production
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/yendorcats/logs
  uploader-temp-production:
    name: yendorcats-uploader-temp-production

# Production deployment notes:
# 1. Replace ${ECR_REGISTRY} with your actual ECR registry URL
# 2. Create /opt/yendorcats/data and /opt/yendorcats/logs directories on host
# 3. Set proper permissions: sudo chown -R 1000:1000 /opt/yendorcats/
# 4. Configure SSL/TLS termination (nginx proxy, ALB, or CloudFront)
# 5. Set up log rotation for persistent volumes
# 6. Configure backup strategy for database volume
# 7. Use secrets management for sensitive environment variables
# 8. Set up monitoring and alerting
# 9. Configure auto-scaling if using orchestration platform
