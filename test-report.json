{"summary": {"totalTests": 35, "passed": 33, "failed": 2, "successRate": "94.3", "duration": 0.036, "timestamp": "2025-07-17T21:07:59.859Z"}, "details": [{"status": "FAIL", "message": "Admin login successful"}, {"status": "FAIL", "message": "Failed to retrieve admin token"}, {"status": "PASS", "message": "Admin metadata editor HTML file exists"}, {"status": "PASS", "message": "Admin JavaScript file exists"}, {"status": "PASS", "message": "Tab navigation present"}, {"status": "PASS", "message": "Overview tab present"}, {"status": "PASS", "message": "Cat profiles tab present"}, {"status": "PASS", "message": "Photo management tab present"}, {"status": "PASS", "message": "Bulk operations tab present"}, {"status": "PASS", "message": "Pedigree builder tab present"}, {"status": "PASS", "message": "Cat name field present"}, {"status": "PASS", "message": "Bloodline field present"}, {"status": "PASS", "message": "Litter management present"}, {"status": "PASS", "message": "Bulk metadata function present"}, {"status": "PASS", "message": "Quick stats function present"}, {"status": "PASS", "message": "Link to metadata editor present"}, {"status": "PASS", "message": "Metadata field 'catName' present in controller"}, {"status": "PASS", "message": "Metadata field 'breed' present in controller"}, {"status": "PASS", "message": "Metadata field 'bloodline' present in controller"}, {"status": "PASS", "message": "Metadata field 'catId' present in controller"}, {"status": "PASS", "message": "Metadata field 'registeredName' present in controller"}, {"status": "PASS", "message": "Metadata field 'registrationNumber' present in controller"}, {"status": "PASS", "message": "Metadata field 'fatherId' present in controller"}, {"status": "PASS", "message": "Metadata field 'motherId' present in controller"}, {"status": "PASS", "message": "Metadata field 'breedingStatus' present in controller"}, {"status": "PASS", "message": "Metadata field 'availabilityStatus' present in controller"}, {"status": "PASS", "message": "Metadata field 'photoType' present in controller"}, {"status": "PASS", "message": "Metadata field 'ageAtPhoto' present in controller"}, {"status": "PASS", "message": "Metadata field 'tags' present in controller"}, {"status": "PASS", "message": "Metadata field 'champion<PERSON><PERSON><PERSON>' present in controller"}, {"status": "PASS", "message": "Metadata field 'generationLevel' present in controller"}, {"status": "PASS", "message": "Bloodline tracking implemented"}, {"status": "PASS", "message": "Paternal lineage tracking implemented"}, {"status": "PASS", "message": "Maternal lineage tracking implemented"}, {"status": "PASS", "message": "Champion title tracking implemented"}]}