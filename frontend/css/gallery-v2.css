/**
 * Yendor Cats Gallery V2 - High-Performance Gallery Styles
 * Modern responsive design with performance optimizations
 * Target: 85-90% performance improvement with excellent UX
 */

/* ===============================
   CSS CUSTOM PROPERTIES (VARIABLES)
   =============================== */
:root {
    /* Primary Colors */
    --primary-color: #2c3e50;
    --primary-light: #34495e;
    --primary-dark: #1a252f;
    
    /* Secondary Colors */
    --secondary-color: #3498db;
    --secondary-light: #5dade2;
    --secondary-dark: #2980b9;
    
    /* Accent Colors */
    --accent-color: #e74c3c;
    --accent-light: #ec7063;
    --accent-dark: #c0392b;
    
    /* Neutral Colors */
    --bg-color: #ffffff;
    --bg-light: #f8f9fa;
    --bg-medium: #e9ecef;
    --bg-dark: #dee2e6;
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --text-light: #ffffff;
    
    /* Status Colors */
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --info-color: #3498db;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    
    /* Grid Settings */
    --grid-columns: repeat(auto-fill, minmax(300px, 1fr));
    --grid-gap: var(--spacing-lg);
    
    /* Performance Optimizations */
    --gpu-acceleration: translateZ(0);
    --will-change: transform, opacity;
}

/* ===============================
   PERFORMANCE OPTIMIZATIONS
   =============================== */
.gallery-container {
    /* Enable GPU acceleration */
    transform: var(--gpu-acceleration);
    
    /* Hint browser about upcoming changes */
    will-change: contents;
    
    /* Optimize repaints */
    contain: layout style paint;
}

.gallery-grid-v2 {
    /* Enable GPU acceleration for grid */
    transform: var(--gpu-acceleration);
    will-change: var(--will-change);
    
    /* Optimize layout calculations */
    contain: layout style;
}

.gallery-image {
    /* Optimize image rendering */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    
    /* Enable GPU acceleration */
    transform: var(--gpu-acceleration);
    will-change: var(--will-change);
    
    /* Optimize compositing */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* ===============================
   LAYOUT COMPONENTS
   =============================== */
.gallery-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: var(--bg-color);
    min-height: 100vh;
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--bg-medium);
}

.gallery-title {
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gallery-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin: var(--spacing-sm) 0 0 0;
    font-weight: 400;
}

/* ===============================
   GALLERY STATS
   =============================== */
.gallery-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-medium) 100%);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===============================
   GALLERY GRID
   =============================== */
.gallery-grid-v2 {
    display: grid;
    grid-template-columns: var(--grid-columns);
    gap: var(--grid-gap);
    margin-bottom: var(--spacing-xl);
    
    /* Performance optimizations */
    contain: layout style;
}

.gallery-item-v2 {
    background: var(--bg-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    
    /* Performance optimizations */
    contain: layout style paint;
    will-change: var(--will-change);
}

.gallery-item-v2:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.gallery-item-v2:active {
    transform: translateY(-2px);
}

/* ===============================
   GALLERY ITEM MEDIA
   =============================== */
.gallery-item-media {
    position: relative;
    overflow: hidden;
    aspect-ratio: 4/3;
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--bg-medium) 100%);
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
    cursor: pointer;
    
    /* Performance optimizations */
    transform: var(--gpu-acceleration);
    will-change: var(--will-change);
}

.gallery-image:hover {
    transform: scale(1.05);
}

.gallery-image.lazy-load {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.gallery-image.loaded {
    opacity: 1;
}

.gallery-image.fallback {
    filter: grayscale(20%);
    opacity: 0.8;
}

.gallery-image.error {
    background: var(--bg-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.gallery-image.error::before {
    content: "🖼️";
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
}

/* ===============================
   GALLERY ITEM OVERLAY
   =============================== */
.gallery-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(44, 62, 80, 0) 0%,
        rgba(44, 62, 80, 0.1) 50%,
        rgba(44, 62, 80, 0.3) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
    
    /* Performance optimizations */
    will-change: opacity;
}

.gallery-item-v2:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.gallery-item-actions button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
    
    /* Performance optimizations */
    will-change: var(--will-change);
}

.gallery-item-actions button:hover {
    background: var(--bg-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.gallery-item-actions button:active {
    transform: scale(0.95);
}

.gallery-item-actions button i {
    font-size: 16px;
    color: var(--text-primary);
}

/* ===============================
   GALLERY ITEM LOADING
   =============================== */
.gallery-item-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    z-index: 10;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--bg-medium);
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    
    /* Performance optimizations */
    will-change: var(--will-change);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===============================
   GALLERY ITEM DETAILS
   =============================== */
.gallery-item-details {
    padding: var(--spacing-lg);
}

.gallery-item-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
    line-height: 1.3;
    
    /* Text overflow handling */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.gallery-item-meta {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.cat-info {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.age-info,
.date-info {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.gallery-item-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: var(--spacing-sm) 0;
    
    /* Multi-line text overflow */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.gallery-item-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
}

.tag {
    display: inline-block;
    background: var(--secondary-color);
    color: var(--text-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.7rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    transition: var(--transition-fast);
    
    /* Performance optimizations */
    will-change: var(--will-change);
}

.tag:hover {
    background: var(--secondary-dark);
    transform: translateY(-1px);
}

/* ===============================
   PAGINATION
   =============================== */
.gallery-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-lg);
    background: var(--bg-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.btn-page {
    background: var(--bg-color);
    border: 2px solid var(--bg-medium);
    border-radius: var(--border-radius-md);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 14px;
    color: var(--text-primary);
    
    /* Performance optimizations */
    will-change: var(--will-change);
}

.btn-page:hover:not(:disabled) {
    background: var(--secondary-color);
    color: var(--text-light);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-page:active:not(:disabled) {
    transform: translateY(0);
}

.btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--bg-medium);
    color: var(--text-muted);
}

.page-info {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0 var(--spacing-sm);
}

/* ===============================
   ERROR STATES
   =============================== */
.gallery-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl);
    text-align: center;
    background: var(--bg-light);
    border-radius: var(--border-radius-lg);
    border: 2px dashed var(--bg-dark);
    min-height: 400px;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.error-message {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    line-height: 1.5;
}

.btn-retry {
    background: var(--secondary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
    
    /* Performance optimizations */
    will-change: var(--will-change);
}

.btn-retry:hover {
    background: var(--secondary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-retry:active {
    transform: translateY(0);
}

/* ===============================
   RESPONSIVE DESIGN
   =============================== */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    :root {
        --grid-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    
    .gallery-container {
        max-width: 1400px;
    }
    
    .gallery-title {
        font-size: 3rem;
    }
}

/* Medium screens (768px to 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
    :root {
        --grid-columns: repeat(auto-fill, minmax(280px, 1fr));
        --grid-gap: var(--spacing-md);
    }
    
    .gallery-stats {
        flex-direction: row;
        gap: var(--spacing-md);
    }
    
    .gallery-title {
        font-size: 2.2rem;
    }
    
    .gallery-item-details {
        padding: var(--spacing-md);
    }
}

/* Small screens (576px to 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    :root {
        --grid-columns: repeat(auto-fill, minmax(250px, 1fr));
        --grid-gap: var(--spacing-md);
    }
    
    .gallery-container {
        padding: var(--spacing-md);
    }
    
    .gallery-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .gallery-title {
        font-size: 2rem;
    }
    
    .gallery-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
}

/* Extra small screens (up to 575px) */
@media (max-width: 575px) {
    :root {
        --grid-columns: 1fr;
        --grid-gap: var(--spacing-md);
    }
    
    .gallery-container {
        padding: var(--spacing-sm);
    }
    
    .gallery-stats {
        flex-direction: column;
        gap: var(--spacing-xs);
        padding: var(--spacing-md);
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-xs) 0;
        border-bottom: 1px solid var(--bg-medium);
    }
    
    .stat-item:last-child {
        border-bottom: none;
    }
    
    .gallery-title {
        font-size: 1.8rem;
    }
    
    .gallery-subtitle {
        font-size: 1rem;
    }
    
    .gallery-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-lg);
    }
    
    .gallery-item-details {
        padding: var(--spacing-sm);
    }
    
    .gallery-item-title {
        font-size: 1.1rem;
    }
    
    .gallery-pagination {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .btn-page {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
}

/* ===============================
   DARK MODE SUPPORT
   =============================== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --bg-light: #2d2d2d;
        --bg-medium: #404040;
        --bg-dark: #595959;
        
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --text-muted: #999999;
        
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
        --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.3);
    }
    
    .gallery-image.error {
        background: var(--bg-medium);
        color: var(--text-muted);
    }
    
    .gallery-item-overlay {
        background: linear-gradient(
            45deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.1) 50%,
            rgba(0, 0, 0, 0.3) 100%
        );
    }
}

/* ===============================
   REDUCED MOTION SUPPORT
   =============================== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .gallery-image:hover {
        transform: none;
    }
    
    .gallery-item-v2:hover {
        transform: none;
    }
    
    .spinner {
        animation: none;
    }
}

/* ===============================
   HIGH CONTRAST MODE
   =============================== */
@media (prefers-contrast: high) {
    :root {
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.5);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.5);
        --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
    }
    
    .gallery-item-v2 {
        border: 2px solid var(--text-primary);
    }
    
    .btn-page {
        border-width: 3px;
    }
}

/* ===============================
   PRINT STYLES
   =============================== */
@media print {
    .gallery-container {
        max-width: none;
        padding: 0;
    }
    
    .gallery-stats,
    .gallery-pagination,
    .gallery-item-overlay,
    .gallery-item-actions {
        display: none !important;
    }
    
    .gallery-grid-v2 {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .gallery-item-v2 {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .gallery-image {
        transform: none !important;
    }
}
