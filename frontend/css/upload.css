/* Upload Page Styles */

/* Login link styling for upload page - less prominent */
.nav-menu .nav-item .nav-link.login-link {
    font-size: 0.9rem !important;
    color: rgba(0, 0, 0, 0.6) !important;
    font-weight: 400 !important;
    text-decoration: underline;
    padding: 0.3rem 0.5rem !important;
}

.nav-menu .nav-item .nav-link.login-link:hover {
    color: var(--accent-primary) !important;
    text-decoration: none;
}

/* Ensure proper spacing for the new navigation items */
.nav-menu {
    gap: 1rem;
}

.nav-item {
    margin: 0 0.5rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .nav-menu .nav-item .nav-link.login-link {
        font-size: 1rem !important;
        color: rgba(255, 255, 255, 0.8) !important;
    }
}

.upload-section {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

.upload-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.upload-description {
    text-align: center;
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* File Upload Area */
.file-upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: #f8f9fa;
    margin: 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover {
    border-color: #8B4513;
    background: #fff;
}

.file-upload-area.dragover {
    border-color: #8B4513;
    background: #fff8f0;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.file-upload-area h3 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.file-upload-area p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.choose-file-btn {
    background: #8B4513;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.choose-file-btn:hover {
    background: #6d3410;
}

/* Preview Area */
.preview-area {
    margin: 2rem;
    text-align: center;
    position: relative;
}

.preview-area img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.remove-file-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-file-btn:hover {
    background: #c82333;
}

/* Metadata Form */
.metadata-form {
    padding: 2rem;
    border-top: 1px solid #dee2e6;
}

.metadata-form h3 {
    color: #495057;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #495057;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #8B4513;
    box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Form Actions */
.form-actions {
    padding: 2rem;
    border-top: 1px solid #dee2e6;
    text-align: center;
}

.upload-btn {
    background: linear-gradient(135deg, #8B4513 0%, #6d3410 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-width: 150px;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(139, 69, 19, 0.3);
}

.upload-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-loading {
    display: none;
}

.upload-btn.loading .btn-text {
    display: none;
}

.upload-btn.loading .btn-loading {
    display: inline;
}

/* Upload Status */
.upload-status {
    margin: 2rem auto;
    max-width: 600px;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
}

.upload-status.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.upload-status.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.status-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.status-message {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.status-details {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Recent Uploads */
.recent-uploads {
    margin-top: 3rem;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recent-uploads h3 {
    color: #495057;
    margin-bottom: 1.5rem;
    text-align: center;
}

.uploads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.upload-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.upload-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.upload-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.upload-item-info {
    padding: 1rem;
}

.upload-item-info h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
    font-size: 1rem;
}

.upload-item-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-section {
        padding: 100px 0 60px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .file-upload-area {
        margin: 1rem;
        padding: 2rem 1rem;
    }
    
    .metadata-form {
        padding: 1.5rem;
    }
    
    .form-actions {
        padding: 1.5rem;
    }
    
    .uploads-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 480px) {
    .upload-container {
        margin: 0 1rem;
    }
    
    .file-upload-area {
        margin: 0.5rem;
        padding: 1.5rem 1rem;
    }
    
    .metadata-form {
        padding: 1rem;
    }
    
    .form-actions {
        padding: 1rem;
    }
    
    .upload-btn {
        width: 100%;
    }
}
