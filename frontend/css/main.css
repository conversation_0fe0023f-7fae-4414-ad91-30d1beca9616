/*
 * Yendor Cats - Main CSS File
 * Imports all CSS modules in the correct order
 */

/* Import variables first so they're available to all other modules */
@import 'variables.css';

/* Base styles and resets */
@import 'base.css';

/* Component styles */
@import 'components/buttons.css';
@import 'components/navbar.css';
@import 'components/footer.css';
@import 'components/carousel.css';

/* Section styles */
@import 'sections.css';

/* Add any additional CSS imports here */

/* --------------------------------------------------
 * Cat Carousel Navigation Buttons
 * -------------------------------------------------- */
.category-section {
    position: relative;
}

.carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
    transition: all 0.3s ease;
}

.carousel-nav:hover {
    background-color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.carousel-nav.prev {
    left: 10px;
}

.carousel-nav.next {
    right: 10px;
}

@media (max-width: 768px) {
    .carousel-nav {
        width: 35px;
        height: 35px;
        font-size: 1.2rem;
    }
}

/* --------------------------------------------------
 * Newsletter Signup Section
 * -------------------------------------------------- */
.newsletter-signup {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
}

.newsletter-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
    margin-top: 2rem;
}

.newsletter-benefits h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.newsletter-benefits ul {
    list-style: none;
    padding: 0;
}

.newsletter-benefits li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
    color: var(--text-secondary);
}

.newsletter-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.newsletter-form .form-group {
    margin-bottom: 1.5rem;
}

.newsletter-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.newsletter-form input,
.newsletter-form select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.newsletter-form input:focus,
.newsletter-form select:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.newsletter-submit {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 1rem;
}

.newsletter-privacy {
    margin-top: 1rem;
    text-align: center;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .newsletter-form {
        padding: 1.5rem;
    }
}