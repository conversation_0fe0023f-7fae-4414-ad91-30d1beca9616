# Frontend Dockerfile for YendorCats
# Multi-stage build for optimized production image

# Stage 1: Build stage (if needed for any build processes)
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json if it exists (for any build dependencies)
COPY package*.json ./

# Install any build dependencies
RUN npm install --only=production 2>/dev/null || echo "No package.json found, skipping npm install"

# Copy all frontend files except nginx.conf and Dockerfile
COPY . .
RUN rm -f nginx.conf Dockerfile 2>/dev/null || true

# Stage 2: Production stage with nginx
FROM nginx:alpine AS production

# Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*

# Copy frontend files to nginx html directory
COPY --from=build /app /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create directory for nginx logs
RUN mkdir -p /var/log/nginx

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
