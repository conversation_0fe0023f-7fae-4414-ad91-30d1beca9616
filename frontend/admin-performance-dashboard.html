<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Dashboard - Yendor Cats</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-card.performance { border-left-color: #3498db; }
        .metric-card.cache { border-left-color: #2ecc71; }
        .metric-card.migration { border-left-color: #f39c12; }
        .metric-card.database { border-left-color: #e74c3c; }

        .metric-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .metric-value.good { color: #27ae60; }
        .metric-value.warning { color: #f39c12; }
        .metric-value.critical { color: #e74c3c; }

        .metric-description {
            color: #7f8c8d;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .progress-bar {
            background: #ecf0f1;
            border-radius: 10px;
            height: 8px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .progress-fill.good { background: #27ae60; }
        .progress-fill.warning { background: #f39c12; }
        .progress-fill.critical { background: #e74c3c; }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .status-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .status-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            color: white;
        }

        .status-icon.complete { background: #27ae60; }
        .status-icon.in-progress { background: #f39c12; }
        .status-icon.blocked { background: #e74c3c; }

        .controls {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .controls h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .log-viewer {
            background: #2c3e50;
            border-radius: 8px;
            padding: 20px;
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
        }

        .log-timestamp {
            color: #95a5a6;
        }

        .log-level-info { color: #3498db; }
        .log-level-success { color: #27ae60; }
        .log-level-warning { color: #f39c12; }
        .log-level-error { color: #e74c3c; }

        @media (max-width: 768px) {
            .dashboard {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚀 Performance Dashboard</h1>
            <p class="subtitle">Critical Infrastructure Upgrade - S3 to Database Migration</p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card performance">
                <div class="metric-title">Database Performance Improvement</div>
                <div class="metric-value good" id="performance-improvement">85%</div>
                <div class="progress-bar">
                    <div class="progress-fill good" style="width: 85%"></div>
                </div>
                <div class="metric-description">Expected improvement from S3 metadata to database queries</div>
            </div>

            <div class="metric-card cache">
                <div class="metric-title">Cache Hit Rate</div>
                <div class="metric-value good" id="cache-hit-rate">--</div>
                <div class="progress-bar">
                    <div class="progress-fill good" style="width: 0%" id="cache-progress"></div>
                </div>
                <div class="metric-description">Smart cache system active - monitoring performance</div>
            </div>

            <div class="metric-card migration">
                <div class="metric-title">Migration Progress</div>
                <div class="metric-value warning" id="migration-progress">75%</div>
                <div class="progress-bar">
                    <div class="progress-fill warning" style="width: 75%"></div>
                </div>
                <div class="metric-description">Database migration and infrastructure setup</div>
            </div>

            <div class="metric-card database">
                <div class="metric-title">Database Status</div>
                <div class="metric-value warning" id="database-status">READY</div>
                <div class="progress-bar">
                    <div class="progress-fill warning" style="width: 90%"></div>
                </div>
                <div class="metric-description">Schema updated, migration in progress</div>
            </div>
        </div>

        <div class="status-grid">
            <div class="status-item">
                <div class="status-icon complete">✓</div>
                <div>
                    <strong>Backend Models</strong><br>
                    <small>CatGalleryImage model aligned with database schema</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon complete">✓</div>
                <div>
                    <strong>Repository Pattern</strong><br>
                    <small>Data access layer implemented</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon complete">✓</div>
                <div>
                    <strong>Migration Services</strong><br>
                    <small>S3 to database migration infrastructure</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon complete">✓</div>
                <div>
                    <strong>API Controllers</strong><br>
                    <small>GalleryV2 and AdminGallery endpoints</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon complete">✓</div>
                <div>
                    <strong>Performance Monitor</strong><br>
                    <small>Real-time monitoring system active</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon complete">✓</div>
                <div>
                    <strong>Cache Manager</strong><br>
                    <small>Intelligent caching with TTL management</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon in-progress">⏳</div>
                <div>
                    <strong>Database Migration</strong><br>
                    <small>EF Core migration in progress</small>
                </div>
            </div>
            <div class="status-item">
                <div class="status-icon in-progress">⏳</div>
                <div>
                    <strong>Integration Testing</strong><br>
                    <small>End-to-end validation pending</small>
                </div>
            </div>
        </div>

        <div class="controls">
            <h3>🔧 Management Controls</h3>
            <div class="control-buttons">
                <button class="btn btn-primary" onclick="runMigration()">🔄 Run Migration</button>
                <button class="btn btn-success" onclick="validateSystem()">✅ Validate System</button>
                <button class="btn btn-warning" onclick="clearCache()">🗑️ Clear Cache</button>
                <button class="btn btn-danger" onclick="rollbackMigration()">⬅️ Rollback</button>
                <a href="gallery-v2.html" class="btn btn-primary">🖼️ Test Gallery V2</a>
                <a href="admin-metadata-editor.html" class="btn btn-warning">⚙️ Metadata Editor</a>
            </div>
        </div>

        <div id="system-logs">
            <h3>📋 System Logs</h3>
            <div class="log-viewer" id="log-container">
                <div class="log-entry">
                    <span class="log-timestamp">[2025-01-18 13:03:00]</span>
                    <span class="log-level-info">[INFO]</span>
                    Dashboard initialized successfully
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[2025-01-18 13:03:01]</span>
                    <span class="log-level-success">[SUCCESS]</span>
                    CatGalleryImage model schema validation passed
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[2025-01-18 13:03:02]</span>
                    <span class="log-level-info">[INFO]</span>
                    Performance monitoring system active
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">[2025-01-18 13:03:03]</span>
                    <span class="log-level-warning">[WARNING]</span>
                    Database migration in progress - EF Core creating tables
                </div>
            </div>
        </div>
    </div>

    <script src="js/performance-monitor.js"></script>
    <script src="js/cache-manager.js"></script>
    <script src="js/api-optimization.js"></script>
    <script>
        // Initialize performance dashboard
        class PerformanceDashboard {
            constructor() {
                this.performanceMonitor = new PerformanceMonitor();
                this.cacheManager = new CacheManager();
                this.apiOptimizer = new APIOptimizer();
                this.initializeRealTimeUpdates();
            }

            initializeRealTimeUpdates() {
                setInterval(() => this.updateMetrics(), 5000);
                setInterval(() => this.updateCacheMetrics(), 2000);
                this.logMessage('Dashboard real-time updates initialized', 'info');
            }

            updateMetrics() {
                // Update performance metrics
                const performanceData = this.performanceMonitor.getMetrics();
                if (performanceData) {
                    this.updateProgressBar('cache-progress', performanceData.cacheHitRate);
                    document.getElementById('cache-hit-rate').textContent = 
                        `${performanceData.cacheHitRate.toFixed(1)}%`;
                }
            }

            updateCacheMetrics() {
                const cacheStats = this.cacheManager.getStats();
                if (cacheStats.hitRate > 0) {
                    document.getElementById('cache-hit-rate').textContent = 
                        `${cacheStats.hitRate.toFixed(1)}%`;
                    this.updateProgressBar('cache-progress', cacheStats.hitRate);
                }
            }

            updateProgressBar(id, percentage) {
                const progressBar = document.getElementById(id);
                if (progressBar) {
                    progressBar.style.width = `${percentage}%`;
                    
                    // Update color based on performance
                    progressBar.className = 'progress-fill ' + 
                        (percentage >= 80 ? 'good' : 
                         percentage >= 60 ? 'warning' : 'critical');
                }
            }

            logMessage(message, level = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logContainer = document.getElementById('log-container');
                
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span>
                    <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                    ${message}
                `;
                
                logContainer.insertBefore(logEntry, logContainer.firstChild);
                
                // Keep only last 20 log entries
                const entries = logContainer.querySelectorAll('.log-entry');
                if (entries.length > 20) {
                    entries[entries.length - 1].remove();
                }
            }
        }

        // Control functions
        function runMigration() {
            dashboard.logMessage('Starting database migration...', 'warning');
            
            fetch('/api/migration/start', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    dashboard.logMessage(`Migration started: ${data.message}`, 'success');
                    updateMigrationProgress(10);
                })
                .catch(error => {
                    dashboard.logMessage(`Migration failed: ${error.message}`, 'error');
                });
        }

        function validateSystem() {
            dashboard.logMessage('Running system validation...', 'info');
            
            fetch('/api/system/validate')
                .then(response => response.json())
                .then(data => {
                    dashboard.logMessage(`Validation complete: ${data.status}`, 'success');
                })
                .catch(error => {
                    dashboard.logMessage(`Validation failed: ${error.message}`, 'error');
                });
        }

        function clearCache() {
            dashboard.logMessage('Clearing all caches...', 'warning');
            dashboard.cacheManager.clearAll();
            dashboard.logMessage('Cache cleared successfully', 'success');
        }

        function rollbackMigration() {
            if (confirm('Are you sure you want to rollback the migration? This will revert to S3-only metadata.')) {
                dashboard.logMessage('Starting migration rollback...', 'warning');
                
                fetch('/api/migration/rollback', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        dashboard.logMessage(`Rollback complete: ${data.message}`, 'success');
                    })
                    .catch(error => {
                        dashboard.logMessage(`Rollback failed: ${error.message}`, 'error');
                    });
            }
        }

        function updateMigrationProgress(progress) {
            document.getElementById('migration-progress').textContent = `${progress}%`;
            const progressBar = document.querySelector('.metric-card.migration .progress-fill');
            progressBar.style.width = `${progress}%`;
        }

        // Initialize dashboard
        const dashboard = new PerformanceDashboard();
        
        // Initial system check
        dashboard.logMessage('Performance dashboard loaded successfully', 'success');
        dashboard.logMessage('Monitoring S3 to Database migration performance', 'info');
    </script>
</body>
</html>
