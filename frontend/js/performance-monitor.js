/**
 * Yendor Cats Gallery V2 - Performance Monitor
 * Real-time performance tracking and optimization suggestions
 * Monitors API response times, cache efficiency, and user experience metrics
 */

class PerformanceMonitor {
    constructor(options = {}) {
        this.options = {
            trackingEnabled: options.trackingEnabled !== false,
            alertThresholds: {
                apiResponseTime: options.apiResponseTimeThreshold || 1000, // ms
                cacheHitRate: options.cacheHitRateThreshold || 70, // percentage
                imageLoadTime: options.imageLoadTimeThreshold || 2000, // ms
                memoryUsage: options.memoryUsageThreshold || 50, // MB
                errorRate: options.errorRateThreshold || 5, // percentage
                ...options.alertThresholds
            },
            sampleInterval: options.sampleInterval || 5000, // ms
            maxSamples: options.maxSamples || 100,
            enableAnalytics: options.enableAnalytics !== false,
            enableAlerts: options.enableAlerts !== false,
            enableReporting: options.enableReporting !== false,
            reportInterval: options.reportInterval || 60000, // 1 minute
            ...options
        };

        // Performance metrics storage
        this.metrics = {
            // API Performance
            apiCalls: [],
            apiErrors: [],
            apiResponseTimes: [],
            
            // Cache Performance
            cacheHitRates: [],
            cacheOperations: [],
            
            // Image Loading
            imageLoadTimes: [],
            imageErrors: [],
            
            // User Experience
            pageLoadTimes: [],
            interactionDelays: [],
            
            // System Resources
            memoryUsage: [],
            networkConditions: [],
            
            // Custom Metrics
            customMetrics: new Map(),
            
            // Timestamps
            startTime: performance.now(),
            lastSample: Date.now()
        };

        // Performance observers
        this.observers = {
            navigation: null,
            resource: null,
            measure: null,
            longTask: null,
            layout: null
        };

        // Alert system
        this.alerts = {
            active: [],
            history: [],
            suppressions: new Set()
        };

        // Event system
        this.eventListeners = new Map();

        // Chart data for visualization
        this.chartData = {
            apiResponseTimes: [],
            cacheHitRates: [],
            imageLoadTimes: [],
            memoryUsage: [],
            timestamps: []
        };

        // Initialize performance monitoring
        this.init();
    }

    /**
     * Initialize performance monitoring
     */
    init() {
        if (!this.options.trackingEnabled) {
            console.warn('Performance monitoring disabled');
            return;
        }

        try {
            this.setupPerformanceObservers();
            this.startSampling();
            this.setupNetworkMonitoring();
            this.setupMemoryMonitoring();
            this.setupUserExperienceTracking();
            
            if (this.options.enableReporting) {
                this.startReporting();
            }

            console.log('Performance Monitor initialized successfully');
            this.emit('monitor:initialized', { startTime: Date.now() });

        } catch (error) {
            console.error('Performance Monitor initialization failed:', error);
            this.trackError('initialization', error);
        }
    }

    /**
     * Setup Performance API observers
     */
    setupPerformanceObservers() {
        // Navigation Timing Observer
        if (window.PerformanceObserver) {
            try {
                this.observers.navigation = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'navigation') {
                            this.trackNavigationTiming(entry);
                        }
                    });
                });
                this.observers.navigation.observe({ entryTypes: ['navigation'] });
            } catch (error) {
                console.warn('Navigation observer not supported:', error);
            }

            // Resource Timing Observer
            try {
                this.observers.resource = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'resource') {
                            this.trackResourceTiming(entry);
                        }
                    });
                });
                this.observers.resource.observe({ entryTypes: ['resource'] });
            } catch (error) {
                console.warn('Resource observer not supported:', error);
            }

            // Measure Observer
            try {
                this.observers.measure = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'measure') {
                            this.trackCustomMeasure(entry);
                        }
                    });
                });
                this.observers.measure.observe({ entryTypes: ['measure'] });
            } catch (error) {
                console.warn('Measure observer not supported:', error);
            }

            // Long Task Observer
            try {
                this.observers.longTask = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'longtask') {
                            this.trackLongTask(entry);
                        }
                    });
                });
                this.observers.longTask.observe({ entryTypes: ['longtask'] });
            } catch (error) {
                console.warn('Long task observer not supported:', error);
            }

            // Layout Shift Observer
            try {
                this.observers.layout = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'layout-shift') {
                            this.trackLayoutShift(entry);
                        }
                    });
                });
                this.observers.layout.observe({ entryTypes: ['layout-shift'] });
            } catch (error) {
                console.warn('Layout shift observer not supported:', error);
            }
        }
    }

    /**
     * Start performance sampling
     */
    startSampling() {
        setInterval(() => {
            this.collectSample();
        }, this.options.sampleInterval);
    }

    /**
     * Collect performance sample
     */
    collectSample() {
        const now = Date.now();
        const timestamp = now;

        // Collect system metrics
        this.collectMemoryUsage();
        this.collectNetworkConditions();
        
        // Update chart data
        this.updateChartData(timestamp);
        
        // Check for alerts
        if (this.options.enableAlerts) {
            this.checkAlerts();
        }
        
        // Cleanup old samples
        this.cleanupOldSamples();
        
        this.metrics.lastSample = now;
        this.emit('monitor:sample', { timestamp, metrics: this.getMetrics() });
    }

    /**
     * Track API performance
     */
    trackApiCall(url, method, startTime, endTime, status, error = null) {
        const duration = endTime - startTime;
        const entry = {
            url,
            method,
            duration,
            status,
            error,
            timestamp: Date.now(),
            success: status >= 200 && status < 300
        };

        this.metrics.apiCalls.push(entry);
        this.metrics.apiResponseTimes.push(duration);

        if (error || status >= 400) {
            this.metrics.apiErrors.push(entry);
        }

        // Performance mark for visualization
        performance.mark(`api-call-${url}-${Date.now()}`);

        this.emit('monitor:api-call', entry);
        
        // Check for performance threshold
        if (duration > this.options.alertThresholds.apiResponseTime) {
            this.createAlert('api-response-time', 
                `API call to ${url} took ${duration}ms (threshold: ${this.options.alertThresholds.apiResponseTime}ms)`,
                'warning'
            );
        }
    }

    /**
     * Track cache performance
     */
    trackCacheOperation(operation, key, hit, duration = 0) {
        const entry = {
            operation,
            key,
            hit,
            duration,
            timestamp: Date.now()
        };

        this.metrics.cacheOperations.push(entry);
        this.emit('monitor:cache-operation', entry);
    }

    /**
     * Track image loading performance
     */
    trackImageLoad(url, loadTime, width, height, fromCache = false) {
        const entry = {
            url,
            loadTime,
            width,
            height,
            fromCache,
            timestamp: Date.now()
        };

        this.metrics.imageLoadTimes.push(loadTime);
        this.emit('monitor:image-load', entry);

        // Check for performance threshold
        if (loadTime > this.options.alertThresholds.imageLoadTime) {
            this.createAlert('image-load-time',
                `Image ${url} took ${loadTime}ms to load (threshold: ${this.options.alertThresholds.imageLoadTime}ms)`,
                'warning'
            );
        }
    }

    /**
     * Track image loading errors
     */
    trackImageError(url, error) {
        const entry = {
            url,
            error: error.message || error,
            timestamp: Date.now()
        };

        this.metrics.imageErrors.push(entry);
        this.emit('monitor:image-error', entry);
    }

    /**
     * Track user interactions
     */
    trackUserInteraction(type, element, delay = 0) {
        const entry = {
            type,
            element: element.tagName || element,
            delay,
            timestamp: Date.now()
        };

        this.metrics.interactionDelays.push(delay);
        this.emit('monitor:user-interaction', entry);
    }

    /**
     * Track navigation timing
     */
    trackNavigationTiming(entry) {
        const timing = {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            load: entry.loadEventEnd - entry.loadEventStart,
            domInteractive: entry.domInteractive - entry.navigationStart,
            domComplete: entry.domComplete - entry.navigationStart,
            timestamp: Date.now()
        };

        this.metrics.pageLoadTimes.push(timing.domComplete);
        this.emit('monitor:navigation', timing);
    }

    /**
     * Track resource timing
     */
    trackResourceTiming(entry) {
        const timing = {
            name: entry.name,
            duration: entry.duration,
            transferSize: entry.transferSize,
            encodedBodySize: entry.encodedBodySize,
            decodedBodySize: entry.decodedBodySize,
            timestamp: Date.now()
        };

        this.emit('monitor:resource', timing);
    }

    /**
     * Track custom measures
     */
    trackCustomMeasure(entry) {
        const measure = {
            name: entry.name,
            duration: entry.duration,
            timestamp: Date.now()
        };

        if (!this.metrics.customMetrics.has(entry.name)) {
            this.metrics.customMetrics.set(entry.name, []);
        }
        this.metrics.customMetrics.get(entry.name).push(measure);

        this.emit('monitor:custom-measure', measure);
    }

    /**
     * Track long tasks
     */
    trackLongTask(entry) {
        const task = {
            duration: entry.duration,
            startTime: entry.startTime,
            timestamp: Date.now()
        };

        this.createAlert('long-task',
            `Long task detected: ${entry.duration}ms`,
            'warning'
        );

        this.emit('monitor:long-task', task);
    }

    /**
     * Track layout shifts
     */
    trackLayoutShift(entry) {
        const shift = {
            value: entry.value,
            sources: entry.sources,
            timestamp: Date.now()
        };

        this.emit('monitor:layout-shift', shift);
    }

    /**
     * Track custom metrics
     */
    trackCustomMetric(name, value, tags = {}) {
        const metric = {
            name,
            value,
            tags,
            timestamp: Date.now()
        };

        if (!this.metrics.customMetrics.has(name)) {
            this.metrics.customMetrics.set(name, []);
        }
        this.metrics.customMetrics.get(name).push(metric);

        this.emit('monitor:custom-metric', metric);
    }

    /**
     * Track errors
     */
    trackError(type, error, context = {}) {
        const errorEntry = {
            type,
            message: error.message || error,
            stack: error.stack,
            context,
            timestamp: Date.now()
        };

        this.createAlert('error',
            `${type}: ${error.message || error}`,
            'error'
        );

        this.emit('monitor:error', errorEntry);
    }

    /**
     * Setup network monitoring
     */
    setupNetworkMonitoring() {
        // Monitor network connection
        if (navigator.connection) {
            const updateNetworkInfo = () => {
                const networkInfo = {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt,
                    saveData: navigator.connection.saveData,
                    timestamp: Date.now()
                };

                this.metrics.networkConditions.push(networkInfo);
                this.emit('monitor:network', networkInfo);
            };

            navigator.connection.addEventListener('change', updateNetworkInfo);
            updateNetworkInfo(); // Initial reading
        }
    }

    /**
     * Setup memory monitoring
     */
    setupMemoryMonitoring() {
        if (performance.memory) {
            setInterval(() => {
                this.collectMemoryUsage();
            }, this.options.sampleInterval);
        }
    }

    /**
     * Collect memory usage
     */
    collectMemoryUsage() {
        if (performance.memory) {
            const memoryInfo = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };

            this.metrics.memoryUsage.push(memoryInfo);

            // Check memory threshold
            const usedMB = memoryInfo.used / 1024 / 1024;
            if (usedMB > this.options.alertThresholds.memoryUsage) {
                this.createAlert('memory-usage',
                    `High memory usage: ${usedMB.toFixed(2)}MB (threshold: ${this.options.alertThresholds.memoryUsage}MB)`,
                    'warning'
                );
            }

            this.emit('monitor:memory', memoryInfo);
        }
    }

    /**
     * Collect network conditions
     */
    collectNetworkConditions() {
        if (navigator.connection) {
            const networkInfo = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData,
                timestamp: Date.now()
            };

            // Only add if conditions changed
            const lastCondition = this.metrics.networkConditions[this.metrics.networkConditions.length - 1];
            if (!lastCondition || 
                lastCondition.effectiveType !== networkInfo.effectiveType ||
                Math.abs(lastCondition.downlink - networkInfo.downlink) > 0.1) {
                this.metrics.networkConditions.push(networkInfo);
            }
        }
    }

    /**
     * Setup user experience tracking
     */
    setupUserExperienceTracking() {
        // Track page visibility changes
        document.addEventListener('visibilitychange', () => {
            this.trackCustomMetric('page-visibility', document.hidden ? 'hidden' : 'visible');
        });

        // Track scroll performance
        let scrolling = false;
        window.addEventListener('scroll', () => {
            if (!scrolling) {
                scrolling = true;
                const startTime = performance.now();
                
                requestAnimationFrame(() => {
                    const scrollTime = performance.now() - startTime;
                    this.trackCustomMetric('scroll-performance', scrollTime);
                    scrolling = false;
                });
            }
        });

        // Track click delays
        document.addEventListener('click', (e) => {
            const clickTime = performance.now();
            requestAnimationFrame(() => {
                const delay = performance.now() - clickTime;
                this.trackUserInteraction('click', e.target, delay);
            });
        });
    }

    /**
     * Create performance alert
     */
    createAlert(type, message, severity = 'info') {
        const alert = {
            id: Date.now() + Math.random(),
            type,
            message,
            severity,
            timestamp: Date.now(),
            acknowledged: false
        };

        // Check if alert is suppressed
        if (this.alerts.suppressions.has(type)) {
            return;
        }

        this.alerts.active.push(alert);
        this.alerts.history.push(alert);

        this.emit('monitor:alert', alert);

        // Auto-dismiss info alerts after 30 seconds
        if (severity === 'info') {
            setTimeout(() => {
                this.dismissAlert(alert.id);
            }, 30000);
        }
    }

    /**
     * Dismiss alert
     */
    dismissAlert(alertId) {
        const index = this.alerts.active.findIndex(alert => alert.id === alertId);
        if (index > -1) {
            this.alerts.active[index].acknowledged = true;
            this.alerts.active.splice(index, 1);
            this.emit('monitor:alert-dismissed', { id: alertId });
        }
    }

    /**
     * Suppress alert type
     */
    suppressAlert(type, duration = 300000) { // 5 minutes default
        this.alerts.suppressions.add(type);
        setTimeout(() => {
            this.alerts.suppressions.delete(type);
        }, duration);
    }

    /**
     * Check for performance alerts
     */
    checkAlerts() {
        // Check API response time
        if (this.metrics.apiResponseTimes.length > 0) {
            const recentTimes = this.metrics.apiResponseTimes.slice(-10);
            const avgResponseTime = recentTimes.reduce((a, b) => a + b, 0) / recentTimes.length;
            
            if (avgResponseTime > this.options.alertThresholds.apiResponseTime) {
                this.createAlert('api-response-time-avg',
                    `Average API response time: ${avgResponseTime.toFixed(2)}ms (threshold: ${this.options.alertThresholds.apiResponseTime}ms)`,
                    'warning'
                );
            }
        }

        // Check cache hit rate
        if (this.metrics.cacheOperations.length > 0) {
            const recentOps = this.metrics.cacheOperations.slice(-50);
            const hits = recentOps.filter(op => op.hit).length;
            const hitRate = (hits / recentOps.length) * 100;
            
            if (hitRate < this.options.alertThresholds.cacheHitRate) {
                this.createAlert('cache-hit-rate',
                    `Low cache hit rate: ${hitRate.toFixed(1)}% (threshold: ${this.options.alertThresholds.cacheHitRate}%)`,
                    'warning'
                );
            }
        }

        // Check error rate
        if (this.metrics.apiCalls.length > 0) {
            const recentCalls = this.metrics.apiCalls.slice(-50);
            const errors = recentCalls.filter(call => !call.success).length;
            const errorRate = (errors / recentCalls.length) * 100;
            
            if (errorRate > this.options.alertThresholds.errorRate) {
                this.createAlert('error-rate',
                    `High error rate: ${errorRate.toFixed(1)}% (threshold: ${this.options.alertThresholds.errorRate}%)`,
                    'error'
                );
            }
        }
    }

    /**
     * Update chart data for visualization
     */
    updateChartData(timestamp) {
        // Add timestamp
        this.chartData.timestamps.push(timestamp);
        
        // Add API response time
        const recentApiTimes = this.metrics.apiResponseTimes.slice(-10);
        const avgApiTime = recentApiTimes.length > 0 ? 
            recentApiTimes.reduce((a, b) => a + b, 0) / recentApiTimes.length : 0;
        this.chartData.apiResponseTimes.push(avgApiTime);
        
        // Add cache hit rate
        const recentCacheOps = this.metrics.cacheOperations.slice(-50);
        const hits = recentCacheOps.filter(op => op.hit).length;
        const hitRate = recentCacheOps.length > 0 ? (hits / recentCacheOps.length) * 100 : 0;
        this.chartData.cacheHitRates.push(hitRate);
        
        // Add image load time
        const recentImageTimes = this.metrics.imageLoadTimes.slice(-10);
        const avgImageTime = recentImageTimes.length > 0 ?
            recentImageTimes.reduce((a, b) => a + b, 0) / recentImageTimes.length : 0;
        this.chartData.imageLoadTimes.push(avgImageTime);
        
        // Add memory usage
        const recentMemory = this.metrics.memoryUsage.slice(-1);
        const memoryUsage = recentMemory.length > 0 ? 
            recentMemory[0].used / 1024 / 1024 : 0; // Convert to MB
        this.chartData.memoryUsage.push(memoryUsage);
        
        // Keep only recent data points
        const maxPoints = 50;
        Object.keys(this.chartData).forEach(key => {
            if (this.chartData[key].length > maxPoints) {
                this.chartData[key] = this.chartData[key].slice(-maxPoints);
            }
        });
    }

    /**
     * Clean up old samples
     */
    cleanupOldSamples() {
        const now = Date.now();
        const maxAge = 300000; // 5 minutes
        
        // Clean up old samples from all metrics arrays
        Object.keys(this.metrics).forEach(key => {
            if (Array.isArray(this.metrics[key])) {
                this.metrics[key] = this.metrics[key].filter(
                    sample => now - sample.timestamp < maxAge
                );
            }
        });
        
        // Clean up old alerts
        this.alerts.history = this.alerts.history.filter(
            alert => now - alert.timestamp < maxAge
        );
    }

    /**
     * Start performance reporting
     */
    startReporting() {
        setInterval(() => {
            this.generateReport();
        }, this.options.reportInterval);
    }

    /**
     * Generate performance report
     */
    generateReport() {
        const report = {
            timestamp: Date.now(),
            period: this.options.reportInterval,
            metrics: this.getMetrics(),
            alerts: {
                active: this.alerts.active.length,
                total: this.alerts.history.length
            },
            recommendations: this.generateRecommendations()
        };

        this.emit('monitor:report', report);
        return report;
    }

    /**
     * Generate performance recommendations
     */
    generateRecommendations() {
        const recommendations = [];
        
        // API performance
        if (this.metrics.apiResponseTimes.length > 0) {
            const avgResponseTime = this.metrics.apiResponseTimes.reduce((a, b) => a + b, 0) / this.metrics.apiResponseTimes.length;
            if (avgResponseTime > this.options.alertThresholds.apiResponseTime) {
                recommendations.push({
                    type: 'api-performance',
                    severity: 'medium',
                    message: 'Consider implementing API response caching or optimizing database queries',
                    metric: `Average API response time: ${avgResponseTime.toFixed(2)}ms`
                });
            }
        }
        
        // Cache performance
        if (this.metrics.cacheOperations.length > 0) {
            const hits = this.metrics.cacheOperations.filter(op => op.hit).length;
            const hitRate = (hits / this.metrics.cacheOperations.length) * 100;
            if (hitRate < this.options.alertThresholds.cacheHitRate) {
                recommendations.push({
                    type: 'cache-performance',
                    severity: 'medium',
                    message: 'Consider increasing cache size or implementing cache warming strategies',
                    metric: `Cache hit rate: ${hitRate.toFixed(1)}%`
                });
            }
        }
        
        // Memory usage
        if (this.metrics.memoryUsage.length > 0) {
            const latestMemory = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
            const usedMB = latestMemory.used / 1024 / 1024;
            if (usedMB > this.options.alertThresholds.memoryUsage) {
                recommendations.push({
                    type: 'memory-usage',
                    severity: 'high',
                    message: 'Consider implementing memory cleanup or reducing cache size',
                    metric: `Memory usage: ${usedMB.toFixed(2)}MB`
                });
            }
        }
        
        // Image loading
        if (this.metrics.imageLoadTimes.length > 0) {
            const avgLoadTime = this.metrics.imageLoadTimes.reduce((a, b) => a + b, 0) / this.metrics.imageLoadTimes.length;
            if (avgLoadTime > this.options.alertThresholds.imageLoadTime) {
                recommendations.push({
                    type: 'image-performance',
                    severity: 'medium',
                    message: 'Consider implementing image optimization, lazy loading, or CDN',
                    metric: `Average image load time: ${avgLoadTime.toFixed(2)}ms`
                });
            }
        }
        
        return recommendations;
    }

    /**
     * Get current metrics
     */
    getMetrics() {
        const calculateAverage = (arr) => arr.length > 0 ? arr.reduce((a, b) => a + b, 0) / arr.length : 0;
        const calculatePercentile = (arr, percentile) => {
            if (arr.length === 0) return 0;
            const sorted = [...arr].sort((a, b) => a - b);
            const index = Math.ceil(sorted.length * percentile / 100) - 1;
            return sorted[index];
        };

        return {
            api: {
                totalCalls: this.metrics.apiCalls.length,
                totalErrors: this.metrics.apiErrors.length,
                errorRate: this.metrics.apiCalls.length > 0 ? 
                    (this.metrics.apiErrors.length / this.metrics.apiCalls.length) * 100 : 0,
                avgResponseTime: calculateAverage(this.metrics.apiResponseTimes),
                p95ResponseTime: calculatePercentile(this.metrics.apiResponseTimes, 95),
                p99ResponseTime: calculatePercentile(this.metrics.apiResponseTimes, 99)
            },
            cache: {
                totalOperations: this.metrics.cacheOperations.length,
                hitRate: this.metrics.cacheOperations.length > 0 ?
                    (this.metrics.cacheOperations.filter(op => op.hit).length / this.metrics.cacheOperations.length) * 100 : 0
            },
            images: {
                totalLoads: this.metrics.imageLoadTimes.length,
                totalErrors: this.metrics.imageErrors.length,
                avgLoadTime: calculateAverage(this.metrics.imageLoadTimes),
                p95LoadTime: calculatePercentile(this.metrics.imageLoadTimes, 95)
            },
            memory: {
                current: this.metrics.memoryUsage.length > 0 ? 
                    this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1].used / 1024 / 1024 : 0,
                peak: this.metrics.memoryUsage.length > 0 ?
                    Math.max(...this.metrics.memoryUsage.map(m => m.used)) / 1024 / 1024 : 0
            },
            alerts: {
                active: this.alerts.active.length,
                total: this.alerts.history.length
            },
            uptime: Date.now() - this.metrics.startTime
        };
    }

    /**
     * Get chart data for visualization
     */
    getChartData() {
        return { ...this.chartData };
    }

    /**
     * Get active alerts
     */
    getActiveAlerts() {
        return [...this.alerts.active];
    }

    /**
     * Get alert history
     */
    getAlertHistory() {
        return [...this.alerts.history];
    }

    /**
     * Export performance data
     */
    exportData(format = 'json') {
        const data = {
            timestamp: Date.now(),
            metrics: this.getMetrics(),
            chartData: this.getChartData(),
            alerts: this.getAlertHistory(),
            recommendations: this.generateRecommendations()
        };

        switch (format) {
            case 'json':
                return JSON.stringify(data, null, 2);
            case 'csv':
                return this.convertToCSV(data);
            default:
                return data;
        }
    }

    /**
     * Convert data to CSV format
     */
    convertToCSV(data) {
        const csvRows = [];
        
        // Add headers
        csvRows.push('timestamp,metric,value');
        
        // Add API metrics
        csvRows.push(`${data.timestamp},api_calls,${data.metrics.api.totalCalls}`);
        csvRows.push(`${data.timestamp},api_errors,${data.metrics.api.totalErrors}`);
        csvRows.push(`${data.timestamp},api_response_time,${data.metrics.api.avgResponseTime}`);
        csvRows.push(`${data.timestamp},cache_hit_rate,${data.metrics.cache.hitRate}`);
        csvRows.push(`${data.timestamp},image_load_time,${data.metrics.images.avgLoadTime}`);
        csvRows.push(`${data.timestamp},memory_usage,${data.metrics.memory.current}`);
        
        return csvRows.join('\n');
    }

    /**
     * Event system
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in performance monitor event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Reset performance metrics
     */
    reset() {
        this.metrics = {
            apiCalls: [],
            apiErrors: [],
            apiResponseTimes: [],
            cacheHitRates: [],
            cacheOperations: [],
            imageLoadTimes: [],
            imageErrors: [],
            pageLoadTimes: [],
            interactionDelays: [],
            memoryUsage: [],
            networkConditions: [],
            customMetrics: new Map(),
            startTime: performance.now(),
            lastSample: Date.now()
        };

        this.alerts = {
            active: [],
            history: [],
            suppressions: new Set()
        };

        this.chartData = {
            apiResponseTimes: [],
            cacheHitRates: [],
            imageLoadTimes: [],
            memoryUsage: [],
            timestamps: []
        };

        this.emit('monitor:reset', { timestamp: Date.now() });
    }

    /**
     * Destroy performance monitor
     */
    destroy() {
        // Disconnect all observers
        Object.values(this.observers).forEach(observer => {
            if (observer) {
                observer.disconnect();
            }
        });

        // Clear event listeners
        this.eventListeners.clear();

        // Reset metrics
        this.reset();

        console.log('Performance Monitor destroyed');
    }
}

// Export for use in other modules
window.PerformanceMonitor = PerformanceMonitor;
