/**
 * Yendor Cats Gallery V2 - Advanced Cache Manager
 * Sophisticated client-side caching system with multiple storage layers
 * Implements intelligent cache warming, invalidation, and performance tracking
 */

class CacheManager {
    constructor(options = {}) {
        this.options = {
            memoryMaxItems: options.memoryMaxItems || 100,
            memoryExpiry: options.memoryExpiry || 5 * 60 * 1000, // 5 minutes
            localStorageExpiry: options.localStorageExpiry || 30 * 60 * 1000, // 30 minutes
            sessionStorageExpiry: options.sessionStorageExpiry || 60 * 60 * 1000, // 1 hour
            indexedDBExpiry: options.indexedDBExpiry || 24 * 60 * 60 * 1000, // 24 hours
            compressionEnabled: options.compressionEnabled !== false,
            encryptionEnabled: options.encryptionEnabled || false,
            enablePerformanceTracking: options.enablePerformanceTracking !== false,
            cleanupInterval: options.cleanupInterval || 15 * 60 * 1000, // 15 minutes
            maxLocalStorageSize: options.maxLocalStorageSize || 5 * 1024 * 1024, // 5MB
            cacheVersion: options.cacheVersion || '1.0.0',
            ...options
        };

        // Cache storage layers
        this.memoryCache = new Map();
        this.memoryCacheTimestamps = new Map();
        this.memoryCacheLRU = [];

        // Performance metrics
        this.metrics = {
            memoryHits: 0,
            memoryMisses: 0,
            localStorageHits: 0,
            localStorageMisses: 0,
            sessionStorageHits: 0,
            sessionStorageMisses: 0,
            indexedDBHits: 0,
            indexedDBMisses: 0,
            totalReads: 0,
            totalWrites: 0,
            compressionRatio: 0,
            averageResponseTime: 0,
            errorCount: 0,
            lastCleanup: Date.now()
        };

        // Cache configuration
        this.cacheKeys = {
            gallery: 'yc_gallery_',
            images: 'yc_images_',
            metadata: 'yc_metadata_',
            performance: 'yc_performance_',
            user: 'yc_user_',
            config: 'yc_config_'
        };

        // IndexedDB setup
        this.dbName = 'YendorCatsCache';
        this.dbVersion = 1;
        this.db = null;

        // Event system
        this.eventListeners = new Map();

        // Initialize cache manager
        this.init();
    }

    /**
     * Initialize the cache manager
     */
    async init() {
        try {
            await this.initIndexedDB();
            this.startCleanupInterval();
            this.registerStorageEventListeners();
            await this.validateCacheVersion();
            
            console.log('Cache Manager initialized successfully');
            this.emit('cache:initialized', { 
                storageTypes: this.getAvailableStorageTypes(),
                metrics: this.metrics 
            });
            
        } catch (error) {
            console.error('Cache Manager initialization failed:', error);
            this.metrics.errorCount++;
        }
    }

    /**
     * Initialize IndexedDB for large data storage
     */
    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                console.warn('IndexedDB not supported');
                resolve();
                return;
            }

            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => {
                console.warn('IndexedDB initialization failed:', request.error);
                resolve(); // Continue without IndexedDB
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('IndexedDB initialized successfully');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                if (!db.objectStoreNames.contains('cache')) {
                    const cacheStore = db.createObjectStore('cache', { keyPath: 'key' });
                    cacheStore.createIndex('category', 'category', { unique: false });
                    cacheStore.createIndex('timestamp', 'timestamp', { unique: false });
                }

                if (!db.objectStoreNames.contains('images')) {
                    const imageStore = db.createObjectStore('images', { keyPath: 'key' });
                    imageStore.createIndex('url', 'url', { unique: false });
                    imageStore.createIndex('size', 'size', { unique: false });
                }
            };
        });
    }

    /**
     * Get data from cache with intelligent fallback
     * @param {string} key - Cache key
     * @param {string} category - Cache category (gallery, images, metadata, etc.)
     * @returns {Promise<any>} Cached data or null
     */
    async get(key, category = 'gallery') {
        const startTime = performance.now();
        this.metrics.totalReads++;

        try {
            const fullKey = this.buildKey(key, category);
            
            // 1. Check memory cache first (fastest)
            const memoryResult = this.getFromMemory(fullKey);
            if (memoryResult !== null) {
                this.metrics.memoryHits++;
                this.updateMetrics(startTime);
                return memoryResult;
            }
            this.metrics.memoryMisses++;

            // 2. Check session storage (fast, session-scoped)
            const sessionResult = await this.getFromSessionStorage(fullKey);
            if (sessionResult !== null) {
                this.metrics.sessionStorageHits++;
                // Promote to memory cache
                this.setInMemory(fullKey, sessionResult.data, sessionResult.timestamp);
                this.updateMetrics(startTime);
                return sessionResult.data;
            }
            this.metrics.sessionStorageMisses++;

            // 3. Check local storage (persistent but slower)
            const localResult = await this.getFromLocalStorage(fullKey);
            if (localResult !== null) {
                this.metrics.localStorageHits++;
                // Promote to higher level caches
                this.setInMemory(fullKey, localResult.data, localResult.timestamp);
                this.setInSessionStorage(fullKey, localResult.data, localResult.timestamp);
                this.updateMetrics(startTime);
                return localResult.data;
            }
            this.metrics.localStorageMisses++;

            // 4. Check IndexedDB (largest storage, slowest)
            const indexedDBResult = await this.getFromIndexedDB(fullKey);
            if (indexedDBResult !== null) {
                this.metrics.indexedDBHits++;
                // Promote to all higher level caches
                this.setInMemory(fullKey, indexedDBResult.data, indexedDBResult.timestamp);
                this.setInSessionStorage(fullKey, indexedDBResult.data, indexedDBResult.timestamp);
                this.setInLocalStorage(fullKey, indexedDBResult.data, indexedDBResult.timestamp);
                this.updateMetrics(startTime);
                return indexedDBResult.data;
            }
            this.metrics.indexedDBMisses++;

            this.updateMetrics(startTime);
            return null;

        } catch (error) {
            console.error('Cache get error:', error);
            this.metrics.errorCount++;
            this.updateMetrics(startTime);
            return null;
        }
    }

    /**
     * Set data in cache with intelligent distribution
     * @param {string} key - Cache key
     * @param {any} data - Data to cache
     * @param {string} category - Cache category
     * @param {object} options - Caching options
     */
    async set(key, data, category = 'gallery', options = {}) {
        const startTime = performance.now();
        this.metrics.totalWrites++;

        try {
            const fullKey = this.buildKey(key, category);
            const timestamp = Date.now();
            const dataSize = this.calculateDataSize(data);
            
            // Compress data if enabled and beneficial
            let processedData = data;
            if (this.options.compressionEnabled && dataSize > 1024) {
                processedData = await this.compressData(data);
                this.updateCompressionMetrics(dataSize, this.calculateDataSize(processedData));
            }

            // Encrypt data if enabled
            if (this.options.encryptionEnabled) {
                processedData = await this.encryptData(processedData);
            }

            // Always store in memory cache (fastest access)
            this.setInMemory(fullKey, data, timestamp, options);

            // Store in session storage for session persistence
            if (options.sessionPersistence !== false) {
                await this.setInSessionStorage(fullKey, processedData, timestamp, options);
            }

            // Store in local storage for cross-session persistence
            if (options.localPersistence !== false && dataSize < this.options.maxLocalStorageSize / 10) {
                await this.setInLocalStorage(fullKey, processedData, timestamp, options);
            }

            // Store in IndexedDB for large data or long-term storage
            if (options.indexedDBStorage !== false && (dataSize > 10240 || options.longTerm)) {
                await this.setInIndexedDB(fullKey, processedData, timestamp, category, options);
            }

            this.updateMetrics(startTime);
            this.emit('cache:set', { key: fullKey, size: dataSize, category });

        } catch (error) {
            console.error('Cache set error:', error);
            this.metrics.errorCount++;
            this.updateMetrics(startTime);
        }
    }

    /**
     * Memory cache operations
     */
    getFromMemory(key) {
        const data = this.memoryCache.get(key);
        const timestamp = this.memoryCacheTimestamps.get(key);

        if (data && timestamp) {
            if (Date.now() - timestamp < this.options.memoryExpiry) {
                this.updateLRU(key);
                return data;
            } else {
                this.memoryCache.delete(key);
                this.memoryCacheTimestamps.delete(key);
                this.removeLRU(key);
            }
        }
        return null;
    }

    setInMemory(key, data, timestamp, options = {}) {
        // Enforce memory cache size limit
        if (this.memoryCache.size >= this.options.memoryMaxItems) {
            this.evictLRUMemoryItem();
        }

        this.memoryCache.set(key, data);
        this.memoryCacheTimestamps.set(key, timestamp || Date.now());
        this.updateLRU(key);
    }

    /**
     * Session storage operations
     */
    async getFromSessionStorage(key) {
        try {
            const stored = sessionStorage.getItem(key);
            if (stored) {
                const parsed = JSON.parse(stored);
                if (Date.now() - parsed.timestamp < this.options.sessionStorageExpiry) {
                    let data = parsed.data;
                    
                    // Decrypt if needed
                    if (parsed.encrypted) {
                        data = await this.decryptData(data);
                    }
                    
                    // Decompress if needed
                    if (parsed.compressed) {
                        data = await this.decompressData(data);
                    }
                    
                    return { data, timestamp: parsed.timestamp };
                } else {
                    sessionStorage.removeItem(key);
                }
            }
        } catch (error) {
            console.warn('Session storage get error:', error);
        }
        return null;
    }

    async setInSessionStorage(key, data, timestamp, options = {}) {
        try {
            const item = {
                data,
                timestamp: timestamp || Date.now(),
                compressed: this.options.compressionEnabled,
                encrypted: this.options.encryptionEnabled,
                version: this.options.cacheVersion
            };
            
            sessionStorage.setItem(key, JSON.stringify(item));
        } catch (error) {
            if (error.name === 'QuotaExceededError') {
                this.cleanupSessionStorage();
                console.warn('Session storage quota exceeded, cleaned up');
            } else {
                console.warn('Session storage set error:', error);
            }
        }
    }

    /**
     * Local storage operations
     */
    async getFromLocalStorage(key) {
        try {
            const stored = localStorage.getItem(key);
            if (stored) {
                const parsed = JSON.parse(stored);
                if (Date.now() - parsed.timestamp < this.options.localStorageExpiry) {
                    let data = parsed.data;
                    
                    // Decrypt if needed
                    if (parsed.encrypted) {
                        data = await this.decryptData(data);
                    }
                    
                    // Decompress if needed
                    if (parsed.compressed) {
                        data = await this.decompressData(data);
                    }
                    
                    return { data, timestamp: parsed.timestamp };
                } else {
                    localStorage.removeItem(key);
                }
            }
        } catch (error) {
            console.warn('Local storage get error:', error);
        }
        return null;
    }

    async setInLocalStorage(key, data, timestamp, options = {}) {
        try {
            const item = {
                data,
                timestamp: timestamp || Date.now(),
                compressed: this.options.compressionEnabled,
                encrypted: this.options.encryptionEnabled,
                version: this.options.cacheVersion
            };
            
            localStorage.setItem(key, JSON.stringify(item));
        } catch (error) {
            if (error.name === 'QuotaExceededError') {
                await this.cleanupLocalStorage();
                console.warn('Local storage quota exceeded, cleaned up');
            } else {
                console.warn('Local storage set error:', error);
            }
        }
    }

    /**
     * IndexedDB operations
     */
    async getFromIndexedDB(key) {
        if (!this.db) return null;

        return new Promise((resolve) => {
            try {
                const transaction = this.db.transaction(['cache'], 'readonly');
                const store = transaction.objectStore('cache');
                const request = store.get(key);

                request.onsuccess = async () => {
                    const result = request.result;
                    if (result && Date.now() - result.timestamp < this.options.indexedDBExpiry) {
                        let data = result.data;
                        
                        // Decrypt if needed
                        if (result.encrypted) {
                            data = await this.decryptData(data);
                        }
                        
                        // Decompress if needed
                        if (result.compressed) {
                            data = await this.decompressData(data);
                        }
                        
                        resolve({ data, timestamp: result.timestamp });
                    } else {
                        if (result) {
                            // Remove expired entry
                            this.deleteFromIndexedDB(key);
                        }
                        resolve(null);
                    }
                };

                request.onerror = () => {
                    console.warn('IndexedDB get error:', request.error);
                    resolve(null);
                };
            } catch (error) {
                console.warn('IndexedDB get error:', error);
                resolve(null);
            }
        });
    }

    async setInIndexedDB(key, data, timestamp, category, options = {}) {
        if (!this.db) return;

        return new Promise((resolve) => {
            try {
                const transaction = this.db.transaction(['cache'], 'readwrite');
                const store = transaction.objectStore('cache');
                
                const item = {
                    key,
                    data,
                    timestamp: timestamp || Date.now(),
                    category,
                    compressed: this.options.compressionEnabled,
                    encrypted: this.options.encryptionEnabled,
                    version: this.options.cacheVersion,
                    size: this.calculateDataSize(data)
                };

                const request = store.put(item);

                request.onsuccess = () => {
                    resolve();
                };

                request.onerror = () => {
                    console.warn('IndexedDB set error:', request.error);
                    resolve();
                };
            } catch (error) {
                console.warn('IndexedDB set error:', error);
                resolve();
            }
        });
    }

    /**
     * Cache warming strategies
     */
    async warmCache(keys, category = 'gallery', fetchFunction) {
        console.log(`Warming cache for ${keys.length} keys in category: ${category}`);
        
        const promises = keys.map(async (key) => {
            const cachedData = await this.get(key, category);
            if (!cachedData && fetchFunction) {
                try {
                    const data = await fetchFunction(key);
                    if (data) {
                        await this.set(key, data, category, { longTerm: true });
                    }
                } catch (error) {
                    console.warn(`Cache warm failed for key ${key}:`, error);
                }
            }
        });

        await Promise.allSettled(promises);
        this.emit('cache:warmed', { category, count: keys.length });
    }

    /**
     * Intelligent prefetching based on usage patterns
     */
    async prefetchRelated(currentKey, category, relationshipFunction, maxPrefetch = 5) {
        if (!relationshipFunction) return;

        try {
            const relatedKeys = await relationshipFunction(currentKey);
            const keysToPrefetch = relatedKeys.slice(0, maxPrefetch);
            
            for (const key of keysToPrefetch) {
                const cached = await this.get(key, category);
                if (!cached) {
                    // Prefetch in background
                    setTimeout(async () => {
                        try {
                            const data = await relationshipFunction(key, true); // true = fetch mode
                            if (data) {
                                await this.set(key, data, category, { 
                                    sessionPersistence: false,
                                    lowPriority: true 
                                });
                            }
                        } catch (error) {
                            console.warn(`Prefetch failed for ${key}:`, error);
                        }
                    }, Math.random() * 1000); // Randomize timing to avoid congestion
                }
            }
        } catch (error) {
            console.warn('Prefetch related error:', error);
        }
    }

    /**
     * Cache invalidation and cleanup
     */
    async invalidate(pattern, category = null) {
        const keysToInvalidate = [];

        // Build full pattern if category provided
        const fullPattern = category ? this.buildKey(pattern, category) : pattern;
        
        // Clear from memory cache
        for (const key of this.memoryCache.keys()) {
            if (this.keyMatches(key, fullPattern)) {
                this.memoryCache.delete(key);
                this.memoryCacheTimestamps.delete(key);
                this.removeLRU(key);
                keysToInvalidate.push(key);
            }
        }

        // Clear from session storage
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key && this.keyMatches(key, fullPattern)) {
                sessionStorage.removeItem(key);
                keysToInvalidate.push(key);
            }
        }

        // Clear from local storage
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && this.keyMatches(key, fullPattern)) {
                localStorage.removeItem(key);
                keysToInvalidate.push(key);
            }
        }

        // Clear from IndexedDB
        if (this.db) {
            await this.invalidateFromIndexedDB(fullPattern);
        }

        console.log(`Invalidated ${keysToInvalidate.length} cache entries for pattern: ${fullPattern}`);
        this.emit('cache:invalidated', { pattern: fullPattern, count: keysToInvalidate.length });
    }

    /**
     * Automatic cleanup processes
     */
    startCleanupInterval() {
        setInterval(() => {
            this.performCleanup();
        }, this.options.cleanupInterval);
    }

    async performCleanup() {
        const startTime = performance.now();
        
        try {
            // Clean memory cache
            this.cleanupMemoryCache();
            
            // Clean storage caches
            await this.cleanupLocalStorage();
            await this.cleanupSessionStorage();
            await this.cleanupIndexedDB();
            
            this.metrics.lastCleanup = Date.now();
            
            const cleanupTime = performance.now() - startTime;
            console.log(`Cache cleanup completed in ${cleanupTime.toFixed(2)}ms`);
            
            this.emit('cache:cleaned', { duration: cleanupTime, timestamp: Date.now() });
            
        } catch (error) {
            console.error('Cache cleanup error:', error);
            this.metrics.errorCount++;
        }
    }

    /**
     * Utility methods
     */
    buildKey(key, category) {
        const prefix = this.cacheKeys[category] || this.cacheKeys.gallery;
        return `${prefix}${key}`;
    }

    keyMatches(key, pattern) {
        if (pattern.includes('*')) {
            const regex = new RegExp(pattern.replace(/\*/g, '.*'));
            return regex.test(key);
        }
        return key.includes(pattern);
    }

    calculateDataSize(data) {
        return JSON.stringify(data).length;
    }

    updateLRU(key) {
        // Remove from current position
        this.removeLRU(key);
        // Add to front
        this.memoryCacheLRU.unshift(key);
    }

    removeLRU(key) {
        const index = this.memoryCacheLRU.indexOf(key);
        if (index > -1) {
            this.memoryCacheLRU.splice(index, 1);
        }
    }

    evictLRUMemoryItem() {
        if (this.memoryCacheLRU.length > 0) {
            const lruKey = this.memoryCacheLRU.pop();
            this.memoryCache.delete(lruKey);
            this.memoryCacheTimestamps.delete(lruKey);
        }
    }

    updateMetrics(startTime) {
        const responseTime = performance.now() - startTime;
        this.metrics.averageResponseTime = 
            (this.metrics.averageResponseTime * (this.metrics.totalReads - 1) + responseTime) / this.metrics.totalReads;
    }

    updateCompressionMetrics(originalSize, compressedSize) {
        const ratio = compressedSize / originalSize;
        this.metrics.compressionRatio = 
            (this.metrics.compressionRatio + ratio) / 2; // Moving average
    }

    getAvailableStorageTypes() {
        return {
            memory: true,
            sessionStorage: typeof Storage !== 'undefined',
            localStorage: typeof Storage !== 'undefined',
            indexedDB: !!window.indexedDB
        };
    }

    async validateCacheVersion() {
        const versionKey = this.buildKey('version', 'config');
        const storedVersion = await this.get(versionKey, 'config');
        
        if (storedVersion !== this.options.cacheVersion) {
            console.log('Cache version mismatch, clearing all caches');
            await this.clearAll();
            await this.set(versionKey, this.options.cacheVersion, 'config');
        }
    }

    /**
     * Public API methods
     */
    async clearAll() {
        // Clear memory
        this.memoryCache.clear();
        this.memoryCacheTimestamps.clear();
        this.memoryCacheLRU = [];

        // Clear session storage
        sessionStorage.clear();

        // Clear local storage (only our keys)
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && Object.values(this.cacheKeys).some(prefix => key.startsWith(prefix))) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));

        // Clear IndexedDB
        if (this.db) {
            const transaction = this.db.transaction(['cache'], 'readwrite');
            const store = transaction.objectStore('cache');
            store.clear();
        }

        this.emit('cache:cleared');
        console.log('All caches cleared');
    }

    getMetrics() {
        const totalHits = this.metrics.memoryHits + this.metrics.sessionStorageHits + 
                         this.metrics.localStorageHits + this.metrics.indexedDBHits;
        const totalMisses = this.metrics.memoryMisses + this.metrics.sessionStorageMisses + 
                           this.metrics.localStorageMisses + this.metrics.indexedDBMisses;
        
        return {
            ...this.metrics,
            hitRate: totalHits / (totalHits + totalMisses) * 100,
            memoryCacheSize: this.memoryCache.size,
            totalCacheRequests: totalHits + totalMisses
        };
    }

    /**
     * Event system
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in cache event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Compression and encryption stubs (implement based on requirements)
     */
    async compressData(data) {
        // Simple JSON compression - replace with LZ4 or similar for production
        return JSON.stringify(data);
    }

    async decompressData(data) {
        return JSON.parse(data);
    }

    async encryptData(data) {
        // Implement encryption if needed for sensitive data
        return data;
    }

    async decryptData(data) {
        // Implement decryption if needed
        return data;
    }

    /**
     * Storage event listeners for cross-tab synchronization
     */
    registerStorageEventListeners() {
        window.addEventListener('storage', (e) => {
            if (Object.values(this.cacheKeys).some(prefix => e.key?.startsWith(prefix))) {
                this.emit('cache:external_change', {
                    key: e.key,
                    oldValue: e.oldValue,
                    newValue: e.newValue
                });
            }
        });
    }

    /**
     * Cleanup methods for specific storage types
     */
    cleanupMemoryCache() {
        const now = Date.now();
        const keysToDelete = [];
        
        for (const [key, timestamp] of this.memoryCacheTimestamps.entries()) {
            if (now - timestamp > this.options.memoryExpiry) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => {
            this.memoryCache.delete(key);
            this.memoryCacheTimestamps.delete(key);
            this.removeLRU(key);
        });
    }

    async cleanupLocalStorage() {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && Object.values(this.cacheKeys).some(prefix => key.startsWith(prefix))) {
                try {
                    const item = JSON.parse(localStorage.getItem(key));
                    if (Date.now() - item.timestamp > this.options.localStorageExpiry) {
                        keysToRemove.push(key);
                    }
                } catch (error) {
                    keysToRemove.push(key); // Remove corrupted entries
                }
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }

    cleanupSessionStorage() {
        const keysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key && Object.values(this.cacheKeys).some(prefix => key.startsWith(prefix))) {
                try {
                    const item = JSON.parse(sessionStorage.getItem(key));
                    if (Date.now() - item.timestamp > this.options.sessionStorageExpiry) {
                        keysToRemove.push(key);
                    }
                } catch (error) {
                    keysToRemove.push(key); // Remove corrupted entries
                }
            }
        }
        keysToRemove.forEach(key => sessionStorage.removeItem(key));
    }

    async cleanupIndexedDB() {
        if (!this.db) return;

        const transaction = this.db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        const now = Date.now();

        store.openCursor().onsuccess = (event) => {
            const cursor = event.target.result;
            if (cursor) {
                const item = cursor.value;
                if (now - item.timestamp > this.options.indexedDBExpiry) {
                    cursor.delete();
                }
                cursor.continue();
            }
        };
    }

    async deleteFromIndexedDB(key) {
        if (!this.db) return;

        return new Promise((resolve) => {
            const transaction = this.db.transaction(['cache'], 'readwrite');
            const store = transaction.objectStore('cache');
            const request = store.delete(key);

            request.onsuccess = () => resolve();
            request.onerror = () => {
                console.warn('IndexedDB delete error:', request.error);
                resolve();
            };
        });
    }

    async invalidateFromIndexedDB(pattern) {
        if (!this.db) return;

        return new Promise((resolve) => {
            const transaction = this.db.transaction(['cache'], 'readwrite');
            const store = transaction.objectStore('cache');
            const request = store.openCursor();

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                if (cursor) {
                    const item = cursor.value;
                    if (this.keyMatches(item.key, pattern)) {
                        cursor.delete();
                    }
                    cursor.continue();
                } else {
                    resolve();
                }
            };

            request.onerror = () => {
                console.warn('IndexedDB invalidate error:', request.error);
                resolve();
            };
        });
    }

    /**
     * Advanced cache strategies
     */
    async preloadImageBatch(imageUrls, priority = 'normal') {
        const promises = imageUrls.map(async (url, index) => {
            // Stagger requests to avoid overwhelming the server
            const delay = priority === 'high' ? index * 50 : index * 200;
            
            return new Promise((resolve) => {
                setTimeout(async () => {
                    try {
                        const cached = await this.get(url, 'images');
                        if (!cached) {
                            const img = new Image();
                            img.onload = async () => {
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                canvas.width = img.width;
                                canvas.height = img.height;
                                ctx.drawImage(img, 0, 0);
                                
                                const imageData = canvas.toDataURL('image/jpeg', 0.8);
                                await this.set(url, imageData, 'images', {
                                    longTerm: true,
                                    indexedDBStorage: true
                                });
                                resolve(true);
                            };
                            img.onerror = () => resolve(false);
                            img.src = url;
                        } else {
                            resolve(true);
                        }
                    } catch (error) {
                        console.warn('Image preload failed:', error);
                        resolve(false);
                    }
                }, delay);
            });
        });

        const results = await Promise.allSettled(promises);
        const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length;
        
        console.log(`Preloaded ${successCount}/${imageUrls.length} images`);
        this.emit('cache:preloaded', { 
            total: imageUrls.length, 
            success: successCount, 
            priority 
        });
    }

    /**
     * Smart cache eviction based on usage patterns
     */
    async smartEviction(targetSize = 0.7) {
        const currentSize = this.memoryCache.size;
        const targetItems = Math.floor(this.options.memoryMaxItems * targetSize);
        
        if (currentSize <= targetItems) return;

        // Get usage statistics
        const usageStats = new Map();
        for (const key of this.memoryCache.keys()) {
            const timestamp = this.memoryCacheTimestamps.get(key);
            const age = Date.now() - timestamp;
            const lruPosition = this.memoryCacheLRU.indexOf(key);
            
            // Calculate score based on age and usage
            const score = (age / 1000) + (lruPosition * 10);
            usageStats.set(key, score);
        }

        // Sort by score (higher score = less valuable)
        const sortedKeys = Array.from(usageStats.entries())
            .sort((a, b) => b[1] - a[1])
            .map(([key]) => key);

        // Evict least valuable items
        const itemsToEvict = sortedKeys.slice(0, currentSize - targetItems);
        itemsToEvict.forEach(key => {
            this.memoryCache.delete(key);
            this.memoryCacheTimestamps.delete(key);
            this.removeLRU(key);
        });

        console.log(`Smart eviction removed ${itemsToEvict.length} items`);
        this.emit('cache:evicted', { count: itemsToEvict.length, strategy: 'smart' });
    }

    /**
     * Performance optimization utilities
     */
    async optimizeStorage() {
        const startTime = performance.now();
        
        // Run smart eviction
        await this.smartEviction(0.8);
        
        // Compress frequently accessed data
        await this.compressFrequentData();
        
        // Cleanup expired entries
        await this.performCleanup();
        
        const optimizationTime = performance.now() - startTime;
        console.log(`Storage optimization completed in ${optimizationTime.toFixed(2)}ms`);
        
        this.emit('cache:optimized', { 
            duration: optimizationTime, 
            timestamp: Date.now() 
        });
    }

    async compressFrequentData() {
        if (!this.options.compressionEnabled) return;

        const frequentKeys = this.memoryCacheLRU.slice(0, 20); // Top 20 most recently used
        
        for (const key of frequentKeys) {
            const data = this.memoryCache.get(key);
            if (data && this.calculateDataSize(data) > 2048) {
                try {
                    const compressed = await this.compressData(data);
                    await this.setInLocalStorage(key, compressed, Date.now(), { compressed: true });
                } catch (error) {
                    console.warn('Compression failed for key:', key, error);
                }
            }
        }
    }

    /**
     * Cache statistics and analytics
     */
    generateReport() {
        const metrics = this.getMetrics();
        const report = {
            timestamp: Date.now(),
            performance: {
                hitRate: metrics.hitRate.toFixed(2) + '%',
                averageResponseTime: metrics.averageResponseTime.toFixed(2) + 'ms',
                compressionRatio: (metrics.compressionRatio * 100).toFixed(1) + '%'
            },
            usage: {
                memoryCache: {
                    size: metrics.memoryCacheSize,
                    hits: metrics.memoryHits,
                    misses: metrics.memoryMisses,
                    efficiency: (metrics.memoryHits / (metrics.memoryHits + metrics.memoryMisses) * 100).toFixed(1) + '%'
                },
                localStorage: {
                    hits: metrics.localStorageHits,
                    misses: metrics.localStorageMisses,
                    efficiency: (metrics.localStorageHits / (metrics.localStorageHits + metrics.localStorageMisses) * 100).toFixed(1) + '%'
                },
                sessionStorage: {
                    hits: metrics.sessionStorageHits,
                    misses: metrics.sessionStorageMisses,
                    efficiency: (metrics.sessionStorageHits / (metrics.sessionStorageHits + metrics.sessionStorageMisses) * 100).toFixed(1) + '%'
                },
                indexedDB: {
                    hits: metrics.indexedDBHits,
                    misses: metrics.indexedDBMisses,
                    efficiency: (metrics.indexedDBHits / (metrics.indexedDBHits + metrics.indexedDBMisses) * 100).toFixed(1) + '%'
                }
            },
            operations: {
                totalReads: metrics.totalReads,
                totalWrites: metrics.totalWrites,
                errors: metrics.errorCount,
                lastCleanup: new Date(metrics.lastCleanup).toISOString()
            }
        };

        return report;
    }

    /**
     * Export cache manager for use in other modules
     */
    destroy() {
        // Clear all caches
        this.memoryCache.clear();
        this.memoryCacheTimestamps.clear();
        this.memoryCacheLRU = [];

        // Clear event listeners
        this.eventListeners.clear();

        // Close IndexedDB connection
        if (this.db) {
            this.db.close();
            this.db = null;
        }

        console.log('Cache Manager destroyed');
    }
}

// Export for use in other modules
window.CacheManager = CacheManager;
