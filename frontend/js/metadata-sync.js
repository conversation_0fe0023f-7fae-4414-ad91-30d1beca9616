/**
 * Metadata Sync Manager for YendorCats
 * Handles periodic metadata synchronization and status checking
 */

class MetadataSyncManager {
    constructor(apiBaseUrl = '/api') {
        this.apiBaseUrl = apiBaseUrl;
        this.syncStatusInterval = null;
        this.autoSyncInterval = null;
        this.isInitialized = false;
    }

    /**
     * Initialize the metadata sync manager
     */
    async initialize() {
        if (this.isInitialized) return;

        console.log('🔄 Initializing Metadata Sync Manager');
        
        // Check initial sync status
        await this.checkSyncStatus();
        
        // Set up periodic status checks (every 5 minutes)
        this.syncStatusInterval = setInterval(() => {
            this.checkSyncStatus();
        }, 5 * 60 * 1000);

        // Set up auto sync calls (every 30 minutes)
        this.autoSyncInterval = setInterval(() => {
            this.triggerAutoSync();
        }, 30 * 60 * 1000);

        this.isInitialized = true;
        console.log('✅ Metadata Sync Manager initialized');
    }

    /**
     * Check the current sync status
     */
    async checkSyncStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/Sync/metadata/status`);
            
            if (response.ok) {
                const status = await response.json();
                console.log('📊 Sync Status:', {
                    lastSync: new Date(status.lastSyncTime).toLocaleString(),
                    isRecent: status.isRecentSync,
                    nextSync: new Date(status.nextScheduledSync).toLocaleString(),
                    imageCount: status.databaseImageCount
                });

                // Trigger sync if it's been too long
                if (!status.isRecentSync) {
                    console.log('⏰ Sync needed - triggering auto sync');
                    await this.triggerAutoSync();
                }
            } else {
                console.warn('⚠️ Failed to check sync status:', response.statusText);
            }
        } catch (error) {
            console.warn('⚠️ Error checking sync status:', error);
        }
    }

    /**
     * Trigger automatic metadata sync
     */
    async triggerAutoSync() {
        try {
            console.log('🔄 Triggering automatic metadata sync');
            
            const response = await fetch(`${this.apiBaseUrl}/Sync/metadata/auto`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                
                if (response.status === 304) {
                    console.log('📝 Auto sync: No sync needed');
                } else {
                    console.log('✅ Auto sync completed:', {
                        success: result.success,
                        message: result.message,
                        duration: `${result.duration}ms`,
                        stats: result.statistics
                    });
                }
            } else {
                console.warn('⚠️ Auto sync failed:', response.statusText);
            }
        } catch (error) {
            console.warn('⚠️ Error in auto sync:', error);
        }
    }

    /**
     * Trigger manual metadata sync for a specific category
     */
    async triggerManualSync(category = null) {
        try {
            console.log(`🔄 Triggering manual metadata sync${category ? ` for category: ${category}` : ''}`);
            
            const url = category 
                ? `${this.apiBaseUrl}/Sync/metadata?category=${encodeURIComponent(category)}`
                : `${this.apiBaseUrl}/Sync/metadata`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Manual sync completed:', {
                    success: result.success,
                    message: result.message,
                    duration: `${result.duration}ms`,
                    stats: result.statistics
                });
                return result;
            } else {
                const error = await response.json();
                console.error('❌ Manual sync failed:', error);
                return { success: false, message: error.message };
            }
        } catch (error) {
            console.error('❌ Error in manual sync:', error);
            return { success: false, message: error.message };
        }
    }

    /**
     * Stop the sync manager
     */
    stop() {
        if (this.syncStatusInterval) {
            clearInterval(this.syncStatusInterval);
            this.syncStatusInterval = null;
        }

        if (this.autoSyncInterval) {
            clearInterval(this.autoSyncInterval);
            this.autoSyncInterval = null;
        }

        this.isInitialized = false;
        console.log('🛑 Metadata Sync Manager stopped');
    }

    /**
     * Get current sync statistics for display
     */
    async getSyncStatistics() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/Sync/metadata/status`);
            
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`Failed to get sync statistics: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error getting sync statistics:', error);
            return null;
        }
    }
}

// Global instance
let metadataSyncManager = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on a page that needs it
    if (window.location.pathname === '/' || 
        window.location.pathname.includes('gallery') || 
        window.location.pathname.includes('index.html')) {
        
        metadataSyncManager = new MetadataSyncManager();
        metadataSyncManager.initialize();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (metadataSyncManager) {
        metadataSyncManager.stop();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined') {
    module.exports = { MetadataSyncManager };
}

// Global access for debugging
window.MetadataSyncManager = MetadataSyncManager;
window.metadataSyncManager = metadataSyncManager;
