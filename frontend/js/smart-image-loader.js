/**
 * Yendor Cats Gallery V2 - Smart Image Loader
 * Advanced image loading with lazy loading, progressive enhancement, and intelligent caching
 * Optimizes image loading performance through preloading, compression, and adaptive quality
 */

class SmartImageLoader {
    constructor(options = {}) {
        this.options = {
            lazyLoadEnabled: options.lazyLoadEnabled !== false,
            progressiveLoadingEnabled: options.progressiveLoadingEnabled !== false,
            preloadEnabled: options.preloadEnabled !== false,
            adaptiveQuality: options.adaptiveQuality !== false,
            cacheManager: options.cacheManager || null,
            performanceMonitor: options.performanceMonitor || null,
            
            // Loading thresholds
            lazyLoadThreshold: options.lazyLoadThreshold || 200, // pixels
            preloadThreshold: options.preloadThreshold || 500, // pixels
            
            // Quality settings
            lowQualityThreshold: options.lowQualityThreshold || 30, // JPEG quality
            mediumQualityThreshold: options.mediumQualityThreshold || 60,
            highQualityThreshold: options.highQualityThreshold || 85,
            
            // Performance settings
            maxConcurrentLoads: options.maxConcurrentLoads || 4,
            retryAttempts: options.retryAttempts || 3,
            retryDelay: options.retryDelay || 1000,
            
            // Progressive loading
            progressiveSteps: options.progressiveSteps || [
                { width: 100, quality: 30 },
                { width: 300, quality: 60 },
                { width: 800, quality: 85 }
            ],
            
            // Responsive breakpoints
            breakpoints: options.breakpoints || [
                { maxWidth: 480, suffix: '-mobile' },
                { maxWidth: 768, suffix: '-tablet' },
                { maxWidth: 1200, suffix: '-desktop' },
                { maxWidth: 1920, suffix: '-hd' }
            ],
            
            ...options
        };

        // Loading state management
        this.loadingQueue = [];
        this.activeLoads = new Set();
        this.loadedImages = new Set();
        this.failedImages = new Set();
        this.observers = new Map();

        // Performance metrics
        this.metrics = {
            totalImages: 0,
            loadedImages: 0,
            failedImages: 0,
            cachedImages: 0,
            totalLoadTime: 0,
            averageLoadTime: 0,
            bandwidthSaved: 0,
            compressionRatio: 0
        };

        // Network condition adaptation
        this.networkCondition = 'unknown';
        this.connectionSpeed = 'unknown';

        // Event system
        this.eventListeners = new Map();

        // Progressive loading cache
        this.progressiveCache = new Map();

        // Initialize the loader
        this.init();
    }

    /**
     * Initialize the smart image loader
     */
    init() {
        this.setupIntersectionObserver();
        this.setupNetworkMonitoring();
        this.setupPerformanceObserver();
        this.setupErrorHandling();
        
        console.log('Smart Image Loader initialized');
        this.emit('loader:initialized', { timestamp: Date.now() });
    }

    /**
     * Setup intersection observer for lazy loading
     */
    setupIntersectionObserver() {
        if (!window.IntersectionObserver) {
            console.warn('IntersectionObserver not supported, falling back to immediate loading');
            return;
        }

        // Lazy loading observer
        const lazyObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        lazyObserver.unobserve(entry.target);
                    }
                });
            },
            {
                rootMargin: `${this.options.lazyLoadThreshold}px`,
                threshold: 0.1
            }
        );

        // Preload observer
        const preloadObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.preloadImage(entry.target);
                        preloadObserver.unobserve(entry.target);
                    }
                });
            },
            {
                rootMargin: `${this.options.preloadThreshold}px`,
                threshold: 0.1
            }
        );

        this.observers.set('lazy', lazyObserver);
        this.observers.set('preload', preloadObserver);
    }

    /**
     * Setup network condition monitoring
     */
    setupNetworkMonitoring() {
        if (navigator.connection) {
            this.updateNetworkCondition();
            
            navigator.connection.addEventListener('change', () => {
                this.updateNetworkCondition();
            });
        }
    }

    /**
     * Update network condition for adaptive loading
     */
    updateNetworkCondition() {
        if (navigator.connection) {
            this.networkCondition = navigator.connection.effectiveType;
            this.connectionSpeed = navigator.connection.downlink;
            
            // Adjust quality based on connection
            if (this.options.adaptiveQuality) {
                this.adjustQualityForNetwork();
            }
        }
    }

    /**
     * Adjust image quality based on network conditions
     */
    adjustQualityForNetwork() {
        let qualityMultiplier = 1.0;
        
        switch (this.networkCondition) {
            case 'slow-2g':
                qualityMultiplier = 0.3;
                break;
            case '2g':
                qualityMultiplier = 0.5;
                break;
            case '3g':
                qualityMultiplier = 0.7;
                break;
            case '4g':
                qualityMultiplier = 1.0;
                break;
            default:
                qualityMultiplier = 0.8;
        }

        // Update quality thresholds
        this.options.lowQualityThreshold = Math.round(30 * qualityMultiplier);
        this.options.mediumQualityThreshold = Math.round(60 * qualityMultiplier);
        this.options.highQualityThreshold = Math.round(85 * qualityMultiplier);
    }

    /**
     * Setup performance observer
     */
    setupPerformanceObserver() {
        if (window.PerformanceObserver) {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.entryType === 'resource' && entry.name.match(/\.(jpg|jpeg|png|webp|gif)$/i)) {
                        this.trackImagePerformance(entry);
                    }
                });
            });

            observer.observe({ entryTypes: ['resource'] });
            this.observers.set('performance', observer);
        }
    }

    /**
     * Setup error handling
     */
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            if (event.target.tagName === 'IMG') {
                this.handleImageError(event.target);
            }
        }, true);
    }

    /**
     * Load image with smart optimization
     */
    async loadImage(imgElement) {
        if (this.loadedImages.has(imgElement)) return;
        
        const startTime = performance.now();
        
        try {
            // Mark as loading
            imgElement.classList.add('loading');
            
            // Get optimal image configuration
            const config = this.getOptimalImageConfig(imgElement);
            
            // Check cache first
            if (this.options.cacheManager) {
                const cachedImage = await this.options.cacheManager.get(config.src, 'images');
                if (cachedImage) {
                    this.applyImageToElement(imgElement, cachedImage, config);
                    this.metrics.cachedImages++;
                    this.trackImageLoad(imgElement, performance.now() - startTime, true);
                    return;
                }
            }

            // Progressive loading
            if (this.options.progressiveLoadingEnabled) {
                await this.loadProgressively(imgElement, config);
            } else {
                await this.loadDirectly(imgElement, config);
            }

            this.trackImageLoad(imgElement, performance.now() - startTime, false);

        } catch (error) {
            this.handleImageError(imgElement, error);
            this.metrics.failedImages++;
        }
    }

    /**
     * Load image progressively
     */
    async loadProgressively(imgElement, config) {
        const steps = this.options.progressiveSteps;
        let currentStep = 0;

        // Show placeholder first
        this.showPlaceholder(imgElement);

        for (const step of steps) {
            try {
                const progressiveConfig = {
                    ...config,
                    width: step.width,
                    quality: step.quality
                };

                const progressiveSrc = this.buildProgressiveUrl(config.src, progressiveConfig);
                const imageData = await this.fetchImageData(progressiveSrc);

                // Apply progressive image
                this.applyImageToElement(imgElement, imageData, progressiveConfig);
                
                // Cache progressive step
                if (this.options.cacheManager) {
                    await this.options.cacheManager.set(progressiveSrc, imageData, 'images');
                }

                currentStep++;
                
                // Small delay for visual effect
                if (currentStep < steps.length) {
                    await this.delay(50);
                }

            } catch (error) {
                console.warn('Progressive loading step failed:', error);
                if (currentStep === 0) {
                    throw error; // Fail if first step fails
                }
                break; // Continue with current quality if later steps fail
            }
        }
    }

    /**
     * Load image directly
     */
    async loadDirectly(imgElement, config) {
        const imageData = await this.fetchImageData(config.src);
        this.applyImageToElement(imgElement, imageData, config);
        
        // Cache the image
        if (this.options.cacheManager) {
            await this.options.cacheManager.set(config.src, imageData, 'images');
        }
    }

    /**
     * Fetch image data with optimization
     */
    async fetchImageData(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                // Convert to canvas for processing
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                // Get image data
                const imageData = canvas.toDataURL('image/jpeg', 0.8);
                
                resolve({
                    data: imageData,
                    width: img.width,
                    height: img.height,
                    size: imageData.length
                });
            };
            
            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = src;
        });
    }

    /**
     * Get optimal image configuration
     */
    getOptimalImageConfig(imgElement) {
        const containerWidth = imgElement.parentElement?.offsetWidth || window.innerWidth;
        const devicePixelRatio = window.devicePixelRatio || 1;
        const effectiveWidth = containerWidth * devicePixelRatio;
        
        // Find appropriate breakpoint
        const breakpoint = this.options.breakpoints.find(bp => effectiveWidth <= bp.maxWidth) || 
                          this.options.breakpoints[this.options.breakpoints.length - 1];
        
        // Get base src
        const baseSrc = imgElement.dataset.src || imgElement.src;
        
        // Build optimized src
        const optimizedSrc = this.buildOptimizedUrl(baseSrc, {
            width: effectiveWidth,
            quality: this.getQualityForSize(effectiveWidth),
            format: this.getOptimalFormat(),
            suffix: breakpoint.suffix
        });

        return {
            src: optimizedSrc,
            width: effectiveWidth,
            quality: this.getQualityForSize(effectiveWidth),
            format: this.getOptimalFormat()
        };
    }

    /**
     * Build optimized image URL
     */
    buildOptimizedUrl(baseSrc, config) {
        // This would integrate with your image optimization service
        // For now, return base URL with parameters
        const url = new URL(baseSrc, window.location.origin);
        
        url.searchParams.set('w', config.width);
        url.searchParams.set('q', config.quality);
        url.searchParams.set('f', config.format);
        
        return url.toString();
    }

    /**
     * Build progressive image URL
     */
    buildProgressiveUrl(baseSrc, config) {
        const url = new URL(baseSrc, window.location.origin);
        
        url.searchParams.set('w', config.width);
        url.searchParams.set('q', config.quality);
        url.searchParams.set('progressive', 'true');
        
        return url.toString();
    }

    /**
     * Get quality setting based on image size
     */
    getQualityForSize(width) {
        if (width <= 300) return this.options.lowQualityThreshold;
        if (width <= 800) return this.options.mediumQualityThreshold;
        return this.options.highQualityThreshold;
    }

    /**
     * Get optimal image format
     */
    getOptimalFormat() {
        // Check WebP support
        if (this.supportsWebP()) return 'webp';
        // Check AVIF support
        if (this.supportsAvif()) return 'avif';
        return 'jpeg';
    }

    /**
     * Check WebP support
     */
    supportsWebP() {
        if (this._webpSupport !== undefined) return this._webpSupport;
        
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        
        this._webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        return this._webpSupport;
    }

    /**
     * Check AVIF support
     */
    supportsAvif() {
        if (this._avifSupport !== undefined) return this._avifSupport;
        
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        
        try {
            this._avifSupport = canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
        } catch {
            this._avifSupport = false;
        }
        
        return this._avifSupport;
    }

    /**
     * Apply image to element
     */
    applyImageToElement(imgElement, imageData, config) {
        imgElement.src = imageData.data;
        imgElement.classList.remove('loading');
        imgElement.classList.add('loaded');
        
        // Set dimensions
        if (config.width && config.height) {
            imgElement.setAttribute('data-original-width', imageData.width);
            imgElement.setAttribute('data-original-height', imageData.height);
        }
        
        // Mark as loaded
        this.loadedImages.add(imgElement);
        
        // Emit load event
        this.emit('image:loaded', {
            element: imgElement,
            config,
            fromCache: false
        });
    }

    /**
     * Show placeholder while loading
     */
    showPlaceholder(imgElement) {
        // Create blur placeholder from data attribute or generate one
        const placeholder = imgElement.dataset.placeholder || this.generatePlaceholder();
        imgElement.src = placeholder;
        imgElement.classList.add('placeholder');
    }

    /**
     * Generate placeholder image
     */
    generatePlaceholder() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = 10;
        canvas.height = 10;
        
        // Create simple gradient placeholder
        const gradient = ctx.createLinearGradient(0, 0, 10, 10);
        gradient.addColorStop(0, '#f0f0f0');
        gradient.addColorStop(1, '#e0e0e0');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 10, 10);
        
        return canvas.toDataURL();
    }

    /**
     * Preload image
     */
    async preloadImage(imgElement) {
        if (this.loadedImages.has(imgElement) || this.activeLoads.has(imgElement)) return;
        
        const config = this.getOptimalImageConfig(imgElement);
        
        try {
            this.activeLoads.add(imgElement);
            
            // Check if already cached
            if (this.options.cacheManager) {
                const cached = await this.options.cacheManager.get(config.src, 'images');
                if (cached) return;
            }
            
            // Preload the image
            const imageData = await this.fetchImageData(config.src);
            
            // Cache it
            if (this.options.cacheManager) {
                await this.options.cacheManager.set(config.src, imageData, 'images');
            }
            
            this.emit('image:preloaded', { element: imgElement, config });
            
        } catch (error) {
            console.warn('Preload failed:', error);
        } finally {
            this.activeLoads.delete(imgElement);
        }
    }

    /**
     * Handle image loading errors
     */
    handleImageError(imgElement, error = null) {
        imgElement.classList.remove('loading');
        imgElement.classList.add('error');
        
        // Try fallback image
        const fallback = imgElement.dataset.fallback;
        if (fallback && imgElement.src !== fallback) {
            imgElement.src = fallback;
            return;
        }
        
        // Show error placeholder
        imgElement.src = this.generateErrorPlaceholder();
        
        this.failedImages.add(imgElement);
        
        if (this.options.performanceMonitor) {
            this.options.performanceMonitor.trackImageError(imgElement.src, error);
        }
        
        this.emit('image:error', { element: imgElement, error });
    }

    /**
     * Generate error placeholder
     */
    generateErrorPlaceholder() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = 100;
        canvas.height = 100;
        
        ctx.fillStyle = '#f5f5f5';
        ctx.fillRect(0, 0, 100, 100);
        
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 2;
        ctx.strokeRect(10, 10, 80, 80);
        
        // Draw X
        ctx.strokeStyle = '#999';
        ctx.beginPath();
        ctx.moveTo(30, 30);
        ctx.lineTo(70, 70);
        ctx.moveTo(70, 30);
        ctx.lineTo(30, 70);
        ctx.stroke();
        
        return canvas.toDataURL();
    }

    /**
     * Track image performance
     */
    trackImagePerformance(entry) {
        const loadTime = entry.duration;
        const transferSize = entry.transferSize;
        
        this.metrics.totalLoadTime += loadTime;
        this.metrics.averageLoadTime = this.metrics.totalLoadTime / this.metrics.loadedImages;
        
        if (this.options.performanceMonitor) {
            this.options.performanceMonitor.trackImageLoad(
                entry.name,
                loadTime,
                null,
                null,
                false
            );
        }
        
        this.emit('performance:tracked', {
            url: entry.name,
            loadTime,
            transferSize
        });
    }

    /**
     * Track image load completion
     */
    trackImageLoad(imgElement, loadTime, fromCache) {
        this.metrics.totalImages++;
        this.metrics.loadedImages++;
        
        if (fromCache) {
            this.metrics.cachedImages++;
        }
        
        this.metrics.totalLoadTime += loadTime;
        this.metrics.averageLoadTime = this.metrics.totalLoadTime / this.metrics.loadedImages;
        
        if (this.options.performanceMonitor) {
            this.options.performanceMonitor.trackImageLoad(
                imgElement.src,
                loadTime,
                imgElement.naturalWidth,
                imgElement.naturalHeight,
                fromCache
            );
        }
    }

    /**
     * Observe images for lazy loading
     */
    observeImages(selector = 'img[data-src]') {
        const images = document.querySelectorAll(selector);
        
        images.forEach(img => {
            if (this.options.lazyLoadEnabled) {
                this.observers.get('lazy')?.observe(img);
            }
            
            if (this.options.preloadEnabled) {
                this.observers.get('preload')?.observe(img);
            }
        });
        
        this.emit('images:observed', { count: images.length });
    }

    /**
     * Batch preload images
     */
    async batchPreload(urls, options = {}) {
        const { priority = 'normal', maxConcurrent = 3 } = options;
        const results = [];
        
        for (let i = 0; i < urls.length; i += maxConcurrent) {
            const batch = urls.slice(i, i + maxConcurrent);
            
            const batchPromises = batch.map(async (url, index) => {
                const delay = priority === 'high' ? index * 10 : index * 100;
                
                return new Promise((resolve) => {
                    setTimeout(async () => {
                        try {
                            const imageData = await this.fetchImageData(url);
                            
                            if (this.options.cacheManager) {
                                await this.options.cacheManager.set(url, imageData, 'images');
                            }
                            
                            resolve({ url, success: true, data: imageData });
                        } catch (error) {
                            resolve({ url, success: false, error });
                        }
                    }, delay);
                });
            });
            
            const batchResults = await Promise.all(batchPromises);
            results.push(...batchResults);
        }
        
        const successful = results.filter(r => r.success).length;
        
        this.emit('batch:preloaded', {
            total: urls.length,
            successful,
            failed: urls.length - successful
        });
        
        return results;
    }

    /**
     * Get loading metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            loadSuccessRate: this.metrics.totalImages > 0 ? 
                (this.metrics.loadedImages / this.metrics.totalImages) * 100 : 0,
            cacheHitRate: this.metrics.totalImages > 0 ? 
                (this.metrics.cachedImages / this.metrics.totalImages) * 100 : 0,
            activeLoads: this.activeLoads.size,
            queuedLoads: this.loadingQueue.length
        };
    }

    /**
     * Generate performance report
     */
    generateReport() {
        const metrics = this.getMetrics();
        
        return {
            timestamp: Date.now(),
            metrics,
            networkCondition: this.networkCondition,
            connectionSpeed: this.connectionSpeed,
            supportedFormats: {
                webp: this.supportsWebP(),
                avif: this.supportsAvif()
            },
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * Generate optimization recommendations
     */
    generateRecommendations() {
        const recommendations = [];
        const metrics = this.getMetrics();
        
        if (metrics.loadSuccessRate < 95) {
            recommendations.push({
                type: 'reliability',
                message: 'Consider implementing better error handling and fallback images',
                priority: 'high'
            });
        }
        
        if (metrics.averageLoadTime > 2000) {
            recommendations.push({
                type: 'performance',
                message: 'Image loading is slow. Consider optimizing image sizes or using a CDN',
                priority: 'medium'
            });
        }
        
        if (metrics.cacheHitRate < 50) {
            recommendations.push({
                type: 'caching',
                message: 'Low cache hit rate. Consider implementing more aggressive caching',
                priority: 'medium'
            });
        }
        
        return recommendations;
    }

    /**
     * Utility method for delays
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Event system
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const listeners = this.eventListeners.get(event);
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in image loader event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Destroy the image loader
     */
    destroy() {
        // Disconnect observers
        this.observers.forEach(observer => {
            observer.disconnect();
        });
        this.observers.clear();
        
        // Clear event listeners
        this.eventListeners.clear();
        
        // Clear caches
        this.loadedImages.clear();
        this.failedImages.clear();
        this.activeLoads.clear();
        this.progressiveCache.clear();
        
        console.log('Smart Image Loader destroyed');
    }
}

// Export for use in other modules
window.SmartImageLoader = SmartImageLoader;
