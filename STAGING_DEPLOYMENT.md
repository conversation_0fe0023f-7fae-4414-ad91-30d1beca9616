# YendorCats Staging Deployment Guide

---

## Overview

The YendorCats application has been successfully containerized into three independent services for staging deployment:

- **Frontend Service**: Nginx-based static file server with API proxying
- **API Service**: .NET Core backend API (independent of frontend)
- **File Uploader Service**: Node.js microservice for handling file uploads

---

## Architecture

### Service Independence

Each service runs in its own container with the following benefits:

- **Independent scaling**: Scale services based on demand
- **Independent updates**: Deploy frontend/backend separately
- **Fault isolation**: Service failures don't affect others
- **Technology flexibility**: Different tech stacks per service

### Network Communication

```mermaid
graph TB
    Client[Client Browser] --> Frontend[Frontend Container :80]
    Frontend --> |/api/*| API[API Container :80]
    Frontend --> |/uploader/*| Uploader[Uploader Container :80]
    API --> Database[(SQLite Database)]
    Uploader --> Storage[S3/B2 Storage]
```

---

## Container Details

### Frontend Container (`yendorcats/frontend:staging`)

- **Base Image**: `nginx:alpine`
- **Port**: 80
- **Features**:
  - Serves static HTML/CSS/JS files
  - Proxies `/api/*` requests to backend
  - Proxies `/uploader/*` requests to file service
  - CORS headers configured
  - Health check endpoint: `/health`

### API Container (`yendorcats/api:staging`)

- **Base Image**: `mcr.microsoft.com/dotnet/aspnet:8.0`
- **Port**: 80 (internal), 5003 (external)
- **Features**:
  - .NET Core API without frontend serving
  - SQLite database with persistence
  - JWT authentication
  - S3/B2 storage integration
  - Health check endpoint: `/health`

### Uploader Container (`yendorcats/uploader:staging`)

- **Base Image**: `node:18-alpine`
- **Port**: 80 (internal), 5002 (external)
- **Features**:
  - File upload handling
  - Image processing
  - S3/B2 storage integration
  - Health check endpoint: `/health`

---

## Deployment Process

### Prerequisites

1. **Docker & Docker Compose** installed
2. **Environment configuration** (`.env.staging`)
3. **Storage credentials** (S3/B2)

### Quick Start

```bash
# 1. Configure environment
cp .env.staging.template .env.staging
# Edit .env.staging with your values

# 2. Deploy staging environment
./deploy-staging.sh

# 3. Verify deployment
curl http://localhost/health          # Frontend
curl http://localhost:5003/api/cats   # API
curl http://localhost:5002/health     # Uploader
```

### Manual Deployment

```bash
# Build all images
docker-compose -f docker-compose.staging.yml build

# Start services
docker-compose -f docker-compose.staging.yml up -d

# Check status
docker-compose -f docker-compose.staging.yml ps

# View logs
docker-compose -f docker-compose.staging.yml logs -f
```

---

## Service Endpoints

### Frontend (Port 80)

| Endpoint | Description |
|----------|-------------|
| `http://localhost/` | Main website |
| `http://localhost/health` | Health check |
| `http://localhost/api/*` | Proxied to API service |
| `http://localhost/uploader/*` | Proxied to uploader service |

### API Service (Port 5003)

| Endpoint | Description |
|----------|-------------|
| `http://localhost:5003/api/cats` | Cat data API |
| `http://localhost:5003/api/auth` | Authentication |
| `http://localhost:5003/health` | Health check |

### File Uploader (Port 5002)

| Endpoint | Description |
|----------|-------------|
| `http://localhost:5002/upload` | File upload |
| `http://localhost:5002/health` | Health check |
| `http://localhost:5002/categories` | Upload categories |

---

## Configuration

### Environment Variables

Key configuration in `.env.staging`:

```bash
# Storage Configuration
AWS_S3_BUCKET_NAME=your-staging-bucket
AWS_S3_ACCESS_KEY=your-access-key
AWS_S3_SECRET_KEY=your-secret-key

# Security
YENDOR_JWT_SECRET=your-jwt-secret-32-chars-min

# Environment
ASPNETCORE_ENVIRONMENT=Staging
CONTAINERIZED_BUILD=true
```

### CORS Configuration

The system is configured for cross-origin requests:

- **Frontend**: Nginx proxy handles CORS headers
- **API**: Permissive CORS policy for staging
- **Uploader**: CORS enabled for file uploads

---

## Monitoring & Health Checks

### Health Check Endpoints

All services provide health checks:

```bash
# Frontend health
curl http://localhost/health
# Response: "healthy"

# API health  
curl http://localhost:5003/health
# Response: JSON with service info

# Uploader health
curl http://localhost:5002/health
# Response: JSON with service status
```

### Container Health

Docker health checks are configured:

```bash
# Check container health
docker ps --format "table {{.Names}}\t{{.Status}}"

# View health check logs
docker inspect yendorcats-frontend-staging | grep -A 10 Health
```

---

## Troubleshooting

### Common Issues

#### Services Not Starting

```bash
# Check logs
docker-compose -f docker-compose.staging.yml logs

# Check specific service
docker-compose -f docker-compose.staging.yml logs api
```

#### CORS Errors

- Verify frontend proxy configuration
- Check API CORS settings
- Ensure `CONTAINERIZED_BUILD=true` is set

#### Database Issues

```bash
# Check API logs for database errors
docker-compose -f docker-compose.staging.yml logs api | grep -i database

# Access database directly
docker exec -it yendorcats-api-staging sqlite3 /app/data/yendorcats.db
```

#### Storage Connection Issues

- Verify S3/B2 credentials in `.env.staging`
- Check network connectivity
- Review storage service logs

### Useful Commands

```bash
# Restart specific service
docker-compose -f docker-compose.staging.yml restart api

# Rebuild and restart
docker-compose -f docker-compose.staging.yml build --no-cache api
docker-compose -f docker-compose.staging.yml up -d

# Clean up
docker-compose -f docker-compose.staging.yml down
docker system prune -f
```

---

## Next Steps: ECR Deployment

### Prepare for ECR

1. **Tag images for ECR**:
```bash
docker tag yendorcats/frontend:staging <account>.dkr.ecr.<region>.amazonaws.com/yendorcats/frontend:staging
docker tag yendorcats/api:staging <account>.dkr.ecr.<region>.amazonaws.com/yendorcats/api:staging
docker tag yendorcats/uploader:staging <account>.dkr.ecr.<region>.amazonaws.com/yendorcats/uploader:staging
```

2. **Push to ECR**:
```bash
aws ecr get-login-password --region <region> | docker login --username AWS --password-stdin <account>.dkr.ecr.<region>.amazonaws.com
docker push <account>.dkr.ecr.<region>.amazonaws.com/yendorcats/frontend:staging
docker push <account>.dkr.ecr.<region>.amazonaws.com/yendorcats/api:staging
docker push <account>.dkr.ecr.<region>.amazonaws.com/yendorcats/uploader:staging
```

---

**Tags**: #docker #staging #deployment #containerization #microservices #nginx #dotnet #nodejs #cors #health-checks

---
