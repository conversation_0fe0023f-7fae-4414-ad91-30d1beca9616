#!/bin/bash

# YendorCats Development Runner
# Loads environment variables and starts the API

echo "🚀 Starting YendorCats API in Development Mode"
echo "=============================================="

# Load environment variables from .env file
if [ -f ".env" ]; then
    echo "📄 Loading environment variables from .env..."
    export $(cat .env | grep -v '^#' | xargs)
    echo "✅ Environment variables loaded"
else
    echo "⚠️  No .env file found. Using default configuration."
fi

# Navigate to API directory
cd backend/YendorCats.API

echo "🔧 Building and running API..."
dotnet run

echo "🏁 API stopped"
