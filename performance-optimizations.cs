// Performance Optimization Strategies for Hybrid Storage Architecture

using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.EntityFrameworkCore;

namespace YendorCats.API.Services
{
    // Multi-level caching strategy
    public class OptimizedGalleryService : IGalleryService
    {
        private readonly AppDbContext _context;
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly IB2StorageService _b2Storage;
        private readonly ILogger<OptimizedGalleryService> _logger;

        // Cache configuration
        private readonly TimeSpan _memoryCacheExpiry = TimeSpan.FromMinutes(5);
        private readonly TimeSpan _distributedCacheExpiry = TimeSpan.FromMinutes(30);
        private const string CATEGORY_CACHE_PREFIX = "gallery_category_";
        private const string IMAGE_CACHE_PREFIX = "gallery_image_";

        public async Task<PagedResult<GalleryImageDto>> GetCategoryImagesAsync(
            string category, 
            int page = 1, 
            int pageSize = 20, 
            string sortBy = "DateTaken", 
            bool descending = true)
        {
            var cacheKey = $"{CATEGORY_CACHE_PREFIX}{category}_{page}_{pageSize}_{sortBy}_{descending}";
            
            // 1. Check memory cache first (fastest)
            if (_memoryCache.TryGetValue(cacheKey, out PagedResult<GalleryImageDto> cachedResult))
            {
                _logger.LogDebug("Cache hit (memory): {CacheKey}", cacheKey);
                return cachedResult;
            }
            
            // 2. Check distributed cache (Redis)
            var distributedCached = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(distributedCached))
            {
                var result = JsonSerializer.Deserialize<PagedResult<GalleryImageDto>>(distributedCached);
                
                // Store in memory cache for next request
                _memoryCache.Set(cacheKey, result, _memoryCacheExpiry);
                
                _logger.LogDebug("Cache hit (distributed): {CacheKey}", cacheKey);
                return result;
            }
            
            // 3. Query database with optimizations
            var query = _context.CatGalleryImages
                .AsNoTracking() // Read-only, no change tracking overhead
                .Where(img => img.Category == category && img.IsActive && img.IsPublic);
            
            // Dynamic sorting
            query = sortBy.ToLower() switch
            {
                "name" => descending ? query.OrderByDescending(img => img.CatName) : query.OrderBy(img => img.CatName),
                "date" => descending ? query.OrderByDescending(img => img.DateTaken) : query.OrderBy(img => img.DateTaken),
                "upload" => descending ? query.OrderByDescending(img => img.DateUploaded) : query.OrderBy(img => img.DateUploaded),
                _ => query.OrderByDescending(img => img.DateTaken)
            };
            
            // Get total count efficiently
            var totalCount = await query.CountAsync();
            
            // Get paged results with projection to DTO
            var images = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(img => new GalleryImageDto
                {
                    Id = img.Id,
                    B2Key = img.B2Key,
                    ImageUrl = img.B2PublicUrl,
                    ThumbnailUrl = img.ThumbnailB2Key != null 
                        ? $"https://f002.backblazeb2.com/file/{img.B2BucketName}/{img.ThumbnailB2Key}"
                        : img.B2PublicUrl,
                    CatName = img.CatName,
                    CatId = img.CatId,
                    Category = img.Category,
                    Title = img.Title,
                    Description = img.Description,
                    AgeAtPhoto = img.AgeAtPhoto,
                    Breed = img.Breed,
                    Bloodline = img.Bloodline,
                    Gender = img.Gender,
                    DateTaken = img.DateTaken,
                    DateUploaded = img.DateUploaded,
                    Width = img.Width,
                    Height = img.Height,
                    AspectRatio = img.AspectRatio,
                    FileSize = img.FileSize
                })
                .ToListAsync();
            
            var pagedResult = new PagedResult<GalleryImageDto>
            {
                Items = images,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                HasNext = page * pageSize < totalCount,
                HasPrevious = page > 1
            };
            
            // Cache the result
            var serializedResult = JsonSerializer.Serialize(pagedResult);
            await _distributedCache.SetStringAsync(cacheKey, serializedResult, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _distributedCacheExpiry
            });
            
            _memoryCache.Set(cacheKey, pagedResult, _memoryCacheExpiry);
            
            _logger.LogDebug("Database query executed and cached: {CacheKey}", cacheKey);
            return pagedResult;
        }

        // Optimized single image retrieval
        public async Task<GalleryImageDto> GetImageAsync(long imageId)
        {
            var cacheKey = $"{IMAGE_CACHE_PREFIX}{imageId}";
            
            if (_memoryCache.TryGetValue(cacheKey, out GalleryImageDto cachedImage))
            {
                return cachedImage;
            }
            
            var image = await _context.CatGalleryImages
                .AsNoTracking()
                .Where(img => img.Id == imageId && img.IsActive && img.IsPublic)
                .Select(img => new GalleryImageDto
                {
                    Id = img.Id,
                    B2Key = img.B2Key,
                    ImageUrl = img.B2PublicUrl,
                    ThumbnailUrl = img.ThumbnailB2Key != null 
                        ? $"https://f002.backblazeb2.com/file/{img.B2BucketName}/{img.ThumbnailB2Key}"
                        : img.B2PublicUrl,
                    CatName = img.CatName,
                    CatId = img.CatId,
                    Category = img.Category,
                    Title = img.Title,
                    Description = img.Description,
                    AgeAtPhoto = img.AgeAtPhoto,
                    Breed = img.Breed,
                    Bloodline = img.Bloodline,
                    Gender = img.Gender,
                    DateTaken = img.DateTaken,
                    DateUploaded = img.DateUploaded,
                    Width = img.Width,
                    Height = img.Height,
                    AspectRatio = img.AspectRatio,
                    FileSize = img.FileSize
                })
                .FirstOrDefaultAsync();
            
            if (image != null)
            {
                _memoryCache.Set(cacheKey, image, _memoryCacheExpiry);
                
                // Update access tracking asynchronously
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _context.Database.ExecuteSqlRawAsync(
                            "UPDATE CatGalleryImages SET AccessCount = AccessCount + 1, LastAccessedAt = GETUTCDATE() WHERE Id = {0}",
                            imageId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to update access count for image {ImageId}", imageId);
                    }
                });
            }
            
            return image;
        }

        // Cache invalidation when images are modified
        public async Task InvalidateCacheAsync(string category = null, long? imageId = null)
        {
            if (imageId.HasValue)
            {
                var imageKey = $"{IMAGE_CACHE_PREFIX}{imageId}";
                _memoryCache.Remove(imageKey);
                await _distributedCache.RemoveAsync(imageKey);
            }
            
            if (!string.IsNullOrEmpty(category))
            {
                // Remove all category cache entries
                var categoryPattern = $"{CATEGORY_CACHE_PREFIX}{category}_*";
                await InvalidateCachePattern(categoryPattern);
            }
        }

        private async Task InvalidateCachePattern(string pattern)
        {
            // This would require a more sophisticated cache implementation
            // For Redis, you could use SCAN with pattern matching
            // For now, we'll implement a simple approach
            
            if (_distributedCache is IDistributedCacheWithPatternSupport patternCache)
            {
                await patternCache.RemoveByPatternAsync(pattern);
            }
        }
    }

    // Thumbnail generation service for performance
    public class ThumbnailService : IThumbnailService
    {
        private readonly IB2StorageService _b2Storage;
        private readonly AppDbContext _context;
        private readonly ILogger<ThumbnailService> _logger;

        public async Task GenerateThumbnailsAsync(long imageId)
        {
            var image = await _context.CatGalleryImages.FindAsync(imageId);
            if (image == null) return;

            try
            {
                // Download original image
                var originalStream = await _b2Storage.DownloadFileAsync(image.B2Key);
                
                // Generate multiple thumbnail sizes
                var thumbnailSizes = new[]
                {
                    new { Width = 300, Height = 200, Suffix = "_thumb" },
                    new { Width = 600, Height = 400, Suffix = "_medium" },
                    new { Width = 150, Height = 150, Suffix = "_square" }
                };

                foreach (var size in thumbnailSizes)
                {
                    using var thumbnailStream = await ResizeImageAsync(originalStream, size.Width, size.Height);
                    
                    var thumbnailKey = image.B2Key.Replace(
                        Path.GetExtension(image.B2Key),
                        $"{size.Suffix}{Path.GetExtension(image.B2Key)}");
                    
                    var uploadResult = await _b2Storage.UploadFileAsync(
                        thumbnailStream, thumbnailKey, image.Category);
                    
                    if (uploadResult.Success && size.Suffix == "_thumb")
                    {
                        // Update database with thumbnail reference
                        image.ThumbnailB2Key = thumbnailKey;
                        await _context.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate thumbnails for image {ImageId}", imageId);
            }
        }

        private async Task<Stream> ResizeImageAsync(Stream originalStream, int width, int height)
        {
            // Use ImageSharp or similar library for image processing
            // This is a placeholder implementation
            using var image = await Image.LoadAsync(originalStream);
            
            image.Mutate(x => x.Resize(new ResizeOptions
            {
                Size = new Size(width, height),
                Mode = ResizeMode.Crop,
                Position = AnchorPositionMode.Center
            }));
            
            var outputStream = new MemoryStream();
            await image.SaveAsJpegAsync(outputStream, new JpegEncoder { Quality = 85 });
            outputStream.Position = 0;
            
            return outputStream;
        }
    }

    // Background service for cache warming
    public class CacheWarmupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<CacheWarmupService> _logger;

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Wait for application to start
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var galleryService = scope.ServiceProvider.GetRequiredService<IGalleryService>();

                    // Warm up cache for popular categories
                    var categories = new[] { "studs", "queens", "kittens", "gallery" };
                    
                    foreach (var category in categories)
                    {
                        // Pre-load first page of each category
                        await galleryService.GetCategoryImagesAsync(category, 1, 20);
                        await Task.Delay(1000, stoppingToken); // Avoid overwhelming the system
                    }

                    _logger.LogInformation("Cache warmup completed");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Cache warmup failed");
                }

                // Run every 30 minutes
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }
    }
}
