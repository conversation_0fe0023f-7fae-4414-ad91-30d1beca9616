// Comprehensive Synchronization Strategies for Hybrid Storage Architecture

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;

namespace YendorCats.API.Services
{
    // Strategy 1: Real-time Sync Service
    public class B2SyncService : IB2SyncService
    {
        private readonly IB2StorageService _b2Storage;
        private readonly AppDbContext _context;
        private readonly ILogger<B2SyncService> _logger;

        public async Task<bool> UploadImageWithMetadata(
            Stream imageStream, 
            string fileName, 
            CatImageMetadata metadata)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // 1. Upload to B2 first
                var b2Result = await _b2Storage.UploadFileAsync(
                    imageStream, fileName, metadata.Category);
                
                if (!b2Result.Success)
                {
                    throw new Exception($"B2 upload failed: {b2Result.ErrorMessage}");
                }
                
                // 2. Save metadata to database
                var dbImage = new CatGalleryImage
                {
                    B2Key = b2Result.B2Key,
                    B2FileId = b2Result.FileId,
                    B2BucketName = _b2Storage.BucketName,
                    OriginalFileName = fileName,
                    FileSize = imageStream.Length,
                    CatName = metadata.CatName,
                    Category = metadata.Category,
                    DateUploaded = DateTime.UtcNow,
                    // ... other metadata fields
                };
                
                _context.CatGalleryImages.Add(dbImage);
                await _context.SaveChangesAsync();
                
                // 3. Log successful sync
                await LogSyncOperation(b2Result.B2Key, "INSERT", "SUCCESS");
                
                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                await LogSyncOperation(fileName, "INSERT", "FAILED", ex.Message);
                _logger.LogError(ex, "Failed to upload image with metadata: {FileName}", fileName);
                return false;
            }
        }

        public async Task<bool> DeleteImageWithMetadata(string b2Key)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            
            try
            {
                // 1. Delete from database first (safer - can recover from B2)
                var dbImage = await _context.CatGalleryImages
                    .FirstOrDefaultAsync(img => img.B2Key == b2Key);
                
                if (dbImage != null)
                {
                    _context.CatGalleryImages.Remove(dbImage);
                    await _context.SaveChangesAsync();
                }
                
                // 2. Delete from B2
                var b2Result = await _b2Storage.DeleteFileAsync(b2Key);
                
                if (!b2Result.Success)
                {
                    // Log but don't fail - file might already be deleted
                    _logger.LogWarning("B2 deletion failed for {B2Key}: {Error}", 
                        b2Key, b2Result.ErrorMessage);
                }
                
                await LogSyncOperation(b2Key, "DELETE", "SUCCESS");
                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                await LogSyncOperation(b2Key, "DELETE", "FAILED", ex.Message);
                _logger.LogError(ex, "Failed to delete image: {B2Key}", b2Key);
                return false;
            }
        }

        private async Task LogSyncOperation(string b2Key, string operation, string status, string errorMessage = null)
        {
            var syncLog = new B2SyncLog
            {
                B2Key = b2Key,
                Operation = operation,
                Status = status,
                ErrorMessage = errorMessage,
                SyncedAt = DateTime.UtcNow
            };
            
            _context.B2SyncLogs.Add(syncLog);
            await _context.SaveChangesAsync();
        }
    }

    // Strategy 2: Background Verification Service
    public class B2VerificationService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<B2VerificationService> _logger;
        private readonly TimeSpan _verificationInterval = TimeSpan.FromHours(6);

        public B2VerificationService(IServiceProvider serviceProvider, ILogger<B2VerificationService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var verificationService = scope.ServiceProvider.GetRequiredService<IB2VerificationService>();
                    
                    await verificationService.VerifyDatabaseB2Sync();
                    
                    _logger.LogInformation("B2 verification completed successfully");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "B2 verification failed");
                }
                
                await Task.Delay(_verificationInterval, stoppingToken);
            }
        }
    }

    public interface IB2VerificationService
    {
        Task<VerificationResult> VerifyDatabaseB2Sync();
        Task<List<OrphanedFile>> FindOrphanedB2Files();
        Task<List<MissingFile>> FindMissingB2Files();
        Task RepairSyncIssues(VerificationResult result);
    }

    public class B2VerificationService : IB2VerificationService
    {
        private readonly IB2StorageService _b2Storage;
        private readonly AppDbContext _context;
        private readonly ILogger<B2VerificationService> _logger;

        public async Task<VerificationResult> VerifyDatabaseB2Sync()
        {
            var result = new VerificationResult();
            
            // 1. Check for database records without B2 files
            var dbImages = await _context.CatGalleryImages
                .Select(img => new { img.B2Key, img.Id })
                .ToListAsync();
            
            var b2Files = await _b2Storage.ListAllFilesAsync();
            var b2Keys = b2Files.Select(f => f.FileName).ToHashSet();
            
            result.MissingB2Files = dbImages
                .Where(db => !b2Keys.Contains(db.B2Key))
                .Select(db => new MissingFile { B2Key = db.B2Key, DatabaseId = db.Id })
                .ToList();
            
            // 2. Check for B2 files without database records
            var dbKeys = dbImages.Select(db => db.B2Key).ToHashSet();
            
            result.OrphanedB2Files = b2Files
                .Where(b2 => !dbKeys.Contains(b2.FileName))
                .Select(b2 => new OrphanedFile 
                { 
                    B2Key = b2.FileName, 
                    FileSize = b2.ContentLength,
                    LastModified = b2.UploadTimestamp 
                })
                .ToList();
            
            // 3. Verify file sizes and checksums for critical images
            await VerifyFileSizes(result, dbImages.Take(100)); // Sample verification
            
            _logger.LogInformation("Verification complete: {MissingCount} missing, {OrphanedCount} orphaned", 
                result.MissingB2Files.Count, result.OrphanedB2Files.Count);
            
            return result;
        }

        public async Task RepairSyncIssues(VerificationResult result)
        {
            // 1. Handle missing B2 files (remove from database or mark as inactive)
            foreach (var missing in result.MissingB2Files)
            {
                var dbImage = await _context.CatGalleryImages
                    .FirstOrDefaultAsync(img => img.B2Key == missing.B2Key);
                
                if (dbImage != null)
                {
                    // Option 1: Mark as inactive instead of deleting
                    dbImage.IsActive = false;
                    dbImage.DateModified = DateTime.UtcNow;
                    
                    // Option 2: Delete completely (more aggressive)
                    // _context.CatGalleryImages.Remove(dbImage);
                    
                    await LogSyncOperation(missing.B2Key, "REPAIR_MISSING", "SUCCESS", 
                        "Marked inactive due to missing B2 file");
                }
            }
            
            // 2. Handle orphaned B2 files (create database records or delete files)
            foreach (var orphaned in result.OrphanedB2Files)
            {
                try
                {
                    // Try to extract metadata from filename/path
                    var metadata = ExtractMetadataFromB2Key(orphaned.B2Key);
                    
                    if (metadata != null)
                    {
                        // Create database record for orphaned file
                        var dbImage = new CatGalleryImage
                        {
                            B2Key = orphaned.B2Key,
                            B2BucketName = _b2Storage.BucketName,
                            OriginalFileName = Path.GetFileName(orphaned.B2Key),
                            FileSize = orphaned.FileSize,
                            Category = metadata.Category,
                            CatName = metadata.CatName,
                            DateUploaded = orphaned.LastModified,
                            DateModified = DateTime.UtcNow,
                            CreatedBy = "AUTO_REPAIR"
                        };
                        
                        _context.CatGalleryImages.Add(dbImage);
                        
                        await LogSyncOperation(orphaned.B2Key, "REPAIR_ORPHANED", "SUCCESS", 
                            "Created database record for orphaned B2 file");
                    }
                    else
                    {
                        // Cannot determine metadata - log for manual review
                        _logger.LogWarning("Cannot auto-repair orphaned file: {B2Key}", orphaned.B2Key);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to repair orphaned file: {B2Key}", orphaned.B2Key);
                }
            }
            
            await _context.SaveChangesAsync();
        }

        private CatImageMetadata ExtractMetadataFromB2Key(string b2Key)
        {
            // Extract category from path: "studs/cat-name-2024-01-15.jpg"
            var parts = b2Key.Split('/');
            if (parts.Length >= 2)
            {
                var category = parts[0];
                var fileName = parts[^1];
                
                // Try to extract cat name from filename
                var nameMatch = Regex.Match(fileName, @"^([^-]+)");
                var catName = nameMatch.Success ? nameMatch.Groups[1].Value : "Unknown";
                
                return new CatImageMetadata
                {
                    Category = category,
                    CatName = catName
                };
            }
            
            return null;
        }
    }

    // Strategy 3: Event-Driven Sync with Webhooks (if B2 supports)
    public class B2WebhookHandler : IB2WebhookHandler
    {
        private readonly IB2SyncService _syncService;
        private readonly ILogger<B2WebhookHandler> _logger;

        [HttpPost("api/webhooks/b2")]
        public async Task<IActionResult> HandleB2Webhook([FromBody] B2WebhookPayload payload)
        {
            try
            {
                switch (payload.EventType)
                {
                    case "file.uploaded":
                        await HandleFileUploaded(payload);
                        break;
                    case "file.deleted":
                        await HandleFileDeleted(payload);
                        break;
                    default:
                        _logger.LogInformation("Unhandled B2 webhook event: {EventType}", payload.EventType);
                        break;
                }
                
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process B2 webhook: {EventType}", payload.EventType);
                return StatusCode(500);
            }
        }

        private async Task HandleFileUploaded(B2WebhookPayload payload)
        {
            // Check if we have a database record for this file
            var existingRecord = await _context.CatGalleryImages
                .FirstOrDefaultAsync(img => img.B2Key == payload.FileName);
            
            if (existingRecord == null)
            {
                // File uploaded outside our system - create database record
                var metadata = ExtractMetadataFromB2Key(payload.FileName);
                if (metadata != null)
                {
                    await _syncService.CreateDatabaseRecordForExistingB2File(payload);
                }
            }
        }

        private async Task HandleFileDeleted(B2WebhookPayload payload)
        {
            // Remove database record for deleted file
            var dbRecord = await _context.CatGalleryImages
                .FirstOrDefaultAsync(img => img.B2Key == payload.FileName);
            
            if (dbRecord != null)
            {
                _context.CatGalleryImages.Remove(dbRecord);
                await _context.SaveChangesAsync();
                
                await LogSyncOperation(payload.FileName, "WEBHOOK_DELETE", "SUCCESS");
            }
        }
    }
}
