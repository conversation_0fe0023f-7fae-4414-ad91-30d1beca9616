# YendorCats Deployment Guide

This comprehensive guide outlines the complete deployment process for the YendorCats application. Follow these instructions to set up development, staging, or production environments successfully.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Prerequisites](#prerequisites)
3. [Environment Setup](#environment-setup)
4. [Configuration Options](#configuration-options)
5. [Deployment Methods](#deployment-methods)
   - [Quick Deployment with B2](#quick-deployment-with-b2)
   - [Standard Deployment](#standard-deployment)
   - [Production Deployment](#production-deployment)
6. [Verification Steps](#verification-steps)
7. [Database Management](#database-management)
8. [Storage Management](#storage-management)
9. [Maintenance Procedures](#maintenance-procedures)
10. [Backup and Recovery](#backup-and-recovery)
11. [Troubleshooting](#troubleshooting)
12. [Security Considerations](#security-considerations)
13. [Performance Optimization](#performance-optimization)
14. [Future Roadmap](#future-roadmap)

## System Architecture

YendorCats is a containerized application using the following technology stack:

- **Backend**: .NET Core API
- **Database**: MariaDB
- **Storage**: Backblaze B2 (S3-compatible API)
- **Frontend**: Static HTML/JS/CSS served via Nginx
- **Container Orchestration**: Docker Compose
- **Image Processing**: ImageSharp
- **Authentication**: JWT Bearer

The system uses a hybrid storage approach, combining database storage for metadata and B2 object storage for images.

### Architecture Diagram

```
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│   Frontend   │◄────►│   Backend    │◄────►│   Database   │
│    (Nginx)   │      │  (.NET API)  │      │   (MariaDB)  │
└──────────────┘      └──────────────┘      └──────────────┘
                            │
                            ▼
                      ┌──────────────┐      ┌──────────────┐
                      │  File Upload │◄────►│  Backblaze   │
                      │   Service    │      │  B2 Storage  │
                      └──────────────┘      └──────────────┘
```

## Prerequisites

- Docker Engine (20.10.x or newer)
- Docker Compose (v2.x or newer)
- Git
- Backblaze B2 account with:
  - B2 bucket created
  - Application key with appropriate permissions
- 2GB RAM minimum (4GB recommended)
- 10GB free disk space minimum

### System Requirements

| Environment | CPU     | RAM   | Storage | Network         |
|-------------|---------|-------|---------|-----------------|
| Development | 2 cores | 2GB   | 10GB    | Local access    |
| Staging     | 2 cores | 4GB   | 20GB    | Internal network|
| Production  | 4 cores | 8GB   | 40GB    | Public access   |

## Environment Setup

1. **Clone the repository**:
   ```bash
   git clone git@fjordev:fjord-an/yendorcats.git
   cd yendorcats/src
   ```

2. **Create environment configuration**:
   ```bash
   cp .env.template .env
   ```

3. **Edit the `.env` file** with your actual values:
   ```
   # Database Configuration
   MYSQL_USER=yendorcats
   MYSQL_PASSWORD=your_secure_password_here
   MYSQL_DATABASE=YendorCats
   
   # JWT Security
   YENDOR_JWT_SECRET=your_jwt_secret_key_here_minimum_32_characters
   
   # B2 Storage Credentials
   B2_APPLICATION_KEY_ID=your_b2_key_id_here
   B2_APPLICATION_KEY=your_b2_application_key_here
   B2_BUCKET_ID=your_b2_bucket_id_here
   
   # AWS Credentials (for S3-compatible B2 API)
   AWS_ACCESS_KEY_ID=${B2_APPLICATION_KEY_ID}
   AWS_SECRET_ACCESS_KEY=${B2_APPLICATION_KEY}
   AWS_REGION=us-west-004
   
   # Optional: Additional B2 Settings
   B2_DOWNLOAD_URL_BASE=https://f004.backblazeb2.com/file
   B2_REGION=us-west-004
   
   # Application Settings
   ASPNETCORE_ENVIRONMENT=Production
   ASPNETCORE_URLS=http://+:5000
   ```

4. **Ensure proper permissions** for deployment scripts:
   ```bash
   chmod +x deploy-with-b2.sh
   chmod +x backend/YendorCats.API/verify-wwwroot.sh
   chmod +x backend/YendorCats.API/watch-frontend.sh
   chmod +x backend/YendorCats.API/Data/build-db.sh
   chmod +x backend/YendorCats.API/Data/init-db.sh
   ```

## Configuration Options

### Database Configuration

YendorCats supports both MariaDB and SQLite as database providers. The default configuration uses MariaDB for production and SQLite for development.

#### MariaDB (Recommended for Production)

Configuration in `appsettings.json`:
```json
"ConnectionStrings": {
  "DefaultConnection": "Server=db;Database=YendorCats;User=yendorcats;Password=your_password;Port=3306;"
}
```

#### SQLite (Suitable for Development)

Configuration in `appsettings.Development.json`:
```json
"ConnectionStrings": {
  "DefaultConnection": "Data Source=Data/yendorcats.db"
}
```

### Storage Configuration

YendorCats uses Backblaze B2 as the primary storage provider. Configuration is managed in `appsettings.json`:

```json
"StorageProvider": {
  "Type": "B2",
  "B2": {
    "ApplicationKeyId": "your_b2_key_id",
    "ApplicationKey": "your_b2_application_key",
    "BucketId": "your_b2_bucket_id"
  },
  "HybridStorage": {
    "Enabled": true,
    "MetadataInDatabase": true,
    "ImagesInS3": true,
    "SyncEnabled": true,
    "SyncIntervalMinutes": 15
  }
}
```

## Deployment Methods

### Quick Deployment with B2

For a streamlined deployment using Backblaze B2 storage:

1. Ensure `.env` file is configured with proper B2 credentials
2. Run the deployment script:
   ```bash
   ./deploy-with-b2.sh
   ```

This script will:
- Validate environment variables
- Build and start containers
- Test database connection
- Verify API endpoint

### Standard Deployment

For standard development or staging environments:

1. Ensure `.env` file is configured
2. Run Docker Compose:
   ```bash
   docker-compose down --remove-orphans
   docker-compose build
   docker-compose up -d
   ```

3. Initialize the database (first run only):
   ```bash
   docker-compose exec api dotnet YendorCats.API.dll --init-db
   ```

### Production Deployment

For production environments:

1. Ensure `.env` file is configured with production values
2. Run using the production configuration:
   ```bash
   docker-compose -f docker-compose.production.yml down --remove-orphans
   docker-compose -f docker-compose.production.yml build
   docker-compose -f docker-compose.production.yml up -d
   ```

3. Set up a reverse proxy (like Nginx or Traefik) for SSL/TLS termination
4. Configure appropriate firewall rules

## Verification Steps

After deployment, verify the system is functioning correctly:

1. **Check container status**:
   ```bash
   docker-compose ps
   ```
   All containers should show status as "Up"

2. **Verify API health**:
   ```bash
   curl http://localhost:5003/health
   ```
   Should return a 200 OK response

3. **Test database connection**:
   ```bash
   docker-compose exec db mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;"
   ```
   Should execute successfully

4. **Check frontend access**:
   - Open `http://localhost:5003` in a browser
   - Verify the homepage loads correctly

5. **Test admin access**:
   - Open `http://localhost:5003/admin.html` in a browser
   - Log in with default admin credentials (if not changed)

6. **Verify B2 integration**:
   ```bash
   docker-compose logs api | grep "B2 sync"
   ```
   Should show successful synchronization messages

## Database Management

### Migrations

Database migrations are managed through Entity Framework Core:

1. **Apply pending migrations**:
   ```bash
   docker-compose exec api dotnet ef database update
   ```

2. **Create a new migration**:
   ```bash
   docker-compose exec api dotnet ef migrations add MigrationName
   ```

### Backup and Restore

1. **Backup database**:
   ```bash
   docker-compose exec db mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" YendorCats > backup_$(date +%Y%m%d).sql
   ```

2. **Restore database**:
   ```bash
   docker-compose exec -T db mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" YendorCats < backup_file.sql
   ```

## Storage Management

### B2 Storage Synchronization

YendorCats automatically synchronizes data between the local database and B2 storage. The synchronization interval is configured in `appsettings.json`:

```json
"HybridStorage": {
  "SyncEnabled": true,
  "SyncIntervalMinutes": 15
}
```

To manually trigger synchronization:

```bash
curl -X POST http://localhost:5003/api/admin/storage/sync
```

### Monitoring Storage Usage

1. **Check B2 storage usage**:
   ```bash
   docker-compose exec api dotnet YendorCats.API.dll --storage-stats
   ```

2. **View synchronization logs**:
   ```bash
   docker-compose exec api dotnet YendorCats.API.dll --sync-logs
   ```

## Maintenance Procedures

### Regular Maintenance Tasks

1. **Update containers**:
   ```bash
   docker-compose pull
   docker-compose up -d
   ```

2. **Clean up unused Docker resources**:
   ```bash
   docker system prune -af
   ```

3. **Rotate log files**:
   ```bash
   docker-compose exec api find /app/Logs -name "*.log" -type f -mtime +30 -delete
   ```

4. **Check database performance**:
   ```bash
   docker-compose exec db mysqltuner
   ```

### Scaling Considerations

For higher load environments:
- Increase container resources in `docker-compose.yml`
- Consider adding a CDN for image delivery
- Implement Redis caching for API responses
- Set up load balancing for multiple API instances

## Backup and Recovery

### Comprehensive Backup Strategy

1. **Database backup** (daily):
   ```bash
   docker-compose exec db mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" YendorCats > backup_$(date +%Y%m%d).sql
   ```

2. **Volume backup** (weekly):
   ```bash
   docker run --rm -v yendorcats_api-data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/api-data-$(date +%Y%m%d).tar.gz -C /source .
   docker run --rm -v yendorcats_api-logs:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/api-logs-$(date +%Y%m%d).tar.gz -C /source .
   docker run --rm -v yendorcats_mariadb-data:/source -v $(pwd)/backups:/backup alpine tar -czf /backup/mariadb-data-$(date +%Y%m%d).tar.gz -C /source .
   ```

3. **Configuration backup** (on change):
   ```bash
   cp .env backups/.env.$(date +%Y%m%d)
   cp backend/YendorCats.API/appsettings.json backups/appsettings.json.$(date +%Y%m%d)
   ```

4. **B2 storage** is already redundant, but consider:
   - Setting up B2 bucket versioning
   - Creating lifecycle rules for archiving

### Recovery Procedures

1. **Database recovery**:
   ```bash
   docker-compose exec -T db mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" YendorCats < backup_file.sql
   ```

2. **Volume recovery**:
   ```bash
   docker run --rm -v yendorcats_api-data:/target -v $(pwd)/backups:/backup alpine sh -c "rm -rf /target/* && tar -xzf /backup/api-data-YYYYMMDD.tar.gz -C /target"
   ```

3. **Complete system recovery**:
   ```bash
   # 1. Restore config files
   cp backups/.env.YYYYMMDD .env
   cp backups/appsettings.json.YYYYMMDD backend/YendorCats.API/appsettings.json
   
   # 2. Rebuild and start containers
   docker-compose down -v
   docker-compose up -d
   
   # 3. Restore database
   docker-compose exec -T db mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" YendorCats < backup_file.sql
   ```

## Troubleshooting

### Common Issues and Solutions

#### Database Connection Failures

**Symptoms**: API logs show database connection errors, `Connection refused` messages

**Solutions**:
1. Check if database container is running:
   ```bash
   docker-compose ps db
   ```
2. Verify database credentials in `.env` file
3. Try restarting the database:
   ```bash
   docker-compose restart db
   ```
4. Check database logs:
   ```bash
   docker-compose logs db
   ```

#### B2 Storage Integration Issues

**Symptoms**: Files not appearing in gallery, sync errors in logs

**Solutions**:
1. Verify B2 credentials in `.env` file
2. Check API logs for B2 connection errors:
   ```bash
   docker-compose logs api | grep "B2"
   ```
3. Ensure B2 bucket has proper permissions
4. Manually trigger synchronization:
   ```bash
   curl -X POST http://localhost:5003/api/admin/storage/sync
   ```
5. Check B2 bucket through web interface to confirm uploads

#### Frontend Loading Issues

**Symptoms**: Frontend doesn't load, shows 404 errors, or displays incorrectly

**Solutions**:
1. Verify Nginx configuration:
   ```bash
   docker-compose exec frontend nginx -t
   ```
2. Check frontend logs:
   ```bash
   docker-compose logs frontend
   ```
3. Rebuild frontend container:
   ```bash
   docker-compose build frontend
   docker-compose up -d frontend
   ```
4. Verify wwwroot contents:
   ```bash
   docker-compose exec api ls -la /app/wwwroot
   ```

#### Performance Issues

**Symptoms**: Slow image loading, high API response times

**Solutions**:
1. Check database query performance:
   ```bash
   docker-compose exec db mysqltuner
   ```
2. Optimize image sizes in B2 storage
3. Enable caching in Nginx:
   ```bash
   # Add to nginx.conf
   proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=image_cache:10m max_size=1g inactive=60m;
   ```
4. Increase API container resources in `docker-compose.yml`

### Diagnostic Commands

1. **View all container logs**:
   ```bash
   docker-compose logs
   ```

2. **View API logs only**:
   ```bash
   docker-compose logs api
   ```

3. **Follow real-time logs**:
   ```bash
   docker-compose logs -f api
   ```

4. **Check container resource usage**:
   ```bash
   docker stats
   ```

5. **Inspect networking**:
   ```bash
   docker network inspect yendorcats-network
   ```

6. **Verify container environment variables**:
   ```bash
   docker-compose exec api env
   ```

## Security Considerations

### Recommended Security Measures

1. **Use secure passwords** for all services
2. **Keep JWT secret secure** and rotate it periodically
3. **Implement HTTPS** with proper SSL/TLS certificates
4. **Restrict network access** to only necessary ports
5. **Use least privilege principle** for B2 application keys
6. **Implement proper authentication** for admin endpoints
7. **Keep Docker and all containers updated** with security patches
8. **Consider migrating secrets to HashiCorp Vault** for enhanced security

### Production Security Hardening

1. **Enable database encryption**:
   ```
   # Add to MariaDB configuration
   docker-compose exec db mysql -e "SET GLOBAL encrypt_binlog=ON;"
   ```

2. **Configure Content Security Policy**:
   ```
   # Add to Nginx configuration
   add_header Content-Security-Policy "default-src 'self'; img-src 'self' https://f004.backblazeb2.com; script-src 'self';" always;
   ```

3. **Implement rate limiting**:
   ```
   # Add to Nginx configuration
   limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
   ```

4. **Set up fail2ban** for SSH and admin login attempts

5. **Use non-root users** in Docker containers:
   ```
   # Add to Dockerfile
   USER app
   ```

## Performance Optimization

### Optimization Targets

| Metric | Target Performance |
|--------|-------------------|
| Gallery Load | 50-200ms |
| API Response Time | < 100ms |
| Image Upload Time | < 2s |
| Concurrent Users | 100+ |

### Optimization Techniques

1. **Enable Nginx caching**:
   ```
   # Add to Nginx configuration
   proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=image_cache:10m max_size=1g inactive=60m;
   ```

2. **Optimize database queries**:
   - Create proper indexes
   - Use efficient query patterns
   - Implement query caching

3. **Use CDN for image delivery**:
   - Configure B2 with Cloudflare or other CDN
   - Update URLs in application to use CDN endpoints

4. **Implement browser caching**:
   ```
   # Add to Nginx configuration
   location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
       expires 30d;
       add_header Cache-Control "public, no-transform";
   }
   ```

5. **Enable compression**:
   ```
   # Add to Nginx configuration
   gzip on;
   gzip_comp_level 6;
   gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
   ```

## Future Roadmap

### Phase 1: Production Stabilization

- SSL/TLS termination setup
- Monitoring dashboards implementation
- Automated backup strategy
- Performance metrics collection
- Error alerting system

### Phase 2: Performance Optimization

- CDN integration for global performance
- Advanced caching strategies
- Database query optimization
- Image compression fine-tuning
- Load balancing configuration

### Phase 3: Enterprise Features

- HashiCorp Vault migration from environment variables
- Advanced secret rotation implementation
- Audit logging enhancement
- Multi-tenant architecture support
- API rate limiting advanced configuration

### Phase 4: Advanced Features

- AI-powered image tagging integration
- Advanced search capabilities
- Mobile API optimization
- Real-time notifications system
- Advanced analytics dashboard

### Phase 5: Innovation & Scaling

- Microservices architecture migration
- Kubernetes deployment option
- Multi-cloud strategy implementation
- GraphQL API development
- Advanced ML features for cat breed detection

---

This deployment guide provides comprehensive instructions for deploying and maintaining the YendorCats application. For questions or support, contact the development team.

Last updated: July 19, 2025
