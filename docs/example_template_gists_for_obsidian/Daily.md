---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: daily
tags: [daily]
---

# <% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>

## Tasks
- [ ]

## Journal
-

## Notes
-

## Projects
```dataview
LIST
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC
LIMIT 3
```

## Today's Meetings
```dataview
LIST
FROM #meeting
WHERE date = date(<% tp.date.now("YYYY-MM-DD") %>)
```

## Today's Tasks
```tasks
not done
due on <% tp.date.now("YYYY-MM-DD") %>
```

## Upcoming Tasks
```tasks
not done
due after <% tp.date.now("YYYY-MM-DD") %>
due before <% tp.date.now("YYYY-MM-DD", 7) %>
```

## Related
- [[Daily Notes TOC]]
- [[Tasks]]
- [[Home]]
