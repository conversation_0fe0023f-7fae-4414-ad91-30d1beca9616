---

# Vault Backup and Recovery Guide

## Overview

This guide covers comprehensive backup and recovery strategies for your HashiCorp Vault deployment, ensuring business continuity and data protection for your YendorCats multi-client setup.

## Tags
#vault #backup #recovery #disaster-recovery #encryption #automation #security #yendorcats

---

## Backup Strategy Overview

### What to Backup

1. **Vault Data** - All secrets and configuration
2. **Vault Configuration** - Server configuration files
3. **Unseal Keys** - Critical for recovery (store separately)
4. **Root Token** - Emergency access (store securely)
5. **Policies and Auth Methods** - Access control configuration
6. **Audit Logs** - Compliance and security monitoring

### Backup Types

- **Full Backup**: Complete Vault data snapshot
- **Incremental Backup**: Changes since last backup
- **Configuration Backup**: Policies, auth methods, mounts
- **Emergency Backup**: Unseal keys and root token

---

## File-Based Storage Backup

### 1. Simple File System Backup

```bash
#!/bin/bash
# vault-backup.sh

VAULT_DATA_DIR="/vault/data"
BACKUP_DIR="/secure/vault-backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="vault-backup-$DATE"

# Create backup directory
mkdir -p $BACKUP_DIR

# Stop Vault service (if safe to do so)
# systemctl stop vault

# Create compressed backup
tar -czf $BACKUP_DIR/$BACKUP_NAME.tar.gz -C /vault data config

# Restart Vault service
# systemctl start vault

# Encrypt backup
gpg --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 \
    --s2k-digest-algo SHA512 --s2k-count 65536 --symmetric \
    --output $BACKUP_DIR/$BACKUP_NAME.tar.gz.gpg \
    $BACKUP_DIR/$BACKUP_NAME.tar.gz

# Remove unencrypted backup
rm $BACKUP_DIR/$BACKUP_NAME.tar.gz

# Set secure permissions
chmod 600 $BACKUP_DIR/$BACKUP_NAME.tar.gz.gpg

echo "Backup completed: $BACKUP_DIR/$BACKUP_NAME.tar.gz.gpg"
```

### 2. Hot Backup (Without Stopping Vault)

```bash
#!/bin/bash
# vault-hot-backup.sh

VAULT_DATA_DIR="/vault/data"
BACKUP_DIR="/secure/vault-backups"
DATE=$(date +%Y%m%d_%H%M%S)
TEMP_DIR="/tmp/vault-backup-$DATE"

# Create temporary directory
mkdir -p $TEMP_DIR

# Use rsync for consistent copy
rsync -av --exclude='*.lock' $VAULT_DATA_DIR/ $TEMP_DIR/

# Create compressed backup
tar -czf $BACKUP_DIR/vault-hot-backup-$DATE.tar.gz -C $TEMP_DIR .

# Encrypt backup
gpg --symmetric --cipher-algo AES256 \
    --output $BACKUP_DIR/vault-hot-backup-$DATE.tar.gz.gpg \
    $BACKUP_DIR/vault-hot-backup-$DATE.tar.gz

# Cleanup
rm -rf $TEMP_DIR
rm $BACKUP_DIR/vault-hot-backup-$DATE.tar.gz

echo "Hot backup completed: vault-hot-backup-$DATE.tar.gz.gpg"
```

### 3. Incremental Backup

```bash
#!/bin/bash
# vault-incremental-backup.sh

VAULT_DATA_DIR="/vault/data"
BACKUP_DIR="/secure/vault-backups"
INCREMENTAL_DIR="$BACKUP_DIR/incremental"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $INCREMENTAL_DIR

# Find files modified in last 24 hours
find $VAULT_DATA_DIR -type f -mtime -1 > /tmp/changed-files.txt

if [ -s /tmp/changed-files.txt ]; then
    # Create incremental backup
    tar -czf $INCREMENTAL_DIR/vault-incremental-$DATE.tar.gz \
        -T /tmp/changed-files.txt
    
    # Encrypt
    gpg --symmetric --cipher-algo AES256 \
        --output $INCREMENTAL_DIR/vault-incremental-$DATE.tar.gz.gpg \
        $INCREMENTAL_DIR/vault-incremental-$DATE.tar.gz
    
    rm $INCREMENTAL_DIR/vault-incremental-$DATE.tar.gz
    echo "Incremental backup completed: vault-incremental-$DATE.tar.gz.gpg"
else
    echo "No changes detected, skipping incremental backup"
fi

rm /tmp/changed-files.txt
```

---

## Vault Snapshot API Backup

### 1. Using Vault Snapshot Command

```bash
#!/bin/bash
# vault-snapshot-backup.sh

VAULT_ADDR="https://localhost:8200"
BACKUP_DIR="/secure/vault-snapshots"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Create snapshot
vault operator raft snapshot save $BACKUP_DIR/vault-snapshot-$DATE.snap

if [ $? -eq 0 ]; then
    echo "Snapshot created successfully"
    
    # Encrypt snapshot
    gpg --symmetric --cipher-algo AES256 \
        --output $BACKUP_DIR/vault-snapshot-$DATE.snap.gpg \
        $BACKUP_DIR/vault-snapshot-$DATE.snap
    
    # Remove unencrypted snapshot
    rm $BACKUP_DIR/vault-snapshot-$DATE.snap
    
    echo "Encrypted snapshot: vault-snapshot-$DATE.snap.gpg"
else
    echo "Snapshot creation failed"
    exit 1
fi
```

### 2. Automated Snapshot with Retention

```bash
#!/bin/bash
# vault-snapshot-with-retention.sh

VAULT_ADDR="https://localhost:8200"
BACKUP_DIR="/secure/vault-snapshots"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# Create snapshot
vault operator raft snapshot save $BACKUP_DIR/vault-snapshot-$DATE.snap

if [ $? -eq 0 ]; then
    # Encrypt snapshot
    gpg --symmetric --cipher-algo AES256 \
        --output $BACKUP_DIR/vault-snapshot-$DATE.snap.gpg \
        $BACKUP_DIR/vault-snapshot-$DATE.snap
    
    rm $BACKUP_DIR/vault-snapshot-$DATE.snap
    
    # Clean old snapshots
    find $BACKUP_DIR -name "vault-snapshot-*.snap.gpg" \
         -mtime +$RETENTION_DAYS -delete
    
    echo "Snapshot backup completed with retention cleanup"
else
    echo "Snapshot backup failed"
    exit 1
fi
```

---

## Configuration Backup

### 1. Export Policies and Auth Methods

```bash
#!/bin/bash
# vault-config-backup.sh

BACKUP_DIR="/secure/vault-config-backups"
DATE=$(date +%Y%m%d_%H%M%S)
CONFIG_DIR="$BACKUP_DIR/config-$DATE"

mkdir -p $CONFIG_DIR

# Export all policies
echo "Exporting policies..."
vault policy list | while read policy; do
    if [ "$policy" != "default" ] && [ "$policy" != "root" ]; then
        vault policy read "$policy" > "$CONFIG_DIR/policy-$policy.hcl"
    fi
done

# Export auth methods
echo "Exporting auth methods..."
vault auth list -format=json > "$CONFIG_DIR/auth-methods.json"

# Export secret engines
echo "Exporting secret engines..."
vault secrets list -format=json > "$CONFIG_DIR/secret-engines.json"

# Export AppRole configurations
echo "Exporting AppRole configurations..."
if vault auth list | grep -q approle; then
    vault list auth/approle/role | while read role; do
        vault read auth/approle/role/"$role" -format=json > "$CONFIG_DIR/approle-$role.json"
    done
fi

# Create compressed archive
tar -czf "$BACKUP_DIR/vault-config-$DATE.tar.gz" -C "$BACKUP_DIR" "config-$DATE"

# Encrypt
gpg --symmetric --cipher-algo AES256 \
    --output "$BACKUP_DIR/vault-config-$DATE.tar.gz.gpg" \
    "$BACKUP_DIR/vault-config-$DATE.tar.gz"

# Cleanup
rm -rf "$CONFIG_DIR"
rm "$BACKUP_DIR/vault-config-$DATE.tar.gz"

echo "Configuration backup completed: vault-config-$DATE.tar.gz.gpg"
```

### 2. Client-Specific Configuration Backup

```bash
#!/bin/bash
# client-config-backup.sh

CLIENT_NAME="$1"
BACKUP_DIR="/secure/client-backups"
DATE=$(date +%Y%m%d_%H%M%S)

if [ -z "$CLIENT_NAME" ]; then
    echo "Usage: $0 <client-name>"
    exit 1
fi

mkdir -p "$BACKUP_DIR/$CLIENT_NAME"

# Backup client secrets
vault kv get -format=json secret/$CLIENT_NAME/yendorcats/app-secrets > \
    "$BACKUP_DIR/$CLIENT_NAME/secrets-$DATE.json"

# Backup client policy
vault policy read ${CLIENT_NAME}-policy > \
    "$BACKUP_DIR/$CLIENT_NAME/policy-$DATE.hcl"

# Backup client AppRole
vault read auth/approle/role/${CLIENT_NAME}-role -format=json > \
    "$BACKUP_DIR/$CLIENT_NAME/approle-$DATE.json"

# Create encrypted archive
tar -czf "$BACKUP_DIR/$CLIENT_NAME-backup-$DATE.tar.gz" \
    -C "$BACKUP_DIR" "$CLIENT_NAME"

gpg --symmetric --cipher-algo AES256 \
    --output "$BACKUP_DIR/$CLIENT_NAME-backup-$DATE.tar.gz.gpg" \
    "$BACKUP_DIR/$CLIENT_NAME-backup-$DATE.tar.gz"

# Cleanup
rm -rf "$BACKUP_DIR/$CLIENT_NAME"
rm "$BACKUP_DIR/$CLIENT_NAME-backup-$DATE.tar.gz"

echo "Client backup completed: $CLIENT_NAME-backup-$DATE.tar.gz.gpg"
```

---

## Recovery Procedures

### 1. Full Vault Recovery

```bash
#!/bin/bash
# vault-recovery.sh

BACKUP_FILE="$1"
VAULT_DATA_DIR="/vault/data"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file.tar.gz.gpg>"
    exit 1
fi

echo "Starting Vault recovery from: $BACKUP_FILE"

# Stop Vault service
systemctl stop vault

# Backup current data (just in case)
mv $VAULT_DATA_DIR $VAULT_DATA_DIR.backup.$(date +%Y%m%d_%H%M%S)

# Decrypt backup
gpg --decrypt "$BACKUP_FILE" > /tmp/vault-recovery.tar.gz

# Extract backup
mkdir -p $VAULT_DATA_DIR
tar -xzf /tmp/vault-recovery.tar.gz -C /vault/

# Set correct permissions
chown -R vault:vault /vault/
chmod -R 750 /vault/

# Start Vault service
systemctl start vault

# Wait for Vault to start
sleep 10

# Check Vault status
vault status

echo "Recovery completed. Please unseal Vault manually."
echo "Remember to verify all secrets and configurations."

# Cleanup
rm /tmp/vault-recovery.tar.gz
```

### 2. Snapshot Recovery

```bash
#!/bin/bash
# vault-snapshot-recovery.sh

SNAPSHOT_FILE="$1"

if [ -z "$SNAPSHOT_FILE" ]; then
    echo "Usage: $0 <snapshot-file.snap.gpg>"
    exit 1
fi

echo "Starting Vault recovery from snapshot: $SNAPSHOT_FILE"

# Decrypt snapshot
gpg --decrypt "$SNAPSHOT_FILE" > /tmp/vault-recovery.snap

# Restore from snapshot
vault operator raft snapshot restore /tmp/vault-recovery.snap

if [ $? -eq 0 ]; then
    echo "Snapshot recovery completed successfully"
    echo "Vault will restart automatically"
else
    echo "Snapshot recovery failed"
    exit 1
fi

# Cleanup
rm /tmp/vault-recovery.snap
```

### 3. Client-Specific Recovery

```bash
#!/bin/bash
# client-recovery.sh

CLIENT_NAME="$1"
BACKUP_FILE="$2"

if [ -z "$CLIENT_NAME" ] || [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <client-name> <backup-file.tar.gz.gpg>"
    exit 1
fi

echo "Recovering client: $CLIENT_NAME from $BACKUP_FILE"

# Decrypt and extract backup
gpg --decrypt "$BACKUP_FILE" > /tmp/client-recovery.tar.gz
tar -xzf /tmp/client-recovery.tar.gz -C /tmp/

# Restore secrets
if [ -f "/tmp/$CLIENT_NAME/secrets-*.json" ]; then
    SECRETS_FILE=$(ls /tmp/$CLIENT_NAME/secrets-*.json | head -1)
    vault kv put secret/$CLIENT_NAME/yendorcats/app-secrets \
        @"$SECRETS_FILE"
    echo "✓ Restored secrets for $CLIENT_NAME"
fi

# Restore policy
if [ -f "/tmp/$CLIENT_NAME/policy-*.hcl" ]; then
    POLICY_FILE=$(ls /tmp/$CLIENT_NAME/policy-*.hcl | head -1)
    vault policy write ${CLIENT_NAME}-policy "$POLICY_FILE"
    echo "✓ Restored policy for $CLIENT_NAME"
fi

# Restore AppRole
if [ -f "/tmp/$CLIENT_NAME/approle-*.json" ]; then
    APPROLE_FILE=$(ls /tmp/$CLIENT_NAME/approle-*.json | head -1)
    # Parse and recreate AppRole from JSON
    # This requires manual intervention as AppRole creation is complex
    echo "⚠ AppRole configuration found. Manual recreation required."
    echo "   File: $APPROLE_FILE"
fi

# Cleanup
rm -rf /tmp/$CLIENT_NAME
rm /tmp/client-recovery.tar.gz

echo "Client recovery completed for $CLIENT_NAME"
```

---

## Automated Backup Scheduling

### 1. Comprehensive Backup Cron Jobs

```bash
# Edit crontab: crontab -e

# Daily full backup at 2 AM
0 2 * * * /secure/scripts/vault-backup.sh >> /var/log/vault-backup.log 2>&1

# Hourly incremental backup during business hours
0 9-17 * * 1-5 /secure/scripts/vault-incremental-backup.sh >> /var/log/vault-backup.log 2>&1

# Weekly configuration backup on Sunday at 3 AM
0 3 * * 0 /secure/scripts/vault-config-backup.sh >> /var/log/vault-backup.log 2>&1

# Monthly snapshot backup on 1st at 4 AM
0 4 1 * * /secure/scripts/vault-snapshot-backup.sh >> /var/log/vault-backup.log 2>&1

# Daily cleanup of old backups at 5 AM
0 5 * * * find /secure/vault-backups -name "*.gpg" -mtime +30 -delete
```

### 2. Systemd Timer for Backups

```ini
# /etc/systemd/system/vault-backup.timer
[Unit]
Description=Vault Backup Timer
Requires=vault-backup.service

[Timer]
OnCalendar=daily
Persistent=true
RandomizedDelaySec=300

[Install]
WantedBy=timers.target
```

```ini
# /etc/systemd/system/vault-backup.service
[Unit]
Description=Vault Backup Service
After=network.target

[Service]
Type=oneshot
ExecStart=/secure/scripts/vault-backup.sh
User=vault
Group=vault
StandardOutput=journal
StandardError=journal
```

```bash
# Enable and start timer
systemctl enable vault-backup.timer
systemctl start vault-backup.timer

# Check timer status
systemctl list-timers vault-backup.timer
```

---

## Backup Verification and Testing

### 1. Backup Integrity Check

```bash
#!/bin/bash
# verify-backup.sh

BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file.tar.gz.gpg>"
    exit 1
fi

echo "Verifying backup: $BACKUP_FILE"

# Check if file exists and is readable
if [ ! -r "$BACKUP_FILE" ]; then
    echo "❌ Backup file not readable: $BACKUP_FILE"
    exit 1
fi

# Test decryption
if gpg --decrypt "$BACKUP_FILE" > /tmp/test-decrypt.tar.gz 2>/dev/null; then
    echo "✓ Decryption successful"
    
    # Test archive integrity
    if tar -tzf /tmp/test-decrypt.tar.gz >/dev/null 2>&1; then
        echo "✓ Archive integrity verified"
        
        # List contents
        echo "Backup contents:"
        tar -tzf /tmp/test-decrypt.tar.gz | head -10
        
        FILE_COUNT=$(tar -tzf /tmp/test-decrypt.tar.gz | wc -l)
        echo "Total files: $FILE_COUNT"
        
    else
        echo "❌ Archive corrupted"
        exit 1
    fi
    
    rm /tmp/test-decrypt.tar.gz
else
    echo "❌ Decryption failed"
    exit 1
fi

echo "✓ Backup verification completed successfully"
```

### 2. Recovery Testing

```bash
#!/bin/bash
# test-recovery.sh

BACKUP_FILE="$1"
TEST_DIR="/tmp/vault-recovery-test"

echo "Testing recovery from: $BACKUP_FILE"

# Create test environment
mkdir -p $TEST_DIR

# Decrypt and extract
gpg --decrypt "$BACKUP_FILE" > $TEST_DIR/backup.tar.gz
tar -xzf $TEST_DIR/backup.tar.gz -C $TEST_DIR/

# Verify critical files exist
CRITICAL_FILES=(
    "data/core/_keyring"
    "data/core/_master"
    "data/logical"
)

for file in "${CRITICAL_FILES[@]}"; do
    if [ -f "$TEST_DIR/$file" ] || [ -d "$TEST_DIR/$file" ]; then
        echo "✓ Found: $file"
    else
        echo "❌ Missing: $file"
    fi
done

# Cleanup
rm -rf $TEST_DIR

echo "Recovery test completed"
```

---

## Disaster Recovery Plan

### 1. Emergency Recovery Checklist

```markdown
# Vault Disaster Recovery Checklist

## Immediate Actions (0-1 hour)
- [ ] Assess the scope of the disaster
- [ ] Notify stakeholders and clients
- [ ] Secure the environment
- [ ] Locate latest backup files
- [ ] Verify backup integrity

## Recovery Actions (1-4 hours)
- [ ] Prepare new Vault server if needed
- [ ] Restore from latest backup
- [ ] Initialize and unseal Vault
- [ ] Verify all secrets are accessible
- [ ] Test client applications

## Post-Recovery Actions (4-24 hours)
- [ ] Update DNS/load balancer settings
- [ ] Notify clients of service restoration
- [ ] Conduct post-incident review
- [ ] Update backup procedures if needed
- [ ] Document lessons learned

## Emergency Contacts
- Vault Administrator: [phone/email]
- Infrastructure Team: [phone/email]
- Client Support: [phone/email]
```

### 2. Recovery Time Objectives

| Component | RTO (Recovery Time Objective) | RPO (Recovery Point Objective) |
|-----------|-------------------------------|--------------------------------|
| Vault Service | 2 hours | 1 hour |
| Client Secrets | 1 hour | 15 minutes |
| Configuration | 30 minutes | 24 hours |
| Audit Logs | 4 hours | 1 hour |

---

## Best Practices Summary

### Backup Security
- ✅ Always encrypt backups with strong encryption
- ✅ Store backups in multiple locations
- ✅ Test backup decryption regularly
- ✅ Limit access to backup files
- ✅ Use separate encryption keys for different backup types

### Backup Strategy
- ✅ Implement multiple backup types (full, incremental, snapshot)
- ✅ Automate backup processes
- ✅ Monitor backup success/failure
- ✅ Maintain proper retention policies
- ✅ Document recovery procedures

### Recovery Preparedness
- ✅ Test recovery procedures regularly
- ✅ Maintain updated disaster recovery documentation
- ✅ Train team members on recovery processes
- ✅ Keep emergency contact information current
- ✅ Review and update procedures quarterly

---
