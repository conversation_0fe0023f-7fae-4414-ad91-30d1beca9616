---

# Vault Secret Rotation and Automation

## Overview

Automated secret rotation is crucial for maintaining security in production environments. This guide covers implementing rotation for database credentials, API keys, and JWT secrets in your YendorCats project.

## Tags
#vault #rotation #automation #database #api-keys #jwt #security #cron #scripts

---

## Why Secret Rotation Matters

- **Security**: Limits exposure window if secrets are compromised
- **Compliance**: Many standards require regular credential rotation
- **Best Practice**: Reduces risk of long-lived credential abuse
- **Automation**: Eliminates manual rotation errors

---

## Database Secret Rotation

### 1. MySQL User Rotation Strategy

```sql
-- Create rotation users in MySQL
CREATE USER 'yendorcats_active'@'%' IDENTIFIED BY 'current_password';
CREATE USER 'yendorcats_standby'@'%' IDENTIFIED BY 'new_password';

-- Grant permissions to both users
GRANT ALL PRIVILEGES ON YendorCats.* TO 'yendorcats_active'@'%';
GRANT ALL PRIVILEGES ON YendorCats.* TO 'yendorcats_standby'@'%';
FLUSH PRIVILEGES;
```

### 2. Database Rotation Script

```bash
#!/bin/bash
# db-rotation.sh

VAULT_ADDR="https://localhost:8200"
MYSQL_HOST="your-mysql-host"
MYSQL_ROOT_USER="root"
MYSQL_ROOT_PASS="root_password"

# Generate new password
NEW_PASSWORD=$(openssl rand -base64 32)

# Current active user
CURRENT_USER="yendorcats_active"
STANDBY_USER="yendorcats_standby"

# Update standby user password
mysql -h $MYSQL_HOST -u $MYSQL_ROOT_USER -p$MYSQL_ROOT_PASS << EOF
ALTER USER '$STANDBY_USER'@'%' IDENTIFIED BY '$NEW_PASSWORD';
FLUSH PRIVILEGES;
EOF

if [ $? -eq 0 ]; then
    echo "Updated standby user password"
    
    # Test new connection
    NEW_CONNECTION="Server=$MYSQL_HOST;Database=YendorCats;User=$STANDBY_USER;Password=$NEW_PASSWORD;Port=3306;"
    
    # Update Vault with new connection string
    vault kv patch secret/yendorcats/app-secrets \
        DbConnectionString="$NEW_CONNECTION"
    
    if [ $? -eq 0 ]; then
        echo "Updated Vault with new connection string"
        
        # Wait for application to pick up new credentials
        sleep 30
        
        # Swap users (standby becomes active)
        # This would require updating your rotation logic
        echo "Database rotation completed successfully"
    else
        echo "Failed to update Vault"
        exit 1
    fi
else
    echo "Failed to update database password"
    exit 1
fi
```

### 3. Dynamic Database Secrets (Advanced)

```bash
# Enable database secrets engine
vault secrets enable database

# Configure MySQL connection
vault write database/config/mysql \
    plugin_name=mysql-database-plugin \
    connection_url="{{username}}:{{password}}@tcp(localhost:3306)/" \
    allowed_roles="yendorcats-role" \
    username="vault" \
    password="vault-password"

# Create role for dynamic credentials
vault write database/roles/yendorcats-role \
    db_name=mysql \
    creation_statements="CREATE USER '{{name}}'@'%' IDENTIFIED BY '{{password}}';GRANT ALL ON YendorCats.* TO '{{name}}'@'%';" \
    default_ttl="1h" \
    max_ttl="24h"
```

---

## JWT Secret Rotation

### 1. JWT Rotation Script

```bash
#!/bin/bash
# jwt-rotation.sh

VAULT_ADDR="https://localhost:8200"

# Generate new JWT secret (256-bit key)
NEW_JWT_SECRET=$(openssl rand -base64 64)

echo "Generated new JWT secret"

# Get current secrets
CURRENT_SECRETS=$(vault kv get -format=json secret/yendorcats/app-secrets)

# Update only JWT secret, keep other values
vault kv patch secret/yendorcats/app-secrets \
    JwtSecret="$NEW_JWT_SECRET"

if [ $? -eq 0 ]; then
    echo "JWT secret rotated successfully"
    
    # Notify application to refresh secrets
    # This could be a webhook, API call, or restart signal
    curl -X POST http://localhost:5000/api/admin/refresh-secrets \
         -H "Authorization: Bearer $ADMIN_TOKEN"
    
    echo "Application notified of secret rotation"
else
    echo "Failed to rotate JWT secret"
    exit 1
fi
```

### 2. Graceful JWT Rotation

To avoid invalidating existing tokens immediately, implement a grace period:

<augment_code_snippet path="backend/YendorCats.API/Services/AuthService.cs" mode="EXCERPT">
```csharp
// Support multiple JWT secrets during rotation
private async Task<bool> ValidateTokenWithMultipleKeys(string token)
{
    var secrets = await _secretsManager.GetAppSecretsAsync();
    
    // Try current secret first
    if (ValidateTokenWithKey(token, secrets.JwtSecret))
        return true;
    
    // Try previous secret (during rotation period)
    var previousSecret = await _secretsManager.GetSecretAsync("jwt-previous");
    if (!string.IsNullOrEmpty(previousSecret) && ValidateTokenWithKey(token, previousSecret))
        return true;
    
    return false;
}
```
</augment_code_snippet>

---

## API Key Rotation

### 1. Backblaze B2 Key Rotation

```bash
#!/bin/bash
# b2-rotation.sh

VAULT_ADDR="https://localhost:8200"
B2_ACCOUNT_ID="your-account-id"
B2_MASTER_KEY="your-master-application-key"

# Create new application key via B2 API
NEW_KEY_RESPONSE=$(curl -s -X POST https://api.backblazeb2.com/b2api/v2/b2_create_key \
    -H "Authorization: Basic $(echo -n "$B2_ACCOUNT_ID:$B2_MASTER_KEY" | base64)" \
    -H "Content-Type: application/json" \
    -d '{
        "accountId": "'$B2_ACCOUNT_ID'",
        "capabilities": ["listBuckets", "listFiles", "readFiles", "shareFiles", "writeFiles", "deleteFiles"],
        "keyName": "yendorcats-api-'$(date +%Y%m%d)'",
        "validDurationInSeconds": 7776000
    }')

NEW_KEY_ID=$(echo $NEW_KEY_RESPONSE | jq -r '.applicationKeyId')
NEW_KEY_SECRET=$(echo $NEW_KEY_RESPONSE | jq -r '.applicationKey')

if [ "$NEW_KEY_ID" != "null" ] && [ "$NEW_KEY_SECRET" != "null" ]; then
    echo "Created new B2 application key: $NEW_KEY_ID"
    
    # Update Vault with new keys
    vault kv patch secret/yendorcats/app-secrets \
        S3AccessKey="$NEW_KEY_ID" \
        S3SecretKey="$NEW_KEY_SECRET"
    
    if [ $? -eq 0 ]; then
        echo "Updated Vault with new B2 keys"
        
        # Test new keys work
        sleep 10
        
        # Delete old key (you'd need to track the old key ID)
        # curl -X POST https://api.backblazeb2.com/b2api/v2/b2_delete_key ...
        
        echo "B2 key rotation completed"
    else
        echo "Failed to update Vault with new B2 keys"
        exit 1
    fi
else
    echo "Failed to create new B2 application key"
    exit 1
fi
```

---

## Automated Rotation Scheduling

### 1. Cron Job Setup

```bash
# Edit crontab
crontab -e

# Database rotation - monthly on 1st at 2 AM
0 2 1 * * /secure/scripts/db-rotation.sh >> /var/log/vault-rotation.log 2>&1

# JWT rotation - weekly on Sunday at 3 AM  
0 3 * * 0 /secure/scripts/jwt-rotation.sh >> /var/log/vault-rotation.log 2>&1

# API key rotation - quarterly (every 3 months)
0 4 1 */3 * /secure/scripts/b2-rotation.sh >> /var/log/vault-rotation.log 2>&1

# Token renewal - daily at 1 AM
0 1 * * * /secure/scripts/vault-token-renewal.sh >> /var/log/vault-rotation.log 2>&1
```

### 2. Systemd Timer (Alternative to Cron)

```ini
# /etc/systemd/system/vault-rotation.timer
[Unit]
Description=Vault Secret Rotation Timer
Requires=vault-rotation.service

[Timer]
OnCalendar=weekly
Persistent=true

[Install]
WantedBy=timers.target
```

```ini
# /etc/systemd/system/vault-rotation.service
[Unit]
Description=Vault Secret Rotation Service
After=network.target

[Service]
Type=oneshot
ExecStart=/secure/scripts/rotation-master.sh
User=vault
Group=vault
```

---

## Application Integration

### 1. Secret Refresh Endpoint

<augment_code_snippet path="backend/YendorCats.API/Controllers/AdminController.cs" mode="EXCERPT">
```csharp
[HttpPost("refresh-secrets")]
[Authorize(Roles = "Admin")]
public async Task<IActionResult> RefreshSecrets()
{
    try
    {
        // Clear cached secrets to force reload
        await _secretsManager.ClearCacheAsync();
        
        // Optionally restart specific services
        await _serviceManager.RestartServicesAsync();
        
        return Ok(new { message = "Secrets refreshed successfully" });
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to refresh secrets");
        return StatusCode(500, new { error = "Failed to refresh secrets" });
    }
}
```
</augment_code_snippet>

### 2. Health Check for Secret Validity

```csharp
public class SecretHealthCheck : IHealthCheck
{
    private readonly ISecretsManagerService _secretsManager;
    
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var secrets = await _secretsManager.GetAppSecretsAsync();
            
            // Validate secrets are not empty
            if (string.IsNullOrEmpty(secrets.DbConnectionString) || 
                string.IsNullOrEmpty(secrets.JwtSecret))
            {
                return HealthCheckResult.Unhealthy("Critical secrets are missing");
            }
            
            // Test database connection
            // Test S3 connection
            // etc.
            
            return HealthCheckResult.Healthy("All secrets are valid");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy("Failed to retrieve secrets", ex);
        }
    }
}
```

---

## Monitoring and Alerting

### 1. Rotation Success/Failure Notifications

```bash
#!/bin/bash
# notification-helper.sh

send_notification() {
    local message="$1"
    local status="$2"
    
    # Email notification
    echo "$message" | mail -s "Vault Rotation $status" <EMAIL>
    
    # Webhook notification (Slack, Discord, etc.)
    curl -X POST https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK \
         -H 'Content-type: application/json' \
         --data "{\"text\":\"$message\"}"
}

# Usage in rotation scripts
if [ $? -eq 0 ]; then
    send_notification "Database rotation completed successfully" "SUCCESS"
else
    send_notification "Database rotation failed" "FAILURE"
fi
```

### 2. Secret Age Monitoring

```bash
#!/bin/bash
# secret-age-check.sh

# Check when secrets were last updated
SECRET_METADATA=$(vault kv metadata secret/yendorcats/app-secrets)
LAST_UPDATED=$(echo "$SECRET_METADATA" | grep "updated_time" | awk '{print $2}')

# Calculate age in days
CURRENT_TIME=$(date +%s)
LAST_UPDATED_TIME=$(date -d "$LAST_UPDATED" +%s)
AGE_DAYS=$(( (CURRENT_TIME - LAST_UPDATED_TIME) / 86400 ))

# Alert if secrets are older than 90 days
if [ $AGE_DAYS -gt 90 ]; then
    echo "WARNING: Secrets are $AGE_DAYS days old"
    send_notification "Secrets are $AGE_DAYS days old - rotation recommended" "WARNING"
fi
```

---

## Best Practices

### Rotation Schedule Recommendations

- **Database Credentials**: Monthly or quarterly
- **JWT Secrets**: Weekly or bi-weekly  
- **API Keys**: Quarterly or when provider recommends
- **Vault Tokens**: Daily renewal, monthly recreation
- **TLS Certificates**: 30 days before expiry

### Safety Measures

- **Test First**: Always test new credentials before updating production
- **Gradual Rollout**: Update standby systems first
- **Rollback Plan**: Keep previous credentials available for emergency rollback
- **Monitoring**: Monitor application health during and after rotation
- **Documentation**: Log all rotation activities with timestamps

---
