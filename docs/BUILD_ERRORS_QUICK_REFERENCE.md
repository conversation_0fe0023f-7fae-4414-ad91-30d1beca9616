---
# Build Errors Quick Reference
**YendorCats S3 Performance Caching Project**

## Current Status
- **🔴 71 Compilation Errors**
- **⚠️ 149 Warnings** 
- **❌ Complete Build Failure**

---

## Quick Fix Checklist

### **Phase 1: Type System (CRITICAL)**
- [ ] Remove duplicate `S3ObjectInfo` from `S3ToDbMigrationService.cs` (lines 986-1000)
- [ ] Add `using YendorCats.API.Services.Compatibility;` to `S3ToDbMigrationService.cs`
- [ ] Fix `PhotoIndexService.cs:428` - change `Id = Guid.NewGuid().ToString()` to `Id = 0`
- [ ] Add to `StorageProviderConfiguration.cs`:
  ```csharp
  public string S3BucketName => S3Config.BucketName;
  public string B2BucketName => B2Config.BucketName;
  ```

### **Phase 2: Interfaces (HIGH)**
- [ ] Fix `GetTotalCountAsync` calls - use named parameters
- [ ] Fix LINQ casting - add `.OrderBy(x => x.Id)` before `IOrderedQueryable` assignments
- [ ] Fix Dictionary property access - use `.GetValueOrDefault("key", "default")`

### **Phase 3: Parameters (MEDIUM)**
- [ ] Replace `method(1, 0)` with `method(activeOnly: true, publicOnly: false)`
- [ ] Fix int/long conversion issues with explicit casting

### **Phase 4: Validation**
- [ ] Run `dotnet build` - should show 0 errors
- [ ] Test service instantiation
- [ ] Verify S3 integration works

---

## Critical Files to Edit

1. **`backend/YendorCats.API/Services/Migration/S3ToDbMigrationService.cs`**
   - Remove duplicate type definitions
   - Fix configuration property access

2. **`backend/YendorCats.API/Services/PhotoIndexService.cs`**
   - Fix ID type mismatch on line 428

3. **`backend/YendorCats.API/Configuration/StorageProviderConfiguration.cs`**
   - Add missing bucket name properties

4. **`backend/YendorCats.API/Data/Repositories/GalleryRepository.cs`**
   - Fix LINQ casting issues

5. **`backend/YendorCats.API/Services/Compatibility/S3CompatibilityService.cs`**
   - Fix string/S3ObjectInfo conflicts

---

## Common Error Patterns & Fixes

### **Pattern 1: Type Conflicts**
```csharp
// ❌ Error: 'string' does not contain definition for 'Key'
var key = stringObject.Key;

// ✅ Fix: Proper object creation
var s3Object = new S3ObjectInfo { Key = stringObject };
```

### **Pattern 2: Dictionary Access**
```csharp
// ❌ Error: Dictionary doesn't have ContentLength
var length = metadata.ContentLength;

// ✅ Fix: Dictionary value access
var length = long.Parse(metadata.GetValueOrDefault("content-length", "0"));
```

### **Pattern 3: LINQ Casting**
```csharp
// ❌ Error: Cannot convert IQueryable to IOrderedQueryable
IOrderedQueryable<T> ordered = query.Where(x => x.Active);

// ✅ Fix: Add explicit ordering
IOrderedQueryable<T> ordered = query.Where(x => x.Active).OrderBy(x => x.Id);
```

### **Pattern 4: Parameter Types**
```csharp
// ❌ Error: Cannot convert int to bool
await method(1, 0);

// ✅ Fix: Use correct types
await method(activeOnly: true, publicOnly: false);
```

---

## Build Test Commands

```bash
# Quick build test
dotnet build --verbosity minimal

# Full build with details
dotnet build --verbosity normal

# Clean and rebuild
dotnet clean && dotnet build
```

---

## Success Indicators

✅ **Build Success**: `dotnet build` completes with 0 errors  
✅ **Service Creation**: All services instantiate without exceptions  
✅ **S3 Integration**: Basic S3 operations work  
✅ **Migration Ready**: S3ToDbMigrationService can be created  

---

## Emergency Rollback

```bash
# If fixes break something
git stash  # Save current changes
git reset --hard HEAD  # Revert to last commit

# Or commit incrementally
git add .
git commit -m "Phase X: Description of changes"
```

---

## Key Resources

- **Detailed Guide**: `/docs/BUILD_ERROR_RESOLUTION_GUIDE.md`
- **Task List**: Use task management tools to track progress
- **Architecture Docs**: `/docs/BACKEND_INFRASTRUCTURE_DOCUMENTATION.md`

---

## Tags
#quick-reference #build-errors #s3-performance #troubleshooting #checklist

---
